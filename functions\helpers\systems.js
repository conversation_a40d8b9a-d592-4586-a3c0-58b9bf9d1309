const SYSTEMS = {
  bigquery: {
    label: "BigQuery",
    color: "purple",
    fields: [
      { id: "projectId", label: "Project ID", defaultValue: "hj-reporting", rules: [{ required: true, message: "Please enter project ID" }] },
      { id: "datasetId", label: "Dataset ID", rules: [{ required: true, message: "Please enter dataset ID" }] },
      { id: "tableId", label: "Table ID", rules: [{ required: true, message: "Please enter table ID" }] },
    ],
  },
  netsuite: {
    label: "Netsuite",
    color: "blue",
    connectionKeys: [
      "realm",
      "consumerKey",
      "consumerSecret",
      "tokenKey",
      "tokenSecret",
    ],
    datasetFields: [
      "datasetId",
      "tableId",
      "description",
      "searchId",
      "refreshInterval",
    ],
  },
  shopify: {
    label: "Shopify",
    color: "green",
    connectionKeys: [
      "storeName",
      "apiKey",
    ],
  },
  amazon: {
    label: "Amazon",
    color: "red",
    connectionKeys: [
      "accessKeyId",
      "secretAccessKey",
      "region",
    ],
    datasetFields: [
      "datasetId",
      "tableId",
      "description",
      "reportId",
      "lookback",
      "refreshInterval",
      "lastRefreshed",
      "status",
    ],
  },
};

module.exports = { SYSTEMS }; 