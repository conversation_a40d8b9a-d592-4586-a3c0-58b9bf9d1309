import React, { useState, useEffect } from 'react';
import { Box, Typography, Grid, Paper, CircularProgress, Alert, LinearProgress } from '@mui/material';
import {
    ComposedChart, Line, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import { useFormatAxisTick } from '../../hooks/useFormatAxisTick';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import ChartExportWrapper from './ChartExportWrapper';

const OrderAccuracyRate = () => {
    const [accuracyData, setAccuracyData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const formatAxisTick = useFormatAxisTick();

    // Function to format numbers without currency
    const formatNumber = (value) => {
        return new Intl.NumberFormat('en-US').format(value);
    };

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const functions = getFunctions();
            const getOrderAccuracyRate = httpsCallable(functions, 'getOrderAccuracyRate');
            const getKPIGoalsForReport = httpsCallable(functions, 'getKPIGoalsForReport');

            const [accuracyResult, goalsResult] = await Promise.all([
                getOrderAccuracyRate(),
                getKPIGoalsForReport({ reportName: 'order-accuracy' })
            ]);

            setAccuracyData(accuracyResult.data);

            if (goalsResult.data.success) {
                setKpiGoals(goalsResult.data.data);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            setError('Failed to fetch data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    const calculateAccuracyTrend = () => {
        if (!accuracyData?.monthlyData || accuracyData.monthlyData.length < 2) return 0;
        const lastTwo = accuracyData.monthlyData.slice(-2);
        return parseFloat(lastTwo[1].accuracyRate) - parseFloat(lastTwo[0].accuracyRate);
    };

    const calculateDomains = () => {
        if (!accuracyData?.monthlyData) return { rateDomain: [0, 100], ordersDomain: [0, 1000] };

        const orderValues = accuracyData.monthlyData.map(item =>
            Math.max(item.totalOrders || 0, item.accurateOrders || 0, item.inaccurateOrders || 0)
        );

        const goalValue = kpiGoals?.['Order Accuracy Rate']?.value;
        const maxRate = Math.max(...accuracyData.monthlyData.map(item => parseFloat(item.accuracyRate)), goalValue ? parseFloat(goalValue) : 0);

        return {
            rateDomain: [95, Math.min(Math.ceil(maxRate * 1.1), 100)], // Start from 95% for better visualization
            ordersDomain: [0, Math.ceil(Math.max(...orderValues) * 1.1)]
        };
    };

    const formatAccuracyRate = (rate) => {
        const numRate = parseFloat(rate);
        if (numRate < 100) {
            return Math.min(numRate, 99.99).toFixed(2);
        }
        return numRate === 100 ? "100.00" : "99.99";
    };

    // Then update the CustomTooltip
    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                    <Typography variant="body2">{`Month: ${label}`}</Typography>
                    <Typography variant="body2" color="#2196f3">
                        {`Accuracy Rate: ${formatAccuracyRate(data.accuracyRate)}%`}
                    </Typography>
                    <Typography variant="body2" color="#4caf50">
                        {`Accurate Orders: ${formatNumber(data.accurateOrders)}`}
                    </Typography>
                    <Typography variant="body2" color="#f44336">
                        {`Inaccurate Orders: ${formatNumber(data.inaccurateOrders)}`}
                    </Typography>
                    <Typography variant="body2" color="#9e9e9e">
                        {`Total Orders: ${formatNumber(data.totalOrders)}`}
                    </Typography>
                </Paper>
            );
        }
        return null;
    };

    const CustomYAxisTick = (props) => {
        const { x, y, payload } = props;
        return (
            <g transform={`translate(${x},${y})`}>
                <text x={0} y={0} dy={16} textAnchor="end" fill="#666">
                    {formatNumber(payload.value)}
                </text>
            </g>
        );
    };

    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: kpiGoals?.['Order Accuracy Rate'],
        yAxisId: "left",
        styles: {
            stroke: "#2196f3",
            strokeDasharray: "3 3",
            label: {
                fill: "#2196f3",
                fontSize: 12,
                position: "right"
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Min",
            max: "Max"
        }
    });

    if (isLoading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Alert severity="error" sx={{ mt: 2 }}>
                {error}
            </Alert>
        );
    }

    if (!accuracyData || !kpiGoals) return null;

    const { rateDomain, ordersDomain } = calculateDomains();
    const accuracyConfig = kpiGoals['Order Accuracy Rate'];

    // Calculate max issues for progress bars
    const maxIssueCount = Math.max(...accuracyData.qualityIssues.types.map(issue => issue.count));

    return (
        <ChartExportWrapper title="Order_Accuracy_Rate">
            <Box sx={{ p: { xs: 2, sm: 3 }, mb: 3 }}>
                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <ResponsiveContainer width="100%" height={400}>
                        <ComposedChart
                            data={accuracyData.monthlyData}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="monthYear" />
                            <YAxis
                                yAxisId="left"
                                domain={rateDomain}
                                label={{
                                    value: 'Accuracy Rate (%)',
                                    angle: -90,
                                    position: 'insideLeft'
                                }}
                            />
                            <YAxis
                                yAxisId="right"
                                orientation="right"
                                domain={ordersDomain}
                                label={{
                                    value: 'Number of Orders',
                                    angle: 90,
                                    position: 'insideRight'
                                }}
                                tickFormatter={formatNumber}
                                tick={<CustomYAxisTick />}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            {goalReferenceLines}
                            <Bar yAxisId="right" dataKey="totalOrders" fill="#9e9e9e" name="Total Orders" />
                            <Bar yAxisId="right" dataKey="accurateOrders" fill="#4caf50" name="Accurate Orders" />
                            <Bar yAxisId="right" dataKey="inaccurateOrders" fill="#f44336" name="Inaccurate Orders" />
                            <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="accuracyRate"
                                stroke="#2196f3"
                                strokeWidth={2}
                                dot={{ r: 4 }}
                                name="Accuracy Rate (%)"
                            />
                        </ComposedChart>
                    </ResponsiveContainer>

                    <Grid container spacing={2} sx={{ mt: 2 }}>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Overall Accuracy Rate"
                                value={`${formatAccuracyRate(accuracyData.overallAccuracyRate)}%`}
                                bgColor="#e3f2fd"
                                textColor="#2196f3"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Accurate Orders"
                                value={formatNumber(accuracyData.summary.accurateOrders)}
                                bgColor="#e8f5e9"
                                textColor="#4caf50"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Inaccurate Orders"
                                value={formatNumber(accuracyData.summary.inaccurateOrders)}
                                bgColor="#ffebee"
                                textColor="#f44336"
                            />
                        </Grid>
                    </Grid>
                </Paper>

                <GoalStatusDisplay
                    currentValue={parseFloat(formatAccuracyRate(accuracyData.overallAccuracyRate))}
                    goalConfig={accuracyConfig}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculateAccuracyTrend()}
                    title="Order Accuracy Performance"
                    size="medium"
                />

                {accuracyData.qualityIssues?.types?.length > 0 && (
                    <Paper elevation={3} sx={{ p: 3, mt: 3 }}>
                        <Typography variant="h6" gutterBottom>
                            Top Quality Issues
                        </Typography>
                        {accuracyData.qualityIssues.types.slice(0, 5).map((issue, index) => (
                            <Box key={issue.type} sx={{ mb: 3 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                                        {`${index + 1}. ${issue.type}`}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        {`${issue.count} ${issue.count === 1 ? 'occurrence' : 'occurrences'}`}
                                    </Typography>
                                </Box>
                                <LinearProgress
                                    variant="determinate"
                                    value={(issue.count / maxIssueCount) * 100}
                                    sx={{
                                        height: 8,
                                        borderRadius: 4,
                                        backgroundColor: '#f5f5f5',
                                        '& .MuiLinearProgress-bar': {
                                            backgroundColor: index === 0 ? '#f44336' :
                                                index === 1 ? '#ff9800' :
                                                    index === 2 ? '#ffc107' :
                                                        index === 3 ? '#4caf50' : '#2196f3'
                                        }
                                    }}
                                />
                            </Box>
                        ))}
                    </Paper>
                )}
            </Box>
        </ChartExportWrapper>
    );
};

export default OrderAccuracyRate;