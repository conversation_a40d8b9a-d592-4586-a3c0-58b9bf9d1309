import { useState, useEffect } from 'react';

export const useGoalEvaluation = (currentValue, goalConfig) => {
  const [status, setStatus] = useState({
    score: 0,
    status: 'neutral',
    message: '',
    trend: null
  });

  useEffect(() => {
    if (!currentValue || !goalConfig) return;

    const calculateScore = () => {
      const { goalCondition, value, min, max, unit } = goalConfig;
      let score = 0;
      let status = 'neutral';
      let message = '';

      // Helper function to ensure score is between 0 and 100
      const normalizeScore = (rawScore) => Math.min(Math.max(rawScore, 0), 100);

      // Handle range-based goals
      if (goalConfig.hasOwnProperty('min') && goalConfig.hasOwnProperty('max')) {
        const range = parseFloat(max) - parseFloat(min);
        
        switch (goalCondition) {
          case 'higherThanMin':
            score = ((currentValue - min) / (max - min)) * 100;
            if (currentValue >= max) score = 100;
            else if (currentValue < min) score = Math.max((currentValue / min) * 75, 0);
            status = currentValue >= min ? 'success' : 'warning';
            message = `Target: ≥${min}${unit}`;
            break;

          case 'lowerThanMax':
            score = ((max - currentValue) / (max - min)) * 100;
            if (currentValue <= min) score = 100;
            else if (currentValue > max) score = Math.max(((2 * max - currentValue) / max) * 75, 0);
            status = currentValue <= max ? 'success' : 'warning';
            message = `Target: ≤${max}${unit}`;
            break;

          case 'inRange':
            // New scoring logic for in-range goals
            if (currentValue >= min && currentValue <= max) {
              // If within range, score should be high (80-100)
              // The closer to the midpoint, the closer to 100
              const midPoint = (parseFloat(min) + parseFloat(max)) / 2;
              const deviation = Math.abs(currentValue - midPoint);
              const maxDeviation = range / 2;
              // Base score of 80 for being in range, up to 100 for being at midpoint
              score = 80 + (20 * (1 - deviation / maxDeviation));
            } else {
              // If outside range, score based on distance to nearest boundary
              const distanceToMin = Math.abs(currentValue - min);
              const distanceToMax = Math.abs(currentValue - max);
              const distanceToNearest = Math.min(distanceToMin, distanceToMax);
              // Score decreases as distance from range increases
              score = Math.max(0, 70 * (1 - distanceToNearest / range));
            }
            status = currentValue >= min && currentValue <= max ? 'success' : 'warning';
            message = `Target: ${min}-${max}${unit}`;
            break;

          default:
            message = `Target: ${min}-${max}${unit}`;
        }
      } else {
        // Handle single value goals (remaining logic unchanged)
        const targetValue = parseFloat(value);
        
        switch (goalCondition) {
          case 'higher':
            score = (currentValue / targetValue) * 100;
            status = currentValue >= targetValue ? 'success' : 'warning';
            message = `Target: >${targetValue}${unit}`;
            break;

          case 'lower':
            if (currentValue <= targetValue) {
              const improvement = targetValue - currentValue;
              const baseScore = 80;
              const bonusScore = Math.min(20, (improvement / targetValue) * 20);
              score = baseScore + bonusScore;
              status = 'success';
            } else {
              const deviation = currentValue - targetValue;
              score = Math.max(0, 80 - (deviation / targetValue) * 80);
              status = 'warning';
            }
            message = `Target: <${targetValue}${unit}`;
            break;

          default:
            score = 0;
            status = 'neutral';
            message = 'No target set';
        }
      }

      // Normalize score to 0-100 range
      score = normalizeScore(score);

      // Calculate performance level
      let performanceLevel = 'poor';
      if (score >= 90) performanceLevel = 'excellent';
      else if (score >= 75) performanceLevel = 'good';
      else if (score >= 60) performanceLevel = 'fair';

      return {
        score,
        status,
        message,
        performanceLevel,
        currentValue
      };
    };

    setStatus(calculateScore());
  }, [currentValue, goalConfig]);

  return status;
};

export default useGoalEvaluation;