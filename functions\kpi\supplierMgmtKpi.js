/* eslint-disable require-jsdoc */
const {onCall} = require("firebase-functions/v2/https");
const logger = require("firebase-functions/logger");
const {executeNSSuiteQLQuery} = require("../helpers/netsuite");
const admin = require("firebase-admin");
const {runQuery} = require("../helpers/bigQuery");

exports.getOnTimeDeliveryV2 = onCall(async (data, context) => {
  try {
    logger.info("Fetching on-time delivery data...");
    const lateBenchmark = data.data.lateBenchmark !== undefined ? data.data.lateBenchmark : 30;
    logger.info(`Late benchmark set to: ${lateBenchmark} days`);

    const query = `
      SELECT 
        vendor.entityid AS name,
        po.tranid AS ns_po,
        item.itemid AS item,
        pol.quantity AS total_quantity_ordered,
        pol.quantityshiprecv AS shipped_quantity,
        pol.expectedreceiptdate AS supplier_actual_load_date,
        pol.custcol_forecast_date AS hj_expected_load_date,
        (pol.custcol_forecast_date - pol.expectedreceiptdate) AS on_time_delivery_days,
        po.trandate AS po_date,
        vendor.entityid
      FROM 
        transaction AS po
      JOIN 
        entity AS vendor ON po.entity = vendor.id
      JOIN 
        transactionline AS pol ON po.id = pol.transaction
      JOIN 
        item ON pol.item = item.id
      WHERE 
        po.type = 'PurchOrd'
        AND po.trandate BETWEEN ADD_MONTHS(CURRENT_DATE, -12) AND CURRENT_DATE
        AND vendor.entityid != 'Xiamen Yungcheng Import & Export Co'
      ORDER BY 
        po.trandate ASC
    `;

    const result = await executeNSSuiteQLQuery(query);

    if (!result || !result.items || !Array.isArray(result.items) || result.items.length === 0) {
      logger.warn("Empty or invalid items array in SuiteQL query response");
      return {error: "No data available"};
    }

    logger.info(`Fetched ${result.items.length} records`);

    const processedData = processOnTimeDeliveryData(result.items, lateBenchmark);

    logger.info("On-time delivery data processed successfully");
    // logger.info(`Total deliveries: ${processedData.totalDeliveries}, On-time deliveries: ${processedData.totalOnTimeDeliveries}`);
    // logger.info(`Overall OTD Score: ${processedData.overallOTDScore}%`);

    return processedData;
  } catch (error) {
    logger.error("Error fetching or processing on-time delivery data:", error);
    throw new Error(`Failed to calculate On-Time Delivery: ${error.message}`);
  }
});

/**
 * Process the on-time delivery data and calculate the OTD score for each month and supplier.
 * @param {Array} data - The raw data fetched from NetSuite
 * @param {number} lateBenchmark - The number of days after which a delivery is considered late
 * @return {Object} - The processed data with OTD scores for each month and supplier
 * @throws {Error} - If there is an error processing the data
 */
function processOnTimeDeliveryData(data, lateBenchmark) {
  const monthlyData = {};
  const supplierData = {};
  let totalDeliveries = 0;
  let totalOnTimeDeliveries = 0;

  logger.info(`Starting to process ${data.length} records with lateBenchmark: ${lateBenchmark}`);

  data.forEach((item, index) => {
    const actualDate = new Date(item.supplier_actual_load_date);
    const monthYear = `${actualDate.getFullYear()}-${(actualDate.getMonth() + 1).toString().padStart(2, "0")}`;
    const supplierName = item.name;
    const quantity = parseInt(item.shipped_quantity) || 0;
    const onTimeDeliveryDays = parseInt(item.on_time_delivery_days) || 0;

    if (!monthlyData[monthYear]) {
      monthlyData[monthYear] = {totalDeliveries: 0, onTimeDeliveries: 0, lateDeliveries: 0, otdScore: 0, suppliers: {}};
    }
    if (!supplierData[supplierName]) {
      supplierData[supplierName] = {totalDeliveries: 0, onTimeDeliveries: 0, lateDeliveries: 0, otdScore: 0, monthlyData: {}};
    }
    if (!monthlyData[monthYear].suppliers[supplierName]) {
      monthlyData[monthYear].suppliers[supplierName] = {totalDeliveries: 0, onTimeDeliveries: 0, lateDeliveries: 0, otdScore: 0};
    }
    if (!supplierData[supplierName].monthlyData[monthYear]) {
      supplierData[supplierName].monthlyData[monthYear] = {totalDeliveries: 0, onTimeDeliveries: 0, lateDeliveries: 0, otdScore: 0};
    }

    monthlyData[monthYear].totalDeliveries += quantity;
    monthlyData[monthYear].suppliers[supplierName].totalDeliveries += quantity;
    supplierData[supplierName].totalDeliveries += quantity;
    supplierData[supplierName].monthlyData[monthYear].totalDeliveries += quantity;
    totalDeliveries += quantity;

    const isOnTime = onTimeDeliveryDays >= -lateBenchmark;
    // logger.debug(`Item ${index + 1}: onTimeDeliveryDays=${onTimeDeliveryDays}, lateBenchmark=${lateBenchmark}, isOnTime=${isOnTime}, quantity=${quantity}`);

    if (isOnTime) {
      monthlyData[monthYear].onTimeDeliveries += quantity;
      monthlyData[monthYear].suppliers[supplierName].onTimeDeliveries += quantity;
      supplierData[supplierName].onTimeDeliveries += quantity;
      supplierData[supplierName].monthlyData[monthYear].onTimeDeliveries += quantity;
      totalOnTimeDeliveries += quantity;
    //   logger.debug(`Item ${index + 1}: Marked as on-time. Running totalOnTimeDeliveries: ${totalOnTimeDeliveries}`);
    } else {
      monthlyData[monthYear].lateDeliveries += quantity;
      monthlyData[monthYear].suppliers[supplierName].lateDeliveries += quantity;
      supplierData[supplierName].lateDeliveries += quantity;
      supplierData[supplierName].monthlyData[monthYear].lateDeliveries += quantity;
    //   logger.debug(`Item ${index + 1}: Marked as late. Running totalLateDeliveries: ${totalDeliveries - totalOnTimeDeliveries}`);
    }

    if (index % 100 === 0 || index === data.length - 1) {
    //   logger.info(`Processed ${index + 1} items. Current totalDeliveries: ${totalDeliveries}, totalOnTimeDeliveries: ${totalOnTimeDeliveries}`);
    }
  });

  logger.info(`Finished processing all items. Final totalDeliveries: ${totalDeliveries}, totalOnTimeDeliveries: ${totalOnTimeDeliveries}`);

  Object.keys(monthlyData).forEach((month) => {
    const monthly = monthlyData[month];
    monthly.otdScore = (monthly.onTimeDeliveries / monthly.totalDeliveries) * 100;
    Object.keys(monthly.suppliers).forEach((supplier) => {
      const supplierMonthly = monthly.suppliers[supplier];
      supplierMonthly.otdScore = (supplierMonthly.onTimeDeliveries / supplierMonthly.totalDeliveries) * 100;
    });
  });

  Object.keys(supplierData).forEach((supplier) => {
    const supplierInfo = supplierData[supplier];
    supplierInfo.otdScore = (supplierInfo.onTimeDeliveries / supplierInfo.totalDeliveries) * 100;
    Object.keys(supplierInfo.monthlyData).forEach((month) => {
      const monthlyInfo = supplierInfo.monthlyData[month];
      monthlyInfo.otdScore = (monthlyInfo.onTimeDeliveries / monthlyInfo.totalDeliveries) * 100;
    });
  });

  const overallOTDScore = (totalOnTimeDeliveries / totalDeliveries) * 100;

  logger.info(`Calculated overall OTD Score: ${overallOTDScore.toFixed(2)}%`);

  return {
    overallOTDScore: overallOTDScore.toFixed(2),
    monthlyData: Object.keys(monthlyData).map((month) => ({
      month,
      otdScore: monthlyData[month].otdScore.toFixed(2),
      onTimeDeliveries: monthlyData[month].onTimeDeliveries,
      lateDeliveries: monthlyData[month].lateDeliveries,
      totalDeliveries: monthlyData[month].totalDeliveries,
      suppliers: Object.keys(monthlyData[month].suppliers).map((supplier) => ({
        name: supplier,
        otdScore: monthlyData[month].suppliers[supplier].otdScore.toFixed(2),
        onTimeDeliveries: monthlyData[month].suppliers[supplier].onTimeDeliveries,
        lateDeliveries: monthlyData[month].suppliers[supplier].lateDeliveries,
        totalDeliveries: monthlyData[month].suppliers[supplier].totalDeliveries,
      })),
    })).sort((a, b) => a.month.localeCompare(b.month)),
    supplierPerformance: Object.keys(supplierData).map((supplier) => ({
      name: supplier,
      otdScore: supplierData[supplier].otdScore.toFixed(2),
      onTimeDeliveries: supplierData[supplier].onTimeDeliveries,
      lateDeliveries: supplierData[supplier].lateDeliveries,
      totalDeliveries: supplierData[supplier].totalDeliveries,
      monthlyData: Object.keys(supplierData[supplier].monthlyData).map((month) => ({
        month,
        otdScore: supplierData[supplier].monthlyData[month].otdScore.toFixed(2),
        onTimeDeliveries: supplierData[supplier].monthlyData[month].onTimeDeliveries,
        lateDeliveries: supplierData[supplier].monthlyData[month].lateDeliveries,
        totalDeliveries: supplierData[supplier].monthlyData[month].totalDeliveries,
      })).sort((a, b) => a.month.localeCompare(b.month)),
    })).sort((a, b) => b.otdScore - a.otdScore),
    totalDeliveries,
    totalOnTimeDeliveries,
    totalLateDeliveries: totalDeliveries - totalOnTimeDeliveries,
    lateBenchmark,
  };
}

exports.getProcurementCycleTime = onCall(async (data, context) => {
  try {
    logger.info("Fetching procurement cycle time data...");

    const query = `
      SELECT 
        vendor.entityid AS name,
        po.tranid AS ns_po,
        item.itemid AS item,
        pol.quantity AS total_quantity_ordered,
        pol.quantityshiprecv AS shipped_quantity,
        pol.expectedreceiptdate AS supplier_actual_load_date,
        pol.custcol_forecast_date AS hj_expected_load_date,
        ABS(pol.custcol_forecast_date - pol.expectedreceiptdate) AS cycle_time_days,
        po.trandate AS po_date,
        vendor.entityid
      FROM 
        transaction AS po
      JOIN 
        entity AS vendor ON po.entity = vendor.id
      JOIN 
        transactionline AS pol ON po.id = pol.transaction
      JOIN 
        item ON pol.item = item.id
      WHERE 
        po.type = 'PurchOrd'
        AND po.trandate BETWEEN ADD_MONTHS(CURRENT_DATE, -12) AND CURRENT_DATE
        AND vendor.entityid != 'Xiamen Yungcheng Import & Export Co'
      ORDER BY 
        po.trandate ASC
    `;

    const result = await executeNSSuiteQLQuery(query);

    if (!result || !result.items || !Array.isArray(result.items) || result.items.length === 0) {
      logger.warn("Empty or invalid items array in SuiteQL query response");
      return {error: "No data available"};
    }

    logger.info(`Fetched ${result.items.length} records`);

    const processedData = processProcurementCycleTimeData(result.items);

    logger.info("Procurement cycle time data processed successfully");
    return processedData;
  } catch (error) {
    logger.error("Error fetching or processing procurement cycle time data:", error);
    throw new Error(`Failed to calculate Procurement Cycle Time: ${error.message}`);
  }
});

/**
 * Process the procurement cycle time data and calculate the average cycle time for each month and supplier.
 * @param {Array} data - The raw data fetched from NetSuite
 * @return {Object} - The processed data with average cycle time for each month and supplier
 * @throws {Error} - If there is an error processing the data
    */
function processProcurementCycleTimeData(data) {
  const monthlyData = {};
  const supplierData = {};
  let totalCycleTimeDays = 0;
  let totalOrders = 0;

  data.forEach((item, index) => {
    const poDate = new Date(item.po_date);
    const monthYear = `${poDate.getFullYear()}-${(poDate.getMonth() + 1).toString().padStart(2, "0")}`;
    const supplierName = item.name;
    const cycleTimeDays = parseInt(item.cycle_time_days) || 0;
    const quantity = parseInt(item.shipped_quantity) || 0;

    if (!monthlyData[monthYear]) {
      monthlyData[monthYear] = {
        totalCycleTimeDays: 0,
        orderCount: 0,
        itemCount: 0,
        averageCycleTime: 0,
        suppliers: {},
      };
    }
    if (!supplierData[supplierName]) {
      supplierData[supplierName] = {
        totalCycleTimeDays: 0,
        orderCount: 0,
        itemCount: 0,
        averageCycleTime: 0,
        monthlyData: {},
      };
    }
    if (!monthlyData[monthYear].suppliers[supplierName]) {
      monthlyData[monthYear].suppliers[supplierName] = {
        totalCycleTimeDays: 0,
        orderCount: 0,
        itemCount: 0,
        averageCycleTime: 0,
      };
    }
    if (!supplierData[supplierName].monthlyData[monthYear]) {
      supplierData[supplierName].monthlyData[monthYear] = {
        totalCycleTimeDays: 0,
        orderCount: 0,
        itemCount: 0,
        averageCycleTime: 0,
      };
    }

    monthlyData[monthYear].totalCycleTimeDays += cycleTimeDays * quantity;
    monthlyData[monthYear].orderCount += quantity;
    monthlyData[monthYear].itemCount += quantity;
    monthlyData[monthYear].suppliers[supplierName].totalCycleTimeDays += cycleTimeDays * quantity;
    monthlyData[monthYear].suppliers[supplierName].orderCount += quantity;
    monthlyData[monthYear].suppliers[supplierName].itemCount += quantity;

    supplierData[supplierName].totalCycleTimeDays += cycleTimeDays * quantity;
    supplierData[supplierName].orderCount += quantity;
    supplierData[supplierName].itemCount += quantity;
    supplierData[supplierName].monthlyData[monthYear].totalCycleTimeDays += cycleTimeDays * quantity;
    supplierData[supplierName].monthlyData[monthYear].orderCount += quantity;
    supplierData[supplierName].monthlyData[monthYear].itemCount += quantity;

    totalCycleTimeDays += cycleTimeDays * quantity;
    totalOrders += quantity;
  });

  Object.keys(monthlyData).forEach((month) => {
    const monthly = monthlyData[month];
    monthly.averageCycleTime = monthly.itemCount > 0 ?
            monthly.totalCycleTimeDays / monthly.itemCount : 0;

    Object.keys(monthly.suppliers).forEach((supplier) => {
      const supplierMonthly = monthly.suppliers[supplier];
      supplierMonthly.averageCycleTime = supplierMonthly.itemCount > 0 ?
                supplierMonthly.totalCycleTimeDays / supplierMonthly.itemCount : 0;
    });
  });

  Object.keys(supplierData).forEach((supplier) => {
    const supplierInfo = supplierData[supplier];
    supplierInfo.averageCycleTime = supplierInfo.itemCount > 0 ?
            supplierInfo.totalCycleTimeDays / supplierInfo.itemCount : 0;

    Object.keys(supplierInfo.monthlyData).forEach((month) => {
      const monthlyInfo = supplierInfo.monthlyData[month];
      monthlyInfo.averageCycleTime = monthlyInfo.itemCount > 0 ?
                monthlyInfo.totalCycleTimeDays / monthlyInfo.itemCount : 0;
    });
  });

  const overallAverageCycleTime = totalOrders > 0 ? totalCycleTimeDays / totalOrders : 0;

  return {
    overallAverageCycleTime: overallAverageCycleTime.toFixed(2),
    monthlyData: Object.keys(monthlyData).map((month) => ({
      month,
      averageCycleTime: monthlyData[month].averageCycleTime.toFixed(2),
      orderCount: monthlyData[month].orderCount,
      itemCount: monthlyData[month].itemCount,
      totalCycleTimeDays: monthlyData[month].totalCycleTimeDays,
      suppliers: Object.keys(monthlyData[month].suppliers).map((supplier) => ({
        name: supplier,
        averageCycleTime: monthlyData[month].suppliers[supplier].averageCycleTime.toFixed(2),
        orderCount: monthlyData[month].suppliers[supplier].orderCount,
        itemCount: monthlyData[month].suppliers[supplier].itemCount,
        totalCycleTimeDays: monthlyData[month].suppliers[supplier].totalCycleTimeDays,
      })),
    })).sort((a, b) => a.month.localeCompare(b.month)),
    supplierPerformance: Object.keys(supplierData).map((supplier) => ({
      name: supplier,
      averageCycleTime: supplierData[supplier].averageCycleTime.toFixed(2),
      orderCount: supplierData[supplier].orderCount,
      itemCount: supplierData[supplier].itemCount,
      totalCycleTimeDays: supplierData[supplier].totalCycleTimeDays,
      monthlyData: Object.keys(supplierData[supplier].monthlyData).map((month) => ({
        month,
        averageCycleTime: supplierData[supplier].monthlyData[month].averageCycleTime.toFixed(2),
        orderCount: supplierData[supplier].monthlyData[month].orderCount,
        itemCount: supplierData[supplier].monthlyData[month].itemCount,
        totalCycleTimeDays: supplierData[supplier].monthlyData[month].totalCycleTimeDays,
      })).sort((a, b) => a.month.localeCompare(b.month)),
    })).sort((a, b) => a.averageCycleTime - b.averageCycleTime),
    totalOrders,
    totalCycleTimeDays,
  };
}

exports.getSupplierLeadTime = onCall(async (data, context) => {
  try {
    logger.info("Fetching supplier lead time data...");

    const query = `
    SELECT 
      vendor.entityid AS name,
      po.tranid AS ns_po,
      item.itemid AS item,
      pol.quantity AS total_quantity_ordered,
      pol.quantityshiprecv AS shipped_quantity,
      pol.expectedreceiptdate AS supplier_actual_load_date,
      po.trandate AS hj_order_date,
      ABS(pol.expectedreceiptdate - po.trandate) AS supplier_lead_days,
      po.trandate AS po_date,
      vendor.entityid
    FROM 
      transaction AS po
    JOIN 
      entity AS vendor ON po.entity = vendor.id
    JOIN 
      transactionline AS pol ON po.id = pol.transaction
    JOIN 
      item ON pol.item = item.id
    WHERE 
      po.type = 'PurchOrd'
      AND po.trandate BETWEEN ADD_MONTHS(CURRENT_DATE, -12) AND CURRENT_DATE
      AND vendor.entityid != 'Xiamen Yungcheng Import & Export Co'
    ORDER BY 
      po.trandate ASC
  `;

    const result = await executeNSSuiteQLQuery(query);

    if (!result || !result.items || !Array.isArray(result.items) || result.items.length === 0) {
      logger.warn("Empty or invalid items array in SuiteQL query response");
      return {error: "No data available"};
    }

    logger.info(`Fetched ${result.items.length} records`);

    const processedData = processSupplierLeadTimeData(result.items);

    logger.info("Supplier lead time data processed successfully");
    return {result: processedData};
  } catch (error) {
    logger.error("Error fetching or processing supplier lead time data:", error);
    throw new Error(`Failed to calculate Supplier Lead Time: ${error.message}`);
  }
});
/**
 * Process the supplier lead time data and calculate the average lead time for each month and supplier.
 * @param {Array} data - The raw data fetched from NetSuite
 * @return {Object} - The processed data with average lead time for each month and supplier
 * @throws {Error} - If there is an error processing the data
 */
function processSupplierLeadTimeData(data) {
  const monthlyData = {};
  const supplierData = {};
  let totalLeadTimeDays = 0;
  let totalOrders = 0;

  data.forEach((item, index) => {
    const poDate = new Date(item.po_date);
    const monthYear = `${poDate.getFullYear()}-${(poDate.getMonth() + 1).toString().padStart(2, "0")}`;
    const supplierName = item.name;
    const leadTimeDays = parseInt(item.supplier_lead_days) || 0;
    const quantity = parseInt(item.shipped_quantity) || 0;

    if (!monthlyData[monthYear]) {
      monthlyData[monthYear] = {
        totalLeadTimeDays: 0,
        orderCount: 0,
        itemCount: 0,
        averageLeadTime: 0,
        suppliers: {},
      };
    }
    if (!supplierData[supplierName]) {
      supplierData[supplierName] = {
        totalLeadTimeDays: 0,
        orderCount: 0,
        itemCount: 0,
        averageLeadTime: 0,
        monthlyData: {},
      };
    }
    if (!monthlyData[monthYear].suppliers[supplierName]) {
      monthlyData[monthYear].suppliers[supplierName] = {
        totalLeadTimeDays: 0,
        orderCount: 0,
        itemCount: 0,
        averageLeadTime: 0,
      };
    }
    if (!supplierData[supplierName].monthlyData[monthYear]) {
      supplierData[supplierName].monthlyData[monthYear] = {
        totalLeadTimeDays: 0,
        orderCount: 0,
        itemCount: 0,
        averageLeadTime: 0,
      };
    }

    monthlyData[monthYear].totalLeadTimeDays += leadTimeDays * quantity;
    monthlyData[monthYear].orderCount += quantity;
    monthlyData[monthYear].itemCount += quantity;
    monthlyData[monthYear].suppliers[supplierName].totalLeadTimeDays += leadTimeDays * quantity;
    monthlyData[monthYear].suppliers[supplierName].orderCount += quantity;
    monthlyData[monthYear].suppliers[supplierName].itemCount += quantity;

    supplierData[supplierName].totalLeadTimeDays += leadTimeDays * quantity;
    supplierData[supplierName].orderCount += quantity;
    supplierData[supplierName].itemCount += quantity;
    supplierData[supplierName].monthlyData[monthYear].totalLeadTimeDays += leadTimeDays * quantity;
    supplierData[supplierName].monthlyData[monthYear].orderCount += quantity;
    supplierData[supplierName].monthlyData[monthYear].itemCount += quantity;

    totalLeadTimeDays += leadTimeDays * quantity;
    totalOrders += quantity;
  });

  Object.keys(monthlyData).forEach((month) => {
    const monthly = monthlyData[month];
    monthly.averageLeadTime = monthly.itemCount > 0 ?
            monthly.totalLeadTimeDays / monthly.itemCount : 0;

    Object.keys(monthly.suppliers).forEach((supplier) => {
      const supplierMonthly = monthly.suppliers[supplier];
      supplierMonthly.averageLeadTime = supplierMonthly.itemCount > 0 ?
                supplierMonthly.totalLeadTimeDays / supplierMonthly.itemCount : 0;
    });
  });

  Object.keys(supplierData).forEach((supplier) => {
    const supplierInfo = supplierData[supplier];
    supplierInfo.averageLeadTime = supplierInfo.itemCount > 0 ?
            supplierInfo.totalLeadTimeDays / supplierInfo.itemCount : 0;

    Object.keys(supplierInfo.monthlyData).forEach((month) => {
      const monthlyInfo = supplierInfo.monthlyData[month];
      monthlyInfo.averageLeadTime = monthlyInfo.itemCount > 0 ?
                monthlyInfo.totalLeadTimeDays / monthlyInfo.itemCount : 0;
    });
  });

  const overallAverageLeadTime = totalOrders > 0 ? totalLeadTimeDays / totalOrders : 0;

  return {
    overallAverageLeadTime: overallAverageLeadTime.toFixed(2),
    monthlyData: Object.keys(monthlyData).map((month) => ({
      month,
      averageLeadTime: monthlyData[month].averageLeadTime.toFixed(2),
      orderCount: monthlyData[month].orderCount,
      itemCount: monthlyData[month].itemCount,
      totalLeadTimeDays: monthlyData[month].totalLeadTimeDays,
      suppliers: Object.keys(monthlyData[month].suppliers).map((supplier) => ({
        name: supplier,
        averageLeadTime: monthlyData[month].suppliers[supplier].averageLeadTime.toFixed(2),
        orderCount: monthlyData[month].suppliers[supplier].orderCount,
        itemCount: monthlyData[month].suppliers[supplier].itemCount,
        totalLeadTimeDays: monthlyData[month].suppliers[supplier].totalLeadTimeDays,
      })),
    })).sort((a, b) => a.month.localeCompare(b.month)),
    supplierPerformance: Object.keys(supplierData).map((supplier) => ({
      name: supplier,
      averageLeadTime: supplierData[supplier].averageLeadTime.toFixed(2),
      orderCount: supplierData[supplier].orderCount,
      itemCount: supplierData[supplier].itemCount,
      totalLeadTimeDays: supplierData[supplier].totalLeadTimeDays,
      monthlyData: Object.keys(supplierData[supplier].monthlyData).map((month) => ({
        month,
        averageLeadTime: supplierData[supplier].monthlyData[month].averageLeadTime.toFixed(2),
        orderCount: supplierData[supplier].monthlyData[month].orderCount,
        itemCount: supplierData[supplier].monthlyData[month].itemCount,
        totalLeadTimeDays: supplierData[supplier].monthlyData[month].totalLeadTimeDays,
      })).sort((a, b) => a.month.localeCompare(b.month)),
    })).sort((a, b) => a.averageLeadTime - b.averageLeadTime),
    totalOrders,
    totalLeadTimeDays,
  };
}


// Helper functions should be identical for both metrics
function safeParseInt(value) {
  const parsed = parseInt(value);
  return isNaN(parsed) ? 0 : parsed;
}

function sanitizeData(data) {
  if (typeof data === "number" && isNaN(data)) return 0;
  if (typeof data === "string" && data === "NaN") return "0.00";
  if (Array.isArray(data)) return data.map(sanitizeData);
  if (typeof data === "object" && data !== null) {
    return Object.fromEntries(
      Object.entries(data).map(([key, value]) => [key, sanitizeData(value)])
    );
  }
  return data;
}

// Calculation functions should mirror each other
function calculateQualityScore(totalOrders, qualityIssues) {
  if (!totalOrders || totalOrders === 0) return "100.00";
  if (!qualityIssues || qualityIssues < 0) qualityIssues = 0;
  
  const score = ((totalOrders - qualityIssues) / totalOrders) * 100;
  if (isNaN(score)) return "100.00";
  
  return qualityIssues > 0 ? Math.min(score, 99.99).toFixed(2) : "100.00";
}


exports.getSupplierQualityScore = onCall(async (data, context) => {
  try {
    logger.info("Fetching supplier quality score data...");

    // Get quality score tags from Firestore
    const tagsDoc = await admin
      .firestore()
      .collection("settings")
      .doc("gorgiasTagMappings")
      .get();

    if (!tagsDoc.exists) {
      throw new Error("Tag mappings not found in Firestore");
    }

    const tagMappings = tagsDoc.data();
    const qualityTags = tagMappings["Supplier Quality Score"]?.tags?.nonDefectiveItems;

    if (!qualityTags || !Array.isArray(qualityTags)) {
      throw new Error("Quality tags are not properly configured in Firestore");
    }

    logger.info("Quality Tags:", qualityTags);

    const poQuery = `
      SELECT 
        vendor.entityid AS name,
        TO_CHAR(po.trandate, 'YYYY-MM') AS month_year,
        po.tranid AS ns_po,
        item.itemid AS item,
        pol.quantity AS total_quantity_ordered,
        pol.quantityshiprecv AS shipped_quantity,
        po.trandate AS po_date
      FROM 
        transaction AS po
      JOIN 
        entity AS vendor ON po.entity = vendor.id
      JOIN 
        transactionline AS pol ON po.id = pol.transaction
      JOIN 
        item ON pol.item = item.id
      WHERE 
        po.type = 'PurchOrd'
        AND po.trandate BETWEEN ADD_MONTHS(CURRENT_DATE, -12) AND CURRENT_DATE
        AND vendor.entityid != 'Xiamen Yungcheng Import & Export Co'
      ORDER BY 
        po.trandate ASC
    `;

    const qualityIssuesQuery = `
    WITH TaggedTickets AS (
      SELECT 
        t.id,
        t.createdAt,
        tag.name as tag_name
      FROM 
        customerService.gorgiasTickets t
        CROSS JOIN UNNEST(t.tags) as tag
      WHERE 
        t.createdAt >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
        AND tag.name IN (${qualityTags.map(tag => `'${tag}'`).join(",")})
    ),
    MonthlyStats AS (
      SELECT 
        FORMAT_TIMESTAMP('%Y-%m', createdAt) as month_year,
        COUNT(DISTINCT id) as quality_issues
      FROM 
        TaggedTickets
      GROUP BY 
        month_year
    ),
    IssueTypes AS (
      SELECT 
        tag_name as type,
        COUNT(DISTINCT id) as count
      FROM 
        TaggedTickets
      GROUP BY 
        tag_name
      ORDER BY 
        count DESC
    )
    SELECT 
      m.*,
      ARRAY_AGG(STRUCT(type, count)) as issue_types
    FROM 
      MonthlyStats m
    CROSS JOIN IssueTypes
    GROUP BY 
      month_year,
      quality_issues
    ORDER BY 
      month_year ASC`;

    const [posResult, qualityIssuesResult] = await Promise.all([
      executeNSSuiteQLQuery(poQuery),
      runQuery({ query: qualityIssuesQuery })
    ]);

    if (!posResult?.items || !Array.isArray(posResult.items)) {
      throw new Error("Invalid response structure from NetSuite query");
    }

    const monthlyData = {};
    const supplierData = {};
    let totalOrders = 0;
    let totalQualityIssues = 0;

    // Process PO data with safe integer parsing
    posResult.items.forEach(row => {
      const monthYear = row.month_year || "";
      const supplierName = row.name || "";
      const quantity = safeParseInt(row.shipped_quantity);

      if (monthYear && supplierName) {
        if (!monthlyData[monthYear]) {
          monthlyData[monthYear] = {
            monthYear,
            totalOrders: 0,
            qualityIssues: 0,
            qualityScore: "100.00",
            suppliers: {}
          };
        }
        
        if (!monthlyData[monthYear].suppliers[supplierName]) {
          monthlyData[monthYear].suppliers[supplierName] = {
            totalOrders: 0,
            qualityIssues: 0,
            qualityScore: "100.00"
          };
        }

        if (!supplierData[supplierName]) {
          supplierData[supplierName] = {
            totalOrders: 0,
            qualityIssues: 0,
            qualityScore: "100.00",
            monthlyData: {}
          };
        }

        if (!supplierData[supplierName].monthlyData[monthYear]) {
          supplierData[supplierName].monthlyData[monthYear] = {
            totalOrders: 0,
            qualityIssues: 0,
            qualityScore: "100.00"
          };
        }

        monthlyData[monthYear].totalOrders += quantity;
        monthlyData[monthYear].suppliers[supplierName].totalOrders += quantity;
        supplierData[supplierName].totalOrders += quantity;
        supplierData[supplierName].monthlyData[monthYear].totalOrders += quantity;
        totalOrders += quantity;
      }
    });

    // Process quality issues with safe integer parsing
    const issueTypes = [];
    qualityIssuesResult.forEach(row => {
      const monthYear = row.month_year;
      if (monthlyData[monthYear]) {
        const qualityIssueCount = safeParseInt(row.quality_issues);
        monthlyData[monthYear].qualityIssues = qualityIssueCount;
        totalQualityIssues += qualityIssueCount;

        if (monthlyData[monthYear].totalOrders > 0) {
          Object.keys(monthlyData[monthYear].suppliers).forEach(supplier => {
            const supplierShare = monthlyData[monthYear].suppliers[supplier].totalOrders / monthlyData[monthYear].totalOrders;
            const supplierIssues = Math.round(qualityIssueCount * supplierShare) || 0;
            monthlyData[monthYear].suppliers[supplier].qualityIssues = supplierIssues;
            supplierData[supplier].qualityIssues += supplierIssues;
            supplierData[supplier].monthlyData[monthYear].qualityIssues = supplierIssues;
          });
        }

        if (row.issue_types && Array.isArray(row.issue_types)) {
          row.issue_types.forEach(issue => {
            if (!issueTypes.find(i => i.type === issue.type)) {
              issueTypes.push({
                type: issue.type,
                count: safeParseInt(issue.count)
              });
            }
          });
        }
      }
    });

    // Calculate quality scores
    Object.keys(monthlyData).forEach(monthYear => {
      const month = monthlyData[monthYear];
      month.qualityScore = calculateQualityScore(month.totalOrders, month.qualityIssues);
      
      Object.keys(month.suppliers).forEach(supplier => {
        const supplierMonth = month.suppliers[supplier];
        supplierMonth.qualityScore = calculateQualityScore(supplierMonth.totalOrders, supplierMonth.qualityIssues);
      });
    });

    Object.keys(supplierData).forEach(supplier => {
      const sup = supplierData[supplier];
      sup.qualityScore = calculateQualityScore(sup.totalOrders, sup.qualityIssues);
      
      Object.keys(sup.monthlyData).forEach(monthYear => {
        const monthData = sup.monthlyData[monthYear];
        monthData.qualityScore = calculateQualityScore(monthData.totalOrders, monthData.qualityIssues);
      });
    });

    issueTypes.sort((a, b) => b.count - a.count);

    const totalQualityPOs = Math.max(0, totalOrders - totalQualityIssues);
    const overallQualityScore = calculateQualityScore(totalOrders, totalQualityIssues);

    const response = {
      overallQualityScore,
      monthlyData: Object.values(monthlyData)
        .sort((a, b) => a.monthYear.localeCompare(b.monthYear))
        .map(month => ({
          ...month,
          suppliers: Object.entries(month.suppliers).map(([name, data]) => ({
            name,
            ...data
          }))
        })),
      supplierPerformance: Object.entries(supplierData)
        .map(([name, data]) => ({
          name,
          qualityScore: data.qualityScore,
          totalOrders: data.totalOrders,
          qualityIssues: data.qualityIssues,
          monthlyData: Object.entries(data.monthlyData)
            .map(([month, monthData]) => ({
              month,
              ...monthData
            }))
            .sort((a, b) => a.month.localeCompare(b.month))
        }))
        .sort((a, b) => parseFloat(b.qualityScore) - parseFloat(a.qualityScore)),
      summary: {
        totalOrders,
        qualityPOs: totalQualityPOs,
        qualityIssues: totalQualityIssues
      },
      qualityIssues: {
        types: issueTypes
      }
    };

    return sanitizeData(response);
  } catch (error) {
    logger.error("Error calculating supplier quality score:", error);
    throw new Error(`Failed to calculate Supplier Quality Score: ${error.message}`);
  }
});

function calculateDefectRate(totalOrders, defectCount) {
  if (!totalOrders || totalOrders === 0) return "0.00";
  if (!defectCount || defectCount < 0) defectCount = 0;
  
  // If there are any defects, ensure minimum rate is 0.01%
  if (defectCount > 0) {
    const rate = (defectCount / totalOrders) * 100;
    if (isNaN(rate)) return "0.01"; // Even if calculation fails, there are defects
    const formattedRate = Math.min(rate, 99.99).toFixed(2);
    // If calculated rate would round to 0.00, return 0.01 instead
    return parseFloat(formattedRate) === 0 ? "0.01" : formattedRate;
  }
  
  return "0.00"; // Only return 0.00 if there are no defects
}

exports.getSupplierDefectRate = onCall(async (data, context) => {
  try {
    logger.info("Fetching supplier defect rate data...");

    // Get defect tags from Firestore
    const tagsDoc = await admin
      .firestore()
      .collection("settings")
      .doc("gorgiasTagMappings")
      .get();

    if (!tagsDoc.exists) {
      throw new Error("Tag mappings not found in Firestore");
    }

    const tagMappings = tagsDoc.data();
    // Use the exact same query as quality score but with defect tags
    const defectTags = tagMappings["Supplier Defect Rate"]?.tags?.defectiveItems;

    if (!defectTags || !Array.isArray(defectTags)) {
      throw new Error("Defect tags are not properly configured in Firestore");
    }

    logger.info("Defect Tags:", defectTags);

    // Keep the exact same PO query as quality score
    const poQuery = `
      SELECT 
        vendor.entityid AS name,
        TO_CHAR(po.trandate, 'YYYY-MM') AS month_year,
        po.tranid AS ns_po,
        item.itemid AS item,
        pol.quantity AS total_quantity_ordered,
        pol.quantityshiprecv AS shipped_quantity,
        po.trandate AS po_date
      FROM 
        transaction AS po
      JOIN 
        entity AS vendor ON po.entity = vendor.id
      JOIN 
        transactionline AS pol ON po.id = pol.transaction
      JOIN 
        item ON pol.item = item.id
      WHERE 
        po.type = 'PurchOrd'
        AND po.trandate BETWEEN ADD_MONTHS(CURRENT_DATE, -12) AND CURRENT_DATE
        AND vendor.entityid != 'Xiamen Yungcheng Import & Export Co'
      ORDER BY 
        po.trandate ASC
    `;

    const defectIssuesQuery = `
    WITH TaggedTickets AS (
      SELECT 
        t.id,
        t.createdAt,
        tag.name as tag_name
      FROM 
        customerService.gorgiasTickets t
        CROSS JOIN UNNEST(t.tags) as tag
      WHERE 
        t.createdAt >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
        AND tag.name IN (${defectTags.map(tag => `'${tag}'`).join(",")})
    ),
    MonthlyStats AS (
      SELECT 
        FORMAT_TIMESTAMP('%Y-%m', createdAt) as month_year,
        COUNT(DISTINCT id) as defect_count
      FROM 
        TaggedTickets
      GROUP BY 
        month_year
    ),
    IssueTypes AS (
      SELECT 
        tag_name as type,
        COUNT(DISTINCT id) as count
      FROM 
        TaggedTickets
      GROUP BY 
        tag_name
      ORDER BY 
        count DESC
    )
    SELECT 
      m.*,
      ARRAY_AGG(STRUCT(type, count)) as issue_types
    FROM 
      MonthlyStats m
    CROSS JOIN IssueTypes
    GROUP BY 
      month_year,
      defect_count
    ORDER BY 
      month_year ASC`;

    const [posResult, defectIssuesResult] = await Promise.all([
      executeNSSuiteQLQuery(poQuery),
      runQuery({ query: defectIssuesQuery })
    ]);

    if (!posResult?.items || !Array.isArray(posResult.items)) {
      throw new Error("Invalid response structure from NetSuite query");
    }

    const monthlyData = {};
    const supplierData = {};
    let totalOrders = 0;
    let totalDefects = 0;

    // Process PO data with safe integer parsing - same as quality score
    posResult.items.forEach(row => {
      const monthYear = row.month_year || "";
      const supplierName = row.name || "";
      const quantity = safeParseInt(row.shipped_quantity);

      if (monthYear && supplierName) {
        if (!monthlyData[monthYear]) {
          monthlyData[monthYear] = {
            monthYear,
            totalOrders: 0,
            defectCount: 0,
            defectRate: "0.00",
            suppliers: {}
          };
        }
        
        if (!monthlyData[monthYear].suppliers[supplierName]) {
          monthlyData[monthYear].suppliers[supplierName] = {
            totalOrders: 0,
            defectCount: 0,
            defectRate: "0.00"
          };
        }

        if (!supplierData[supplierName]) {
          supplierData[supplierName] = {
            totalOrders: 0,
            defectCount: 0,
            defectRate: "0.00",
            monthlyData: {}
          };
        }

        if (!supplierData[supplierName].monthlyData[monthYear]) {
          supplierData[supplierName].monthlyData[monthYear] = {
            totalOrders: 0,
            defectCount: 0,
            defectRate: "0.00"
          };
        }

        monthlyData[monthYear].totalOrders += quantity;
        monthlyData[monthYear].suppliers[supplierName].totalOrders += quantity;
        supplierData[supplierName].totalOrders += quantity;
        supplierData[supplierName].monthlyData[monthYear].totalOrders += quantity;
        totalOrders += quantity;
      }
    });

    // Process defects using same aggregation logic as quality score
    const issueTypes = [];
    defectIssuesResult.forEach(row => {
      const monthYear = row.month_year;
      if (monthlyData[monthYear]) {
        const defectCount = safeParseInt(row.defect_count);
        monthlyData[monthYear].defectCount = defectCount;
        totalDefects += defectCount;

        if (monthlyData[monthYear].totalOrders > 0) {
          Object.keys(monthlyData[monthYear].suppliers).forEach(supplier => {
            const supplierShare = monthlyData[monthYear].suppliers[supplier].totalOrders / monthlyData[monthYear].totalOrders;
            const supplierDefects = Math.round(defectCount * supplierShare) || 0;
            monthlyData[monthYear].suppliers[supplier].defectCount = supplierDefects;
            supplierData[supplier].defectCount += supplierDefects;
            supplierData[supplier].monthlyData[monthYear].defectCount = supplierDefects;
          });
        }

        if (row.issue_types && Array.isArray(row.issue_types)) {
          row.issue_types.forEach(issue => {
            const existingIssue = issueTypes.find(i => i.type === issue.type);
            if (!existingIssue) {
              issueTypes.push({
                type: issue.type,
                count: safeParseInt(issue.count)
              });
            }
          });
        }
      }
    });

    // Calculate rates using same logic as quality score
    Object.keys(monthlyData).forEach(monthYear => {
      const month = monthlyData[monthYear];
      month.defectRate = calculateDefectRate(month.totalOrders, month.defectCount);
      
      Object.keys(month.suppliers).forEach(supplier => {
        const supplierMonth = month.suppliers[supplier];
        supplierMonth.defectRate = calculateDefectRate(supplierMonth.totalOrders, supplierMonth.defectCount);
      });
    });

    Object.keys(supplierData).forEach(supplier => {
      const sup = supplierData[supplier];
      sup.defectRate = calculateDefectRate(sup.totalOrders, sup.defectCount);
      
      Object.keys(sup.monthlyData).forEach(monthYear => {
        const monthData = sup.monthlyData[monthYear];
        monthData.defectRate = calculateDefectRate(monthData.totalOrders, monthData.defectCount);
      });
    });

    issueTypes.sort((a, b) => b.count - a.count);

    const totalNonDefectOrders = Math.max(0, totalOrders - totalDefects);
    const overallDefectRate = calculateDefectRate(totalOrders, totalDefects);

    const response = {
      overallDefectRate,
      monthlyData: Object.values(monthlyData)
        .sort((a, b) => a.monthYear.localeCompare(b.monthYear))
        .map(month => ({
          ...month,
          suppliers: Object.entries(month.suppliers).map(([name, data]) => ({
            name,
            ...data
          }))
        })),
      supplierPerformance: Object.entries(supplierData)
        .map(([name, data]) => ({
          name,
          defectRate: data.defectRate,
          totalOrders: data.totalOrders,
          defectCount: data.defectCount,
          monthlyData: Object.entries(data.monthlyData)
            .map(([month, monthData]) => ({
              month,
              ...monthData
            }))
            .sort((a, b) => a.month.localeCompare(b.month))
        }))
        .sort((a, b) => parseFloat(a.defectRate) - parseFloat(b.defectRate)),
      summary: {
        totalOrders,
        nonDefectOrders: totalNonDefectOrders,
        totalDefects
      },
      defects: {
        types: issueTypes
      }
    };

    return sanitizeData(response);
  } catch (error) {
    logger.error("Error calculating supplier defect rate:", error);
    throw new Error(`Failed to calculate Supplier Defect Rate: ${error.message}`);
  }
});