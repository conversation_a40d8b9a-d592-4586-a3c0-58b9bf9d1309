import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Button, Popconfirm, message, Table } from 'antd';
import { collection, doc, deleteDoc, onSnapshot, addDoc, setDoc } from 'firebase/firestore';
import { db, api } from '../pages/firebase';
import { SYSTEMS } from '../constants';
import { Tabs } from 'antd';

const ConnectorModal = ({ visible, onCancel, connector, onSave }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (connector) {
      form.setFieldsValue(connector);
    } else {
      form.resetFields();
    }
  }, [connector, form]);

  const handleSubmit = () => {
    form.validateFields()
      .then(values => {
        onSave(values);
        form.resetFields();
      })
      .catch(info => {
        console.log('Validate Failed:', info);
      });
  };

  return (
    <Modal
      title={connector ? "Edit Connector" : "Add Connector"}
      open={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        connector && (
          <Popconfirm
            key="delete"
            title="Are you sure you want to delete this connector?"
            okText="Yes"
            cancelText="No"
            onConfirm={() => {
              // Handle delete logic here
              onSave({ ...connector, _delete: true });
            }}
          >
            <Button danger>Delete</Button>
          </Popconfirm>
        ),
        <Button key="submit" type="primary" onClick={handleSubmit}>
          Save
        </Button>,
      ]}
    >
      <Tabs>
        <Tabs.TabPane tab="Connection" key="connection">
          <Form
            form={form}
            layout="vertical"
            initialValues={connector || {}}
          >
            <Form.Item
              name="name"
              label="Name"
              rules={[{ required: true, message: 'Please enter connector name' }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="system"
              label="System"
              rules={[{ required: true, message: 'Please select connector system' }]}
            >
              <Select>
                {Object.entries(SYSTEMS).map(([key, vals]) => (
                  <Select.Option key={key} value={key}>{vals.label}</Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item label="Connection Details" shouldUpdate={(prevValues, currentValues) => prevValues.system !== currentValues.system}>
              {({ getFieldValue }) => {
                const system = getFieldValue('system');
                return system ? (
                  <>
                    {SYSTEMS[system]?.connectionKeys.map((key) => (
                      <Form.Item
                        key={key}
                        name={['connection', key]}
                        label={key.charAt(0).toUpperCase() + key.slice(1)}
                        rules={[{ required: true, message: `Please enter ${key}` }]}
                      >
                        <Input />
                      </Form.Item>
                    ))}
                  </>
                ) : null;
              }}
            </Form.Item>
            <Form.Item
              name="status"
              label="Status"
            >
              <Select>
                <Select.Option value="active">Active</Select.Option>
                <Select.Option value="inactive">Inactive</Select.Option>
                <Select.Option value="testing">Testing</Select.Option>
              </Select>
            </Form.Item>
          </Form>
        </Tabs.TabPane>
      </Tabs>
    </Modal>
  );
};

export default ConnectorModal;