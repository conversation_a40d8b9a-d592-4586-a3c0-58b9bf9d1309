/* eslint-disable camelcase */
const API_URL = 'https://open-api.tiktokglobalshop.com';
const axios = require('axios');
const crypto = require('crypto');

const tiktokGetSignature = (connection, params, apiUrl, reqBody) => {
  const keys = Object.keys(params).filter(k => k !== 'sign' && k !== 'access_token').sort();
  const baseString = keys.map(key => `${key}${params[key]}`).join('');
  const bodyString = reqBody && Object.keys(reqBody).length > 0 ? JSON.stringify(reqBody) : '';
  const fullString = `${connection.appSecret}${apiUrl}${baseString}${bodyString}${connection.appSecret}`;

  return crypto
    .createHmac('sha256', connection.appSecret)
    .update(fullString)
    .digest('hex');
};

const tiktokGetOrders = async (connection, startDate, endDate = new Date()) => {
  const API_URL = 'https://open-api.tiktokglobalshop.com'; // Make sure this is set correctly
  const orderUrl = '/order/202309/orders/search';
  const allOrders = [];

  try {
    let pageToken = null;
    while (true) {
      const timestamp = Math.floor(Date.now() / 1000);
      const requestBody = {
        create_time_ge: Math.floor(startDate.getTime() / 1000),
        create_time_lt: Math.floor(endDate.getTime() / 1000),
      };

      const queryParams = {
        access_token: connection.accessToken,
        app_key: connection.appKey,
        version: '202309',
        shop_id: connection.shopId,
        shop_cipher: connection.shopCipher,
        sort_field: 'create_time',
        sort_order: 'ASC',
        page_size: 50,
        timestamp,
      };

      if (pageToken) {
        queryParams.page_token = pageToken;
      }

      const sign = tiktokGetSignature(connection, queryParams, orderUrl, requestBody);
      queryParams.sign = sign;

      const sortedParams = Object.keys(queryParams).sort().reduce((acc, key) => {
        acc[key] = queryParams[key];
        return acc;
      }, {});

      const url = `${API_URL}${orderUrl}?${new URLSearchParams(sortedParams).toString()}`;

      const response = await axios.post(url, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          'x-tts-access-token': queryParams.access_token
        }
      });

      const data = response.data?.data;
      if (!data?.orders || data.orders.length === 0) break;

      allOrders.push(...data.orders);

      if (!data.next_page_token) break;
      pageToken = data.next_page_token;
    }
  } catch (err) {
    console.error('Failed to fetch TikTok orders:', err.response?.data || err.message || err);
  }

  return allOrders;
};
exports.tiktokGetOrders = tiktokGetOrders;

const flattenObject = (obj, parent = '', res = {}) => {
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const propName = parent ? parent + '_' + key : key;
      if (typeof obj[key] == 'object' && obj[key] !== null) {
        flattenObject(obj[key], propName, res);
      } else {
        res[propName] = obj[key];
      }
    }
  }
  return res;
};