import '../App.css';
import {
  // useEffect,
  useEffect,
  useState,
} from 'react';
import React from 'react';
// import Select from 'react-select';
// import Header from '../components/headers';
import axios from 'axios';
import * as XLSX from 'xlsx';

const ItemTable = ({userObj}) => {
  const [itemData, setItemData] = useState([]);
  const [itemLoading, setItemLoading] = useState(true);

  const [selectedItemInternalId, setSelectedItemInternalId] = useState(null);
  const [itemDetails, setItemDetails] = useState(null);
  const [itemDetailsLoading, setItemDetailsLoading] = useState(false);
  const [selectedProductType, setSelectedProductType] = useState('All');
  const [productTypes, setProductTypes] = useState([]);
  const [selectedLifeStatus, setSelectedLifeStatus] = useState('All');
  const [lifeStatuses, setLifeStatuses] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItems, setSelectedItems] = useState([]);
  const [showFieldModal, setShowFieldModal] = useState(false);
  const [brands, setBrands] = useState([]);
  const [selectedBrands, setSelectedBrands] = useState('All');
  const [productSpecifications, setProductSpecifications] = useState([]);
  const [selectedProductSpecification, setSelectedProductSpecification] = useState('All');
  const defaultSelectedFields = {
    name: true,
    item_image_url: true,
    display_name: true,
    upccode: true,
    product_type: true,
    life_status: true,
    weight: true,
    unit_per_case: true,
  };
  const fieldLabels = {
    name: 'Name',
    item_image_url: 'Image',
    display_name: 'Display Name',
    upccode: 'UPC Code',
    product_type: 'Product Type',
    life_status: 'Life Status',
    weight: 'Weight',
    unit_per_case: 'Unit per Case',
  };
  const [selectedFields, setSelectedFields] = useState(
      JSON.parse(localStorage.getItem('selectedFields')) || defaultSelectedFields,
  );

  useEffect(() => {
    const fetchItemData = async () => {
      try {
        // console.log('fetching item data');
        const response = await axios.get(
            // eslint-disable-next-line max-len
            'https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=3661&deploy=1&action=getitems&compid=6810379&ns-at=AAEJ7tMQMYrAVRguSFLy9cAiJ_3qWbbMYyvGMu1ULt198gs3NcQ',
        );
        const result = response.data;
        if (!result || result.length === 0) {
          setItemLoading(false);
          return;
        }
        if (response.data.error_name === 'SSS_USAGE_LIMIT_EXCEEDED') {
          alert('NetSuite API limit exceeded. Please try again later.');
          setItemLoading(false);
          return;
        }
        // console.log('result', result);
        const data = JSON.parse(result.replace(/<!--[\s\S]*?-->/g, ''));
        const types = new Set(data.map((item) => item.product_type));
        setProductTypes(['All', ...types]);
        const lifestatuses = new Set(
            data
                .map((item) => item.life_status)
                .filter(
                    (status) =>
                      status && status.trim() !== '' && status.trim() !== '- None -',
                ),
        );
        const brands = new Set(
            data
                .map((item) => item.brand)
                .filter(
                    (brand) =>
                      brand && brand.trim() !== '' && brand.trim() !== '- None -',
                ),
        );
        const productSpecifications = new Set(
            data
                .map((item) => item.prodSpec)
                .filter(
                    (spec) =>
                      spec && spec.trim() !== '' && spec.trim() !== '- None -',
                ),
        );
        setLifeStatuses(['All', ...lifestatuses]);
        setBrands(['All', ...brands]);
        setProductSpecifications(['All', ...productSpecifications]);
        setItemData(data || []);
        setItemLoading(false);
      } catch (error) {
        console.error('Error fetching item data:', error);
        setItemLoading(false);
      }
    };

    fetchItemData();
  }, []);

  useEffect(() => {
    const fetchItemDetails = async () => {
      if (selectedItemInternalId) {
        setItemDetailsLoading(true);
        try {
          const response = await axios.get(
              `https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=3661&deploy=1` +
            `&action=getitemdata&param_item_internalid=${selectedItemInternalId}&compid=6810379` +
            `&ns-at=AAEJ7tMQMYrAVRguSFLy9cAiJ_3qWbbMYyvGMu1ULt198gs3NcQ`,
          );
          const result = response.data;
          const data =
            typeof result === 'string' ?
              JSON.parse(result.replace(/<!--[\s\S]*?-->/g, '')) :
              result;
          setItemDetails(data);
          setItemDetailsLoading(false);
          return;
        } catch (error) {
          console.error('Error fetching item details:', error);
          setItemDetailsLoading(false);
        }
      }
      return;
    };
    if (selectedItemInternalId) {
      fetchItemDetails();
    }
  }, [selectedItemInternalId]);
  const handleRowClick = (itemInternalId) => {
    setSelectedItemInternalId(itemInternalId);
  };

  const closeModal = () => {
    setSelectedItemInternalId(null);
    setItemDetails(null);
  };

  const handleProductTypeChange = (event) => {
    setSelectedProductType(event.target.value);
  };

  const handleLifeStatusChange = (event) => {
    setSelectedLifeStatus(event.target.value);
  };
  const handleBrandChange = (event) => {
    setSelectedBrands(event.target.value);
  };
  const handleProductSpecificationChange = (event) => {
    setSelectedProductSpecification(event.target.value);
  };

  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
  };

  const handleCheckboxChange = (item) => {
    const isSelected = selectedItems.includes(item);
    const updatedSelectedItems = isSelected ?
      selectedItems.filter((i) => i !== item) :
      [...selectedItems, item];
    setSelectedItems(updatedSelectedItems);
  };

  const exportToExcel = () => {
    const dataToExport = selectedItems.map((item) => {
      const selectedData = {};
      Object.keys(selectedFields).forEach((field) => {
        if (selectedFields[field]) {
          let value = item[field];
          if (field == 'item_image_url') {
            value = value.replace(/&amp;/g, '&');
          }
          selectedData[fieldLabels[field]] = value;
        }
      });
      return selectedData;
    });

    const worksheet = XLSX.utils.json_to_sheet(dataToExport);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'HJ Items');
    XLSX.writeFile(workbook, 'HJ Items.xlsx');
  };

  const handleMarkAll = () => {
    setSelectedItems(filteredItems);
  };

  const handleUnmarkAll = () => {
    setSelectedItems([]);
  };

  const handleFieldSelectionChange = (field) => {
    const updatedFields = {
      ...selectedFields,
      [field]: !selectedFields[field],
    };
    setSelectedFields(updatedFields);
    localStorage.setItem('selectedFields', JSON.stringify(updatedFields));
  };

  const filteredItems = itemData.filter(
      (item) =>
        (selectedProductType === 'All' ||
        item.product_type === selectedProductType) &&
      (selectedLifeStatus === 'All' ||
        item.life_status === selectedLifeStatus) &&
      (item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.display_name.toLowerCase().includes(searchQuery.toLowerCase())) && (
          selectedBrands === 'All' || item.brand === selectedBrands
        ),
  );

  userObj.page = 'items';

  return (
    <div id="wrapper">
      <div id="content-wrapper" className="d-flex flex-column">
        <div className="container-fluid">
          <div className="card shadow mt-3 mb-4" id="filter-area">
            <div className="card-body">
              <form className="user">
                <div className="form-group row">
                  <div className="filter-div">
                    <div className="col-auto">
                      <label className="col-form-label">Search:</label>
                    </div>
                    <div className="col-auto">
                      <input
                        id="searchBox"
                        type="text"
                        value={searchQuery}
                        onChange={handleSearchChange}
                        placeholder="Search..."
                        className="form-control form-control-sm"
                        style={{width: '200px'}}
                      />
                    </div>
                  </div>
                  <div className="filter-div">
                    <div className="col-auto">
                      <label className="col-form-label">Product Type:</label>
                    </div>
                    <div className="col-auto">
                      <select
                        id="productTypeFilter"
                        className="form-control form-select form-control-sm"
                        value={selectedProductType}
                        onChange={handleProductTypeChange}
                      >
                        {productTypes.map((type) => (
                          <option key={type} value={type}>
                            {type}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <div className="filter-div">
                    <div className="col-auto">
                      <label className="col-form-label">Life Status:</label>
                    </div>
                    <div className="col-auto">
                      <select
                        id="lifeStatusFilter"
                        className="form-control form-select form-control-sm"
                        value={selectedLifeStatus}
                        onChange={handleLifeStatusChange}
                      >
                        {lifeStatuses.map((type) => (
                          <option key={type} value={type}>
                            {type}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <div className="filter-div">
                    <div className="col-auto">
                      <label className="col-form-label">Brand:</label>
                    </div>
                    <div className="col-auto">
                      <select
                        id="brandFilter"
                        className="form-control form-select form-control-sm"
                        value={selectedBrands}
                        onChange={handleBrandChange}
                      >
                        {brands.map((type) => (
                          <option key={type} value={type}>
                            {type}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <div className="filter-div">
                    <div className="col-auto">
                      <label className="col-form-label">Specification:</label>
                    </div>
                    <div className="col-auto">
                      <select
                        id="productSpecificationFilter"
                        className="form-control form-select form-control-sm"
                        value={selectedProductSpecification}
                        onChange={handleProductSpecificationChange}
                      >
                        {productSpecifications.map((type) => (
                          <option key={type} value={type}>
                            {type}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
          <div className="card shadow mt-3 mb-4">
            <div className="card-head">
              <div
                className="container-fluid"
                style={{padding: '20px 20px 0'}}
              >
                <div className="d-flex justify-content-between">
                  <div className="pull-left">
                    <button
                      type="submit"
                      className="btn btn-dark btn-sm"
                      onClick={handleMarkAll}
                    >
                      <span className="text">Mark All</span>
                    </button>
                    <button
                      type="submit"
                      className="btn btn-dark btn-sm"
                      onClick={handleUnmarkAll}
                    >
                      <span className="text">Unmark All</span>
                    </button>
                    <button
                      type="submit"
                      className="btn btn-dark btn-sm"
                      onClick={exportToExcel}
                      disabled={selectedItems.length === 0}
                    >
                      <span className="text">Export</span>
                    </button>
                  </div>
                  <div className="pull-right">
                    <button
                      type="submit"
                      className="btn btn-dark btn-sm"
                      onClick={() => setShowFieldModal(true)}
                    >
                      <span className="text">Configure Fields</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div className="card-body">
              {}
              <div className="table-responsive">
                {itemLoading ? (
                  <div className="spinner-container">
                    <div className="spinner-border text-primary" role="status">
                      <span className="sr-only">Loading...</span>
                    </div>
                  </div>
                ) : (
                  <table className="table">
                    {itemDetailsLoading ? (
                      <div className="spinner-container">
                        <div className="spinner-border text-primary" role="status">
                          <span className="sr-only">Loading...</span>
                        </div>
                      </div>
                    ) : (<tbody className="flex-container">
                      {filteredItems.map((item) => (
                        <tr
                          key={item.internalid}
                          onClick={() => handleRowClick(item.internalid)}
                          className="flex-item"
                        >
                          <td className="center-checkbox">
                            <input
                              type="checkbox"
                              checked={selectedItems.includes(item)}
                              onChange={() => handleCheckboxChange(item)}
                              onClick={(e) => e.stopPropagation()}
                            />
                          </td>
                          {selectedFields.item_image_url && (
                            <td style={{width: '110px'}}>
                              {item.item_image_url &&
                                selectedFields.item_image_url ? (
                                <img
                                  src={item.item_image_url.replace(/&amp;/g, '&')}
                                  alt={item.display_name}
                                  style={{width: '100px'}}
                                />
                              ) : (
                                ''
                              )}
                            </td>
                          )}
                          <td style={{paddingTop: '20px'}}>
                            <strong>{item.name}</strong>
                            <ul>
                              {selectedFields.display_name && (
                                <li>Display Name: {item.display_name}</li>
                              )}
                              {selectedFields.upccode && (
                                <li>UPC Code: {item.upccode}</li>
                              )}
                              {selectedFields.product_type && (
                                <li>Product Type: {item.product_type}</li>
                              )}
                              {selectedFields.life_status && (
                                <li>Life Status Type: {item.life_status}</li>
                              )}
                              {selectedFields.weight && (
                                <li>Weight: {item.weight} oz.</li>
                              )}
                              {selectedFields.unit_per_case && (
                                <li>{item.unit_per_case} Units per Case</li>
                              )}
                            </ul>
                          </td>
                        </tr>
                      ))}
                    </tbody>)}

                  </table>
                )}
              </div>
            </div>

            {/* Render the modal if an item is selected */}
            {selectedItemInternalId && (
              <div className="modal fade show">
                <div className="modal-dialog modal-lg">
                  {itemDetailsLoading ? (
                    <div>Loading...</div>
                  ) : (
                    itemDetails && (
                      <div className="modal-content">
                        <div className="modal-header">
                          <h5 className="modal-title">{itemDetails.name}</h5>
                          <span className="close" onClick={closeModal}>
                            &times;
                          </span>
                        </div>
                        <div className="modal-body">
                          <div className="row">
                            {itemDetails.item_image_url ? (
                              <div className="col-md-3">
                                <img
                                  src={itemDetails.item_image_url.replace(
                                      /&amp;/g,
                                      '&',
                                  )}
                                  alt={itemDetails.display_name}
                                  style={{width: '150px'}}
                                />
                              </div>
                            ) : (
                              ''
                            )}
                            <div
                              className={
                                itemDetails.item_image_url ?
                                  'col-md-9' :
                                  'col-md-12'
                              }
                            >
                              <dl className="row">
                                <dt className="col-sm-3">Display Name:</dt>
                                <dd className="col-sm-9">
                                  {itemDetails.display_name}
                                </dd>
                                <dt className="col-sm-3">UPC Code:</dt>
                                <dd className="col-sm-9">
                                  {itemDetails.upccode}
                                </dd>
                                <dt className="col-sm-3">Product Type:</dt>
                                <dd className="col-sm-9">
                                  {itemDetails.product_type}
                                </dd>
                                <dt className="col-sm-3">Life Status:</dt>
                                <dd className="col-sm-9">
                                  {itemDetails.life_status}
                                </dd>
                                <dt className="col-sm-3">Weight:</dt>
                                <dd className="col-sm-9">
                                  {itemDetails.weight}
                                </dd>
                                <dt className="col-sm-3">Units per Case:</dt>
                                <dd className="col-sm-9">
                                  {itemDetails.unit_per_case}
                                </dd>
                              </dl>
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>
            )}
            {showFieldModal && (
              <div className="modal fade show">
                <div className="modal-dialog">
                  <div className="modal-content">
                    <div className="modal-header">
                      <h5 className="modal-title">Select Fields to Display</h5>
                      <span
                        className="close"
                        onClick={() => setShowFieldModal(false)}
                      >
                        &times;
                      </span>
                    </div>
                    <div className="modal-body">
                      <div className="row">
                        <div className="field-selection">
                          {Object.keys(selectedFields).map((field) => (
                            <div key={field} className="form-check">
                              <input
                                type="checkbox"
                                checked={selectedFields[field]}
                                onChange={() =>
                                  handleFieldSelectionChange(field)
                                }
                                disabled={field == 'name'}
                                className="form-check-input"
                              />
                              <label className="form-check-label">
                                {fieldLabels[field]}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ItemTable;
