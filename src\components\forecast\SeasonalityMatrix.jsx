import React, { useState, useEffect, useMemo } from 'react';
import { Layout, Row, Col, Button, Modal, Card, Space, Typography, Switch, Badge, Tooltip } from 'antd';
import { AgGridReact } from 'ag-grid-react';
import { api } from '../../pages/firebase';
import { themeBalham } from 'ag-grid-community';

const { Title } = Typography;

const SeasonalityMatrix = () => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [forecastNodeData, setForecastNodeData] = useState({});
  const [originalData, setOriginalData] = useState({});
  const [pendingChanges, setPendingChanges] = useState({});
  const [isDirty, setIsDirty] = useState(false);
  const [showPercent, setShowPercent] = useState(true);
  const [validationErrors, setValidationErrors] = useState({});

  const fetchData = async () => {
    setLoading(true);
    try {
      const bigqueryPromises = [];
      const forecastNodes = await api.bigQueryRunQueryOnCall({
        options: {
          query: `SELECT code, color FROM \`hj-reporting.forecast.forecast_nodes\` WHERE sales_predictor_method = 'seasonal'`
        }
      });

      // Get seasonality factor from BigQuery
      bigqueryPromises.push(api.bigQueryRunQueryOnCall({
        options: {
          query: `SELECT * FROM \`hj-reporting.forecast.seasonality\` WHERE forecast_node IN (${forecastNodes.data.map(node => `'${node.code}'`).join(',')})`
        }
      }));

      // Get revenue data
      bigqueryPromises.push(api.bigQueryRunQueryOnCall({
        options: {
          query: `
          WITH
            raw_data AS (
              SELECT
                t.forecast_node,
                t.date,
                -1 * t.amount AS revenue_amt
              FROM
                \`hj-reporting.transactions.transactions_netsuite\` AS t
              WHERE
                t.date >= DATE_SUB(CURRENT_DATE(), INTERVAL 24 MONTH)
                AND t.account = '40100 Gross Revenue'
                AND t.is_posting = TRUE
                AND t.forecast_node IN (${forecastNodes.data.map(node => `'${node.code}'`).join(',')})
            ),
            tagged AS (
              SELECT
                forecast_node,
                CASE
                  WHEN date >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH) THEN 'LY'
                  ELSE '2Y'
                END AS period,
                EXTRACT(MONTH FROM date) AS month,
                revenue_amt
              FROM raw_data
            ),
            monthly_sums AS (
              SELECT
                forecast_node,
                period,
                month,
                SUM(revenue_amt) AS period_month_amt
              FROM tagged
              GROUP BY 1,2,3
            ),
            period_totals AS (
              SELECT
                forecast_node,
                period,
                SUM(period_month_amt) AS period_total_amt
              FROM monthly_sums
              GROUP BY 1,2
            )
          SELECT
            m.forecast_node,
            m.period,
            m.month,
            ROUND(m.period_month_amt, 2) AS revenue,
            ROUND(
              SAFE_DIVIDE(m.period_month_amt, p.period_total_amt) * 100,
              2
            ) AS pct_of_period
          FROM
            monthly_sums AS m
          JOIN
            period_totals AS p
          ON
            m.forecast_node = p.forecast_node
            AND m.period = p.period
          ORDER BY
            m.forecast_node,
            m.period DESC,
            m.month;`
        }
      }));

      const responses = await Promise.all(bigqueryPromises);
      const { data: seasonalityResponse } = responses[0];
      const { data: revenueResponse } = responses[1];

      // Process seasonality data
      const seasonalityMap = {};
      if (seasonalityResponse && seasonalityResponse.length > 0) {
        seasonalityResponse.forEach(row => {
          if (!seasonalityMap[row.forecast_node]) {
            seasonalityMap[row.forecast_node] = {};
          }
          // Use month_id (fallback to month if needed)
          const monthId = row.month_id || row.month;
          seasonalityMap[row.forecast_node][monthId] = row.seasonality_factor || 0;
        });
      }

      // Process revenue data by forecast node
      const nodeData = {};
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      
      // Generate 12 months starting from current month
      const today = new Date();
      const currentMonth = today.getMonth();
      const currentYear = today.getFullYear();
      
      const next12Months = [];
      for (let i = 0; i < 12; i++) {
        const monthDate = new Date(currentYear, currentMonth + i, 1);
        const monthNum = monthDate.getMonth() + 1;
        next12Months.push({
          monthNum: monthNum,
          monthName: monthNames[monthDate.getMonth()]
        });
      }

      if (revenueResponse && revenueResponse.length > 0) {
        revenueResponse.forEach(row => {
          const { forecast_node, period, month, pct_of_period, revenue } = row;
          
          if (!nodeData[forecast_node]) {
            nodeData[forecast_node] = {};
          }
          
          if (!nodeData[forecast_node][month]) {
            nodeData[forecast_node][month] = {
              month: month,
              monthName: monthNames[month - 1]
            };
          }
          
          nodeData[forecast_node][month][period] = pct_of_period || 0;
          nodeData[forecast_node][month][`${period}_revenue`] = revenue || 0;
        });
      }

      // Convert to array format for ag-grid
      const processedNodeData = {};
      
      const allForecastNodes = new Set([
        ...Object.keys(nodeData),
        ...Object.keys(seasonalityMap)
      ]);
      
      allForecastNodes.forEach(forecastNode => {
        const monthData = [];
        next12Months.forEach(monthObj => {
          const monthNum = monthObj.monthNum;
          const monthInfo = nodeData[forecastNode] && nodeData[forecastNode][monthNum] ? 
            nodeData[forecastNode][monthNum] : {
              month: monthNum,
              monthName: monthObj.monthName
            };
          
          monthData.push({
            month: monthNum,
            monthName: monthObj.monthName,
            LY: monthInfo.LY || 0,
            LY_revenue: monthInfo.LY_revenue || 0,
            '2Y': monthInfo['2Y'] || 0,
            '2Y_revenue': monthInfo['2Y_revenue'] || 0,
            seasonality: seasonalityMap[forecastNode] && seasonalityMap[forecastNode][monthNum] ? 
              seasonalityMap[forecastNode][monthNum] : (monthInfo.LY || 0),
            forecastNode: forecastNode
          });
        });
        
        processedNodeData[forecastNode] = monthData;
      });

      setForecastNodeData(processedNodeData);
      setOriginalData(JSON.parse(JSON.stringify(processedNodeData)));
      
      // Validate initial data
      const initialErrors = validateSeasonalityTotals(processedNodeData);
      setValidationErrors(initialErrors);
      
      // Load pending changes from localStorage
      const savedChanges = localStorage.getItem('seasonalityMatrix_pendingChanges');
      console.log('Saved changes from localStorage:', savedChanges);
      
      if (savedChanges) {
        const changes = JSON.parse(savedChanges);
        console.log('Parsed changes:', changes);
        setPendingChanges(changes);
        
        // Apply pending changes to the data
        const updatedData = { ...processedNodeData };
        Object.keys(changes).forEach(changeKey => {
          // Split from the right to handle forecast nodes with underscores
          const parts = changeKey.split('_');
          const field = parts.pop(); // 'seasonality'
          const monthNumber = parts.pop(); // '7'
          const forecastNode = parts.join('_'); // 'US_HJ_DTC_Shopify'
          const monthNum = parseInt(monthNumber);
          
          console.log(`Applying change: ${changeKey} = ${changes[changeKey]}`);
          console.log(`Parsed: forecastNode=${forecastNode}, monthNum=${monthNum}, field=${field}`);
          
          // Find the correct array index based on month number
          if (updatedData[forecastNode]) {
            const monthIndex = updatedData[forecastNode].findIndex(row => row.month === monthNum);
            console.log(`Found month ${monthNum} at index ${monthIndex}`);
            
            if (monthIndex !== -1) {
              console.log(`Before: ${updatedData[forecastNode][monthIndex][field]}`);
              updatedData[forecastNode][monthIndex][field] = changes[changeKey];
              console.log(`After: ${updatedData[forecastNode][monthIndex][field]}`);
            }
          }
        });
        
        // Validate updated data
        const errors = validateSeasonalityTotals(updatedData);
        
        setForecastNodeData(updatedData);
        setValidationErrors(errors);
        setIsDirty(Object.keys(changes).length > 0);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      Modal.error({
        title: 'Error',
        content: `Failed to fetch data: ${error.message}`
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Validate seasonality totals
  const validateSeasonalityTotals = (data) => {
    const errors = {};
    
    Object.keys(data).forEach(forecastNode => {
      const nodeData = data[forecastNode];
      const totalSeasonality = nodeData.reduce((sum, row) => sum + (row.seasonality || 0), 0);
      
      // Round to 2 decimal places to match display precision
      const roundedTotal = Math.round(totalSeasonality * 100) / 100;
      // Flag as error if the rounded total is not exactly 100.00
      if (roundedTotal !== 100) {
        let message;
        if (roundedTotal > 100) {
          message = `Seasonality total is ${roundedTotal.toFixed(2)}% (exceeds 100.00%)`;
        } else {
          message = `Seasonality total is ${roundedTotal.toFixed(2)}% (must be exactly 100.00%)`;
        }
        errors[forecastNode] = {
          total: totalSeasonality,
          message: message
        };
      }
    });
    
    return errors;
  };

  // Custom cell style for validation errors in grand total row
  const getCellStyle = (params) => {
    // Check if this is a grand total row (ag-grid sets node.footer = true for grand total)
    if (params.node && params.node.footer) {
      const forecastNode = params.data?.forecastNode;
      if (forecastNode && validationErrors[forecastNode] && params.colDef.field === 'seasonality') {
        return {
          backgroundColor: '#ff4d4f',
          color: 'white',
          fontWeight: 'bold'
        };
      }
      return { fontWeight: 'bold' };
    }
    
    // Check for pending changes styling
    if (params.data && !params.node?.footer) {
      const monthNumber = params.data.month;
      const field = params.colDef.field;
      const forecastNode = params.data.forecastNode;
      const changeKey = `${forecastNode}_${monthNumber}_${field}`;
      
      if (pendingChanges[changeKey] !== undefined) {
        return {
          backgroundColor: '#e6f3ff',
          border: '2px solid #1890ff',
          fontWeight: 'bold',
          color: 'black'
        };
      }
    }
    
    // Default styling for editable seasonality cells
    if (params.colDef.field === 'seasonality' && !params.node?.footer) {
      return { backgroundColor: '#f5f5f5' };
    }
    
    return {};
  };

  // Calculate total LY revenue for each forecast node
  const calculateTotalLYRevenue = (forecastNode) => {
    const nodeData = forecastNodeData[forecastNode];
    if (!nodeData) return 0;
    
    return nodeData.reduce((total, row) => total + (row.LY_revenue || 0), 0);
  };

  // Column definitions with ag-Grid aggregation
  const columnDefs = useMemo(() => [
    {
      headerName: 'Month',
      field: 'monthName',
      flex: 1,
      minWidth: 70,
      cellStyle: getCellStyle
    },
    {
      headerName: showPercent ? '2Y %' : '2Y $',
      field: showPercent ? '2Y' : '2Y_revenue',
      flex: 1,
      minWidth: 90,
      aggFunc: 'sum', // Enable ag-Grid aggregation
      valueFormatter: params => {
        if (showPercent) {
          return `${(params.value || 0).toFixed(2)}%`;
        } else {
          return `$${Math.round(params.value || 0).toLocaleString()}`;
        }
      },
      cellStyle: getCellStyle
    },
    {
      headerName: showPercent ? 'LY %' : 'LY $',
      field: showPercent ? 'LY' : 'LY_revenue',
      flex: 1,
      minWidth: 90,
      aggFunc: 'sum', // Enable ag-Grid aggregation
      valueFormatter: params => {
        if (showPercent) {
          return `${(params.value || 0).toFixed(2)}%`;
        } else {
          return `$${Math.round(params.value || 0).toLocaleString()}`;
        }
      },
      cellStyle: getCellStyle
    },
    {
      headerName: showPercent ? 'Seasonality %' : 'Seasonality $',
      field: 'seasonality',
      flex: 1,
      minWidth: 100,
      aggFunc: 'sum', // Enable ag-Grid aggregation
      editable: params => !params.node?.footer, // Don't allow editing grand total row
      cellEditor: 'agNumberCellEditor',
      cellEditorParams: {
        min: 0,
        max: 100,
        step: 0.01
      },
      valueFormatter: params => {
        if (showPercent) {
          return `${(params.value || 0).toFixed(2)}%`;
        } else {
          // Calculate dollar amount based on seasonality percentage and total LY revenue
          const forecastNode = params.data?.forecastNode;
          if (forecastNode && forecastNodeData[forecastNode]) {
            const totalLYRevenue = calculateTotalLYRevenue(forecastNode);
            const dollarAmount = (params.value || 0) * totalLYRevenue / 100;
            return `$${Math.round(dollarAmount).toLocaleString()}`;
          }
          return `$0`;
        }
      },
      cellStyle: getCellStyle
    }
  ], [showPercent, pendingChanges, validationErrors, forecastNodeData]);

  // Handle cell value changes
  const onCellValueChanged = (params, forecastNode) => {
    if (params.node?.footer) return; // Don't edit grand total
    
    const updatedData = { ...forecastNodeData };
    const monthIndex = params.node.rowIndex;
    const newValue = params.newValue;
    
    // Get the actual month number from the data (more stable than row index)
    const monthNumber = updatedData[forecastNode][monthIndex].month;
    
    console.log(`Cell changed: ${forecastNode}, monthIndex=${monthIndex}, monthNumber=${monthNumber}, newValue=${newValue}`);
    
    // Update the data
    updatedData[forecastNode][monthIndex].seasonality = newValue;
    
    // Track the change using month number instead of row index
    const changeKey = `${forecastNode}_${monthNumber}_seasonality`;
    const newPendingChanges = { ...pendingChanges };
    newPendingChanges[changeKey] = newValue;
    
    console.log(`Storing change with key: ${changeKey} = ${newValue}`);
    console.log('All pending changes:', newPendingChanges);
    
    // Validate seasonality totals
    const errors = validateSeasonalityTotals(updatedData);
    
    // Save to localStorage
    localStorage.setItem('seasonalityMatrix_pendingChanges', JSON.stringify(newPendingChanges));
    console.log('Saved to localStorage:', JSON.stringify(newPendingChanges));
    
    setForecastNodeData(updatedData);
    setPendingChanges(newPendingChanges);
    setIsDirty(Object.keys(newPendingChanges).length > 0);
    setValidationErrors(errors);
  };

  // Clear pending changes
  const clearPendingChanges = () => {
    setPendingChanges({});
    localStorage.removeItem('seasonalityMatrix_pendingChanges');
    setIsDirty(false);
    setValidationErrors({});
    if (originalData) {
      setForecastNodeData(JSON.parse(JSON.stringify(originalData)));
      const errors = validateSeasonalityTotals(originalData);
      setValidationErrors(errors);
    }
  };

  // Save changes
  const saveChanges = async () => {
    console.log('saveChanges function called');
    console.log('pendingChanges:', pendingChanges);
    
    setSaving(true);
    
    try {
      console.log('Starting to process pending changes');
      
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      
      // Get all forecast nodes that have seasonal data
      const forecastNodesResponse = await api.bigQueryRunQueryOnCall({
        options: {
          query: `SELECT code FROM \`hj-reporting.forecast.forecast_nodes\` WHERE sales_predictor_method = 'seasonal'`
        }
      });
      
      console.log('Forecast nodes:', forecastNodesResponse);
      
      const forecastNodes = forecastNodesResponse.data || [];
      
      // Create a complete dataset for all forecast nodes and months
      const seasonalityRows = [];
      
      forecastNodes.forEach(node => {
        const forecastNode = node.code;
        
        // For each month (1-12)
        for (let monthNum = 1; monthNum <= 12; monthNum++) {
          const changeKey = `${forecastNode}_${monthNum}_seasonality`;
          
          let seasonalityFactor = 0;
          
          // If there's a pending change, use that value
          if (pendingChanges[changeKey] !== undefined) {
            seasonalityFactor = pendingChanges[changeKey];
          } else {
            // Otherwise, get the current value from the displayed data
            const nodeData = forecastNodeData[forecastNode];
            if (nodeData) {
              const monthData = nodeData.find(row => row.month === monthNum);
              if (monthData) {
                seasonalityFactor = monthData.seasonality || 0;
              }
            }
          }
          
          // Create the record
          seasonalityRows.push({
            forecast_node: forecastNode,
            month_id: monthNum,
            month_name: monthNames[monthNum - 1],
            seasonality_factor: seasonalityFactor
          });
        }
      });

      console.log('Final seasonality rows to save:', seasonalityRows);
      console.log('Calling bigQueryReplaceTableOnCall with:', {
        datasetId: 'forecast',
        tableId: 'seasonality',
        rows: seasonalityRows
      });

      const result = await api.bigQueryReplaceTableOnCall({
        datasetId: 'forecast',
        tableId: 'seasonality',
        rows: seasonalityRows
      });

      console.log('API result:', result);

      if (result.data && result.data.success) {
        console.log('Success! Clearing pending changes');
        // Clear pending changes from localStorage
        setPendingChanges({});
        localStorage.removeItem('seasonalityMatrix_pendingChanges');
        setIsDirty(false);
        setValidationErrors({});
        
        Modal.success({
          title: 'Success',
          content: `Seasonality data saved successfully! ${Object.keys(pendingChanges).length} changes applied.`
        });
        
        // Reload fresh data from BigQuery
        await fetchData();
      } else {
        console.error('API call failed:', result);
        throw new Error(result.data?.error || 'Unknown error occurred');
      }
    } catch (error) {
      console.error('Error saving changes:', error);
      Modal.error({
        title: 'Error',
        content: `Failed to save changes: ${error.message}`
      });
    } finally {
      setSaving(false);
    }
  };

  const getPendingChangesSummary = () => {
    const changeCount = Object.keys(pendingChanges).length;
    const affectedNodes = new Set();
    
    Object.keys(pendingChanges).forEach(key => {
      const [forecastNode] = key.split('_');
      affectedNodes.add(forecastNode);
    });
    
    return {
      totalChanges: changeCount,
      affectedNodeCount: affectedNodes.size
    };
  };

  return (
    <Layout style={{ width: '100%', height: '100vh', overflow: 'auto' }}>
      <Layout.Content style={{ width: '100%', height: '100%', padding: 20, maxWidth: '100%' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Title level={2}>Seasonality Matrix</Title>
              {Object.keys(validationErrors).length > 0 && (
                <div style={{ marginBottom: '10px', padding: '8px 12px', backgroundColor: '#fff2f0', border: '1px solid #ff4d4f', borderRadius: '4px' }}>
                  <span style={{ color: '#ff4d4f', fontSize: '14px', fontWeight: 'bold' }}>
                    ⚠️ Validation Errors: {Object.keys(validationErrors).length} forecast nodes have seasonality totals that are not exactly 100%
                  </span>
                </div>
              )}
            </div>
            <Space>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span>$</span>
                <Switch 
                  checked={showPercent}
                  onChange={setShowPercent}
                  size="small"
                />
                <span>%</span>
              </div>
              {console.log('isDirty:', isDirty, 'pendingChanges:', pendingChanges)}
              {isDirty && (
                <>
                  <Button 
                    onClick={clearPendingChanges} 
                    size="large"
                    loading={saving}
                    disabled={saving}
                  >
                    Clear Changes
                  </Button>
                  <Button 
                    type="primary" 
                    onClick={() => {
                      console.log('Save button clicked!');
                      saveChanges();
                    }} 
                    size="large"
                    loading={saving}
                    disabled={saving}
                  >
                    Save to BigQuery ({getPendingChangesSummary().totalChanges})
                  </Button>
                </>
              )}
            </Space>
          </div>

          {(loading || saving) ? (
            <div style={{ textAlign: 'center', padding: '50px' }}>
              {saving ? 'Saving to BigQuery...' : 'Loading...'}
            </div>
          ) : (
            <Row gutter={[16, 16]} style={{ width: '100%' }}>
              {Object.keys(forecastNodeData).map(forecastNode => {
                const nodeData = forecastNodeData[forecastNode];
                const hasValidationError = validationErrors[forecastNode];

                return (
                  <Col xs={24} sm={24} md={12} lg={12} xl={8} xxl={6} key={forecastNode}>
                    <Card
                      title={
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          {forecastNode}
                          {hasValidationError && (
                            <span style={{ color: '#ff4d4f', fontSize: '12px' }}>
                              ⚠️ Not 100%
                            </span>
                          )}
                        </div>
                      }
                      size="small"
                      style={{ height: '100%' }}
                    >
                      <div style={{ width: '100%' }}>
                        <AgGridReact
                          key={`${forecastNode}-${showPercent}`}
                          columnDefs={columnDefs}
                          rowData={nodeData}
                          grandTotalRow="bottom"
                          domLayout="autoHeight"
                          defaultColDef={{
                            resizable: false,
                            sortable: false
                          }}
                          onCellValueChanged={(params) => onCellValueChanged(params, forecastNode)}
                          headerHeight={25}
                          rowHeight={30}
                          theme={themeBalham}
                        />
                      </div>
                    </Card>
                  </Col>
                );
              })}
            </Row>
          )}
        </Space>
      </Layout.Content>
    </Layout>
  );
};

export default SeasonalityMatrix;

