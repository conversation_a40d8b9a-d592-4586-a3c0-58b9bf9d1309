import React, { useState } from 'react';
import { Select, Upload, Button, Typography } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { api } from './firebase';

const { Option } = Select;
const { Title } = Typography;
const uploadMap = {
  'TikTok Sales Data': {
    filetype: 'csv',
    uniqueKey: 'orderId',
    columns: ['orderId', 'product', 'quantity', 'price', 'total'],
  },
  'Shopify Customers Over Time': {
    filetype: 'csv',
    columns: ['customerId', 'name', 'email', 'phone'],
    keyCols: ['day', 'customer_type']
  },

};

const UploadData = () => {
  const [uploadType, setUploadType] = useState(null);
  const [uploadedFile, setUploadedFile] = useState(null);


  const handleUploadTypeChange = (value) => {
    // console.log('Upload Type:', value);
    setUploadType(value);
  };

  const handleFileChange = (info) => {
    // Handle file change
    setUploadedFile(info.file);
  };

  const handleUpload = () => {
    // Handle upload
    // console.log('File:', uploadedFile);
    const reader = new FileReader();
    const keyCol = uploadMap[uploadType].keyCols.join('_');
    reader.onload = (e) => {
      const csvData = e.target.result;
      // console.log('CSV Data:', csvData);
      const rows = csvData.split('\n');
      const headers = rows[0].split(',');
      // console.log('Headers:', headers);
      let data = rows.slice(1).map((row) => {
        const rowValues = row.split(',');
        const rowObj = headers.reduce((acc, header, index) => {
          acc[header] = rowValues[index];
          return acc;
        }, {});
        if (!rowObj[keyCol]) {
          rowObj[keyCol] = uploadMap[uploadType].keyCols.map((col) => rowObj[col]).join('_');
        }
        return rowObj;
      });

      data.pop();
      // console.log('Data:', data);
      api.upsertOnCall({ datasetId: 'sales', table: 'shopify customers over time', key: keyCol, rows: data });
      // Further processing of CSV data can be done here
    };
    reader.readAsText(uploadedFile);
    // parse the file
  };

  return (
    <div>
      <Title level={1}>Upload Data</Title>
      <label htmlFor="uploadType">Upload Type:</label>
      <Select id="uploadType" value={uploadType} onChange={handleUploadTypeChange} style={{ width: 250 }}
        options={Object.keys(uploadMap).map((key) => ({ value: key, label: key }))}
      />
      {uploadType && (
        <Upload accept={uploadMap[uploadType].filetype} beforeUpload={() => false} onChange={handleFileChange}>
          <Button icon={<UploadOutlined />}>Select File</Button>
        </Upload>
      )}
      {uploadedFile && (
        <Button type="primary" onClick={handleUpload} style={{ marginTop: 16 }}>
          Upload
        </Button>
      )}
    </div>
  );
};

export default UploadData;