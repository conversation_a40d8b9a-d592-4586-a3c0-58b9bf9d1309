import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Grid, Paper, CircularProgress, Alert, FormControl, Autocomplete, TextField } from '@mui/material';
import {
    Composed<PERSON>hart, Line, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { useFormatAxisTick } from '../../hooks/useFormatAxisTick';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import ChartExportWrapper from './ChartExportWrapper';

const DIOReport = () => {
    const [dioData, setDioData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [selectedUPC, setSelectedUPC] = useState(null);
    const [lastCompleteMonth, setLastCompleteMonth] = useState('');
    const [earliestDataMonth, setEarliestDataMonth] = useState('');
    const [totalDays, setTotalDays] = useState(0);
    const formatYAxisTick = useFormatAxisTick();

    // Fetch both DIO data and KPI goals
    const fetchData = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        try {
            const functions = getFunctions();
            const getDIOData = httpsCallable(functions, 'getDIOData');
            const getKPIGoalsForReport = httpsCallable(functions, 'getKPIGoalsForReport');

            const [dioResult, goalsResult] = await Promise.all([
                getDIOData(),
                getKPIGoalsForReport({ reportName: 'dio' })
            ]);

            setDioData(dioResult.data);
            setLastCompleteMonth(dioResult.data.lastCompleteMonth);
            setEarliestDataMonth(dioResult.data.earliestDataMonth);
            setTotalDays(dioResult.data.totalDays);

            if (goalsResult.data.success) {
                setKpiGoals(goalsResult.data.data);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            setError('Failed to fetch data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            const dio = payload.find(p => p.dataKey === 'dio')?.value;
            const cogs = payload.find(p => p.dataKey === 'cogs')?.value;
            const averageInventory = payload.find(p => p.dataKey === 'averageInventory')?.value;

            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                    <Typography variant="body2">{`Month: ${label}`}</Typography>
                    {dio !== undefined && (
                        <Typography variant="body2" color="primary">
                            {`DIO: ${dio.toFixed(2)} days`}
                        </Typography>
                    )}
                    {cogs !== undefined && (
                        <Typography variant="body2" color="secondary">
                            {`COGS: $${cogs.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                        </Typography>
                    )}
                    {averageInventory !== undefined && (
                        <Typography variant="body2" color="error">
                            {`Avg Inventory Cost: $${averageInventory.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                        </Typography>
                    )}
                </Paper>
            );
        }
        return null;
    };

    const getChartData = () => {
        if (!dioData) return [];
        let data = !selectedUPC ? dioData.dioData :
            Object.entries(dioData.dioByUPC[selectedUPC]?.monthlyData || {})
                .map(([date, data]) => ({
                    date,
                    dio: data.dio,
                    cogs: data.cogs,
                    averageInventory: data.averageInventory
                }));
        return data.filter(item => item.date >= earliestDataMonth && item.date <= lastCompleteMonth);
    };

    const calculateTotals = (data) => {
        return data.reduce((acc, item) => {
            acc.totalCOGS += item.cogs || 0;
            acc.totalAverageInventory += item.averageInventory || 0;
            return acc;
        }, { totalCOGS: 0, totalAverageInventory: 0 });
    };

    const getDIOValue = () => {
        if (!selectedUPC) {
            return dioData?.overallDIO;
        }
        const upcData = dioData?.dioByUPC[selectedUPC];
        return upcData ? upcData.dio : null;
    };

    const calculateDIOTrend = () => {
        const chartData = getChartData();
        if (chartData.length < 2) return 0;
        const lastTwo = chartData.slice(-2);
        return lastTwo[1].dio - lastTwo[0].dio;
    };

    const calculateDomains = (data) => {
        if (!data.length) return { dioDomain: [0, 100], amountDomain: [0, 1000] };

        // Calculate DIO domain
        const dioValues = data.map(item => item.dio);
        const maxDIO = Math.max(...dioValues);
        const minDIO = Math.min(...dioValues);

        // Include goal values in DIO domain calculation
        const goalValue = kpiGoals?.['DIO']?.value;
        const goalMin = kpiGoals?.['DIO']?.min;
        const goalMax = kpiGoals?.['DIO']?.max;

        const allDIOValues = [
            ...dioValues,
            goalValue && parseFloat(goalValue),
            goalMin && parseFloat(goalMin),
            goalMax && parseFloat(goalMax)
        ].filter(Boolean);

        const dioDomain = [
            Math.max(0, Math.floor(Math.min(...allDIOValues) * 0.9)),
            Math.ceil(Math.max(...allDIOValues) * 1.1)
        ];

        // Calculate amount domain for COGS and Average Inventory
        const cogsValues = data.map(item => item.cogs || 0);
        const inventoryValues = data.map(item => item.averageInventory || 0);
        const maxAmount = Math.max(...cogsValues, ...inventoryValues);
        const minAmount = Math.min(...cogsValues, ...inventoryValues);

        const amountDomain = [
            minAmount < 0 ? Math.floor(minAmount * 1.1) : 0,
            Math.ceil(maxAmount * 1.1)
        ];

        return { dioDomain, amountDomain };
    };

    // Use the KPI reference lines hook
    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: kpiGoals?.['DIO'],
        yAxisId: "left",
        styles: {
            stroke: "#ff9800",
            strokeDasharray: "3 3",
            label: {
                fill: "#ff9800",
                fontSize: 12,
                position: "right"
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Min",
            max: "Max"
        }
    });

    if (isLoading) return <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}><CircularProgress /></Box>;
    if (error) return <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>;
    if (!dioData || !kpiGoals) return null;

    const chartData = getChartData();
    const { dioDomain, amountDomain } = calculateDomains(chartData);
    const { totalCOGS, totalAverageInventory } = calculateTotals(chartData);
    const upcOptions = [{ label: 'Overall', value: null }, ...Object.keys(dioData.dioByUPC).map(upc => ({ label: upc, value: upc }))];
    const dioValue = getDIOValue();
    const dioConfig = kpiGoals['DIO'];

    return (
        <ChartExportWrapper title={`DIO_Report${selectedUPC ? `_${selectedUPC}` : ''}`}>
            <Box sx={{ p: { xs: 2, sm: 3 }, mb: 3 }}>
                <FormControl sx={{ mb: 2, width: '300px' }}>
                    <Autocomplete
                        options={upcOptions}
                        getOptionLabel={(option) => option.label}
                        renderInput={(params) => <TextField {...params} label="Select UPC" size="small" />}
                        value={upcOptions.find(option => option.value === selectedUPC) || upcOptions[0]}
                        onChange={(event, newValue) => {
                            setSelectedUPC(newValue ? newValue.value : null);
                        }}
                        size="small"
                    />
                </FormControl>

                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <ResponsiveContainer width="100%" height={500}>
                        <ComposedChart data={chartData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis
                                yAxisId="left"
                                domain={dioDomain}
                                label={{ value: 'DIO (Days)', angle: -90, position: 'insideLeft' }}
                            />
                            <YAxis
                                yAxisId="right"
                                orientation="right"
                                domain={amountDomain}
                                label={{ value: 'Amount', angle: 90, position: 'insideRight' }}
                                tickFormatter={formatYAxisTick}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            {goalReferenceLines}
                            <Bar yAxisId="right" dataKey="cogs" fill="#82ca9d" name="COGS" />
                            <Bar yAxisId="right" dataKey="averageInventory" fill="#ffc658" name="Avg Inventory Cost" />
                            <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="dio"
                                stroke="#8884d8"
                                strokeWidth={3}
                                dot={{ r: 4, fill: "#8884d8" }}
                                activeDot={{ r: 8 }}
                                name="DIO"
                            />
                        </ComposedChart>
                    </ResponsiveContainer>

                    <Grid container spacing={2} justifyContent="center" sx={{ mt: 2 }}>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <KPICard
                                title={!selectedUPC ? 'Overall DIO' : `UPC ${selectedUPC} DIO`}
                                value={dioValue !== null && dioValue !== undefined
                                    ? `${dioValue.toFixed(2)} days`
                                    : 'N/A'}
                                bgColor="#f0f4ff"
                                textColor="primary"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <KPICard
                                title="Total COGS"
                                value={`$${totalCOGS.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                                bgColor="#f0fff0"
                                textColor="secondary"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <KPICard
                                title="Total Avg Inventory Cost"
                                value={`$${totalAverageInventory.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                                bgColor="#fff0f0"
                                textColor="error"
                            />
                        </Grid>
                    </Grid>
                </Paper>

                <GoalStatusDisplay
                    currentValue={dioValue}
                    goalConfig={dioConfig}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculateDIOTrend()}
                    size="medium"
                    title="DIO Performance"
                />
            </Box>
        </ChartExportWrapper>
    );
};

export default DIOReport;