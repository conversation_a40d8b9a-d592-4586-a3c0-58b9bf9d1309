---
description: UI component and styling guidelines using antd, ag-grid
globs: 
alwaysApply: false
---
# UI Components and Styling

## UI Framework
- Use `antd` for components
- Use `antd-icons` for icons
- Use `ag-grid-react` for tables

## Loading Components
Use the `<Spin>` component to handle loading states:

```tsx
<Spin spinning={isLoading}>
  {data && <MyComponent data={data} />}
</Spin>
```

## Form Components
### Email Input
```tsx
<Input
  type="email"
  placeholder="Enter email"
/>
```

### Text Area
```tsx
<Input
  type="text"
  autosizeTextarea
  rows={3}
  name="message"
  placeholder="Paste in email content"
  registerProps={register("message", { required: true })}
  error={errors.message}
/>
``` 
### Select
```tsx
<Select
  options={options}
  placeholder="Select an option"
/>
```

### Checkbox
```tsx
<Checkbox
  label="Checkbox"
/>
```
## Tables or Grids
```tsx
import { AgGridReact } from 'ag-grid-react';

<AgGridReact
  rowData={data}
  columnDefs={columns}
/>
```