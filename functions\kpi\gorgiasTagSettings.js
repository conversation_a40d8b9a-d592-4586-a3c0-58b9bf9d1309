const {onCall} = require("firebase-functions/v2/https");
const admin = require("firebase-admin");

const defaultTagMappings = {
  "Perfect Order Rate": {
    category: "Order Management KPIs",
    description: "((Total Orders - Quality Issue Orders) / Total Orders) * 100",
    tags: {
      perfectOrders: [ // Tags that indicate non-perfect orders
        "QC 32 OZ SPORT LEAKING",
        "QC SPORT 64oz LEAK",
        "QC LEAKING 64oz SPORT",
        "QC SPORT LID HANDLE BROKE",
        "QC SPORT LID SCRATCHED",
        "QC SPORT LID FADING",
        "QC SPORT HANDLE BROKE",
        "QC SPORT RATTLE",
        "QC SPORT SUCTION",
        "QC SPORT ROUGH EDGE",
        "QC MISSING SPORT STRAW",
        "QC SPORT MISSING STRAW",
        "QC TRAVELER MISSING LID",
        "QC TRAVELER STICKER",
        "QC TRAVELER SLEEVE SEAM",
        "QC TRAVELER SLEEVE ZIPPER",
        "QC TRAVELER LID MELTED",
        "QC TRAVELR LID STUCK",
        "QC SPORT BASE",
        "QC SHKR BASE",
        "QC CROOKED ETCH",
        "QC MONSTERA SCRATCHED",
        "QC RED SHINE SCRATCHED",
        "QC SPORT LOGO",
        "QC MOLD FULL REPLACEMENT",
      ],
    },
  },
  "Order Accuracy Rate": {
    category: "Order Management KPIs",
    description: "((Total Orders - Issue Orders) / Total Orders) * 100",
    tags: {
      accurateOrders: [ // Tags that indicate inaccurate orders
        "QC SPORT LID SCRATCHED",
        "QC SPORT LID FADING",
        "QC MONSTERA SCRATCHED",
        "QC RED SHINE SCRATCHED",
        "QC SPORT LOGO",
        "QC CROOKED ETCH",
        "QC MISSING SPORT STRAW",
        "QC SPORT MISSING STRAW",
        "QC TRAVELER MISSING LID",
        "QC SPORT LID HANDLE BROKE",
        "QC SPORT HANDLE BROKE",
        "QC SPORT BASE",
        "QC SHKR BASE",
        "QC MOLD FULL REPLACEMENT",
      ],
    },
  },
  "Supplier Defect Rate": {
    category: "Supplier Management KPIs",
    description: "(Number of Defective Items / Total Items Received) * 100",
    tags: {
      defectiveItems: [ // Tags that indicate defective items
        "QC MOLD FULL REPLACEMENT",
        "QC SPORT BASE",
        "QC SHKR BASE",
        "QC SPORT LID HANDLE BROKE",
        "QC SPORT HANDLE BROKE",
        "QC 32 OZ SPORT LEAKING",
        "QC SPORT 64oz LEAK",
        "QC LEAKING 64oz SPORT",
        "QC SPORT RATTLE",
        "QC SPORT SUCTION",
        "QC SPORT ROUGH EDGE",
      ],
    },
  },
  "Supplier Quality Score": {
    category: "Supplier Management KPIs",
    description: "((Total Items - Poor Quality Items) / Total Items) * 100",
    tags: {
      nonDefectiveItems: [ // Tags that indicate poor quality items
        "QC MOLD FULL REPLACEMENT",
        "QC SPORT BASE",
        "QC SHKR BASE",
        "QC SPORT LID SCRATCHED",
        "QC SPORT LID FADING",
        "QC MONSTERA SCRATCHED",
        "QC RED SHINE SCRATCHED",
        "QC SPORT LOGO",
        "QC CROOKED ETCH",
        "QC SPORT LID HANDLE BROKE",
        "QC SPORT HANDLE BROKE",
        "QC 32 OZ SPORT LEAKING",
        "QC SPORT 64oz LEAK",
        "QC LEAKING 64oz SPORT",
      ],
    },
  },
};


// Function to get Gorgias tag mappings
exports.getGorgiasTagMappings = onCall({
  enforceAppCheck: false,
  maxInstances: 10,
}, async () => {
  try {
    const doc = await admin
        .firestore()
        .collection("settings")
        .doc("gorgiasTagMappings")
        .get();

    if (!doc.exists) {
      // console.log("No existing mappings found, initializing with defaults");
      // Initialize with default mappings if none exist
      await admin
          .firestore()
          .collection("settings")
          .doc("gorgiasTagMappings")
          .set(defaultTagMappings);

      return {result: defaultTagMappings};
    }

    const data = doc.data();
    // console.log("Retrieved data:", data);

    // Check if data is empty or missing tags
    // eslint-disable-next-line max-len
    if (!data || !Object.keys(data).length || !(data["Perfect Order Rate"] && data["Perfect Order Rate"].tags && data["Perfect Order Rate"].tags.perfectOrders && data["Perfect Order Rate"].tags.perfectOrders.length)) {
      // console.log("Empty or invalid data found, resetting to defaults");
      await admin
          .firestore()
          .collection("settings")
          .doc("gorgiasTagMappings")
          .set(defaultTagMappings);

      return {result: defaultTagMappings};
    }

    return {result: data};
  } catch (error) {
    console.error("Error getting Gorgias tag mappings:", error);
    throw new Error("Error getting Gorgias tag mappings");
  }
});

// Function to update Gorgias tag mappings
exports.updateGorgiasTagMappings = onCall({
  enforceAppCheck: false,
  maxInstances: 10,
}, async (request) => {
  try {
    const {tagMappings} = request.data;
    if (!tagMappings) {
      throw new Error("Missing tag mappings data in request");
    }

    // Validate the structure of tag mappings
    const requiredKPIs = Object.keys(defaultTagMappings);
    const receivedKPIs = Object.keys(tagMappings);

    const missingKPIs = requiredKPIs.filter(
        (kpi) => !receivedKPIs.includes(kpi),
    );

    if (missingKPIs.length > 0) {
      throw new Error(`Missing required KPIs: ${missingKPIs.join(", ")}`);
    }

    await admin
        .firestore()
        .collection("settings")
        .doc("gorgiasTagMappings")
        .set(tagMappings);

    return {
      success: true,
      message: "Gorgias tag mappings updated successfully",
      result: tagMappings,
    };
  } catch (error) {
    console.error("Error updating Gorgias tag mappings:", error);
    throw new Error(error.message || "Error updating Gorgias tag mappings");
  }
});

// Function to reset Gorgias tag mappings to defaults
exports.resetGorgiasTagMappings = onCall({
  enforceAppCheck: false,
  maxInstances: 10,
}, async () => {
  try {
    // console.log("Resetting to defaults:", defaultTagMappings);

    await admin
        .firestore()
        .collection("settings")
        .doc("gorgiasTagMappings")
        .set(defaultTagMappings);

    return {
      success: true,
      message: "Gorgias tag mappings reset to defaults successfully",
      result: defaultTagMappings,
    };
  } catch (error) {
    console.error("Error resetting Gorgias tag mappings:", error);
    throw new Error("Error resetting Gorgias tag mappings");
  }
});
