import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Grid, Paper, CircularProgress, Autocomplete, TextField, Alert } from '@mui/material';
import {
    ComposedChart, Line, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Bar<PERSON>hart, LabelList
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import ChartExportWrapper from './ChartExportWrapper';

const ReturnRateReport = () => {
    const [returnRateData, setReturnRateData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [filteredChartData, setFilteredChartData] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [selectedProductType, setSelectedProductType] = useState(null);
    const [productTypes, setProductTypes] = useState([]);
    const [summaryData, setSummaryData] = useState(null);

    const fetchData = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        // Reset all data states
        setReturnRateData(null);
        setFilteredChartData(null);
        setSummaryData(null);
        setProductTypes([]);
        setSelectedProductType(null);

        try {
            const functions = getFunctions();
            const getReturnRate = httpsCallable(functions, 'getReturnRateV2');
            const getKPIGoalsForReport = httpsCallable(functions, 'getKPIGoalsForReport');

            const [returnRateResult, goalsResult] = await Promise.all([
                getReturnRate(),
                getKPIGoalsForReport({ reportName: 'return-rate' })
            ]);

            if (returnRateResult.data && returnRateResult.data.monthlyData && returnRateResult.data.productTypeReturnRates) {
                const result = returnRateResult.data;
                setReturnRateData(result);
                setFilteredChartData(result.monthlyData);
                setSummaryData({
                    overallReturnRate: result.overallReturnRate,
                    totalReturns: result.totalReturns,
                    totalSold: result.totalSold
                });

                const uniqueProductTypes = ['Select All', ...result.productTypeReturnRates.map(item => item.productType)];
                setProductTypes(uniqueProductTypes);
            }

            if (goalsResult.data.success) {
                setKpiGoals(goalsResult.data.data);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            setError(`Failed to fetch data: ${error.message}`);
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    useEffect(() => {
        if (returnRateData) {
            if (selectedProductType && selectedProductType !== 'Select All') {
                const productData = returnRateData.productTypeReturnRates.find(pt => pt.productType === selectedProductType);
                if (productData) {
                    setFilteredChartData(productData.monthlyData);
                    setSummaryData({
                        overallReturnRate: productData.returnRate.toFixed(2),
                        totalReturns: productData.returns,
                        totalSold: productData.sold
                    });
                }
            } else {
                setFilteredChartData(returnRateData.monthlyData);
                setSummaryData({
                    overallReturnRate: returnRateData.overallReturnRate,
                    totalReturns: returnRateData.totalReturns,
                    totalSold: returnRateData.totalSold
                });
            }
        }
    }, [selectedProductType, returnRateData]);

    const calculateReturnRateTrend = () => {
        if (!filteredChartData || filteredChartData.length < 2) return 0;
        const lastTwo = filteredChartData.slice(-2);
        return lastTwo[1].returnRate - lastTwo[0].returnRate;
    };

    const handleProductTypeChange = (event, newValue) => {
        setSelectedProductType(newValue);
    };

    const calculateDomains = (data) => {
        if (!data) return { rateDomain: [0, 10], quantityDomain: [0, 100] };

        const rateValues = data.map(item => item.returnRate);
        const quantityValues = data.map(item => item.returns);

        // Include goal values in rate domain calculation
        const goalMin = kpiGoals?.['Return Rate']?.min;
        const goalMax = kpiGoals?.['Return Rate']?.max;

        const allRateValues = [
            ...rateValues,
            goalMin && parseFloat(goalMin),
            goalMax && parseFloat(goalMax)
        ].filter(Boolean);

        return {
            rateDomain: [
                0,
                Math.ceil(Math.max(...allRateValues) * 1.2) // Add 20% padding
            ],
            quantityDomain: [0, Math.ceil(Math.max(...quantityValues) * 1.1)]
        };
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            const returnRate = payload.find(p => p.name === 'Return Rate (%)');
            const returns = payload.find(p => p.name === 'Returns Quantity');
            const totalSold = payload[0].payload.sold;

            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                    <Typography variant="body2">{`Month: ${label}`}</Typography>
                    {returnRate && (
                        <Typography variant="body2" color="primary">
                            {`Return Rate: ${returnRate.value.toFixed(2)}%`}
                        </Typography>
                    )}
                    {returns && (
                        <Typography variant="body2" color="error">
                            {`Returns: ${returns.value.toLocaleString()}`}
                        </Typography>
                    )}
                    {totalSold && (
                        <Typography variant="body2" color="success.main">
                            {`Total Sold: ${totalSold.toLocaleString()}`}
                        </Typography>
                    )}
                </Paper>
            );
        }
        return null;
    };

    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: kpiGoals?.['Return Rate'],
        yAxisId: "left",
        styles: {
            stroke: "#ff9800",
            strokeDasharray: "3 3",
            label: {
                fill: "#ff9800",
                fontSize: 12,
                position: "right"
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Target Min",
            max: "Target Max"
        }
    });

    if (isLoading) return <CircularProgress />;
    if (error) return <Alert severity="error">{error}</Alert>;
    if (!returnRateData || !summaryData || !kpiGoals) return <Alert severity="info">No data available</Alert>;

    const { rateDomain, quantityDomain } = calculateDomains(filteredChartData);
    const topProductTypes = returnRateData.productTypeReturnRates
        .sort((a, b) => b.returnRate - a.returnRate)
        .slice(0, 10);

    const returnRateConfig = kpiGoals['Return Rate'];

    return (
        <ChartExportWrapper title={`Return_Rate${selectedProductType ? `_${selectedProductType}` : ''}`}>

            <Box sx={{ p: { xs: 2, sm: 3 }, mb: 3 }}>
                <Grid container spacing={2} alignItems="center" sx={{ mb: 3 }}>
                    <Grid item xs={12} sm={6} md={4}>
                        <Autocomplete
                            options={productTypes}
                            value={selectedProductType}
                            onChange={handleProductTypeChange}
                            renderInput={(params) => <TextField {...params} label="Filter by Product Type" />}
                            fullWidth
                        />
                    </Grid>
                </Grid>

                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <ResponsiveContainer width="100%" height={400}>
                        <ComposedChart data={filteredChartData}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                            <XAxis dataKey="month" />
                            <YAxis
                                yAxisId="left"
                                orientation="left"
                                stroke="#8884d8"
                                domain={rateDomain}
                                label={{ value: 'Return Rate (%)', angle: -90, position: 'insideLeft' }}
                            />
                            <YAxis
                                yAxisId="right"
                                orientation="right"
                                stroke="#ff7675"
                                domain={quantityDomain}
                                label={{ value: 'Returns Quantity', angle: 90, position: 'insideRight' }}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            {goalReferenceLines}
                            <Bar
                                yAxisId="right"
                                dataKey="returns"
                                fill="#ff7675"
                                name="Returns Quantity"
                                opacity={0.8}
                                barSize={20}
                            />
                            <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="returnRate"
                                name="Return Rate (%)"
                                stroke="#8884d8"
                                strokeWidth={3}
                                dot={{ r: 4 }}
                                activeDot={{ r: 8 }}
                            />
                        </ComposedChart>
                    </ResponsiveContainer>
                </Paper>

                <Grid container spacing={2} sx={{ mb: 4 }}>
                    <Grid item xs={12} sm={6} md={4}>
                        <KPICard
                            title={selectedProductType && selectedProductType !== 'Select All'
                                ? `${selectedProductType} Return Rate`
                                : 'Overall Return Rate'}
                            value={`${summaryData.overallReturnRate}%`}
                            bgColor="#f0f4ff"
                            textColor="primary"
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                        <KPICard
                            title={selectedProductType && selectedProductType !== 'Select All'
                                ? `${selectedProductType} Returned Quantity`
                                : 'Total Returned Quantity'}
                            value={summaryData.totalReturns.toLocaleString()}
                            bgColor="#fff5f5"
                            textColor="error"
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                        <KPICard
                            title={selectedProductType && selectedProductType !== 'Select All'
                                ? `${selectedProductType} Quantity Sold`
                                : 'Total Quantity Sold'}
                            value={summaryData.totalSold.toLocaleString()}
                            bgColor="#e8f5e9"
                            textColor="success.main"
                        />
                    </Grid>
                </Grid>


                <GoalStatusDisplay
                    currentValue={parseFloat(summaryData.overallReturnRate)}
                    goalConfig={returnRateConfig}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculateReturnRateTrend()}
                    size="medium"
                    title={selectedProductType && selectedProductType !== 'Select All'
                        ? `${selectedProductType} Return Rate Performance`
                        : "Overall Return Rate Performance"
                    }
                />


                <Box sx={{ mt: 4 }}>
                    <Typography variant="h6" gutterBottom>Top 10 Product Types by Return Rate</Typography>
                    <ResponsiveContainer width="100%" height={400}>
                        <BarChart
                            data={topProductTypes}
                            layout="vertical"
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis type="number" />
                            <YAxis dataKey="productType" type="category" width={150} />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="returnRate" fill="#8884d8" name="Return Rate (%)">
                                <LabelList dataKey="returnRate" position="right" formatter={(value) => `${value.toFixed(2)}%`} />
                            </Bar>
                        </BarChart>
                    </ResponsiveContainer>
                </Box>
            </Box>
        </ChartExportWrapper>
    );
};

export default ReturnRateReport;