import React from 'react';
import { Card, Typography } from 'antd';
// import { useTheme } from 'antd';

const { Title, Text } = Typography;

const KPICard = ({ title, value, bgColor = '#f0f4ff', textColor = 'primary' }) => {
    // const { token } = useTheme();

    // Function to determine font size based on value length
    const getValueFontSize = () => {
        if (value.length > 10) {
            return { fontSize: '1.12rem' };
        } else if (value.length > 7) {
            return { fontSize: '1.26rem' };
        }
        return { fontSize: '1.4rem' };
    };

    return (
        <Card
            style={{
                backgroundColor: bgColor,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                textAlign: 'center',
                padding: '8px 12px'
            }}
        >
            <div>
                <Text
                    type="secondary"
                    style={{
                        fontSize: '0.7rem',
                        lineHeight: 1.2,
                        marginBottom: '4px',
                        display: 'block'
                    }}
                >
                    {title}
                </Text>
                <Title
                    level={4}
                    style={{
                        ...getValueFontSize(),
                        lineHeight: 1.2,
                        wordBreak: 'break-word',
                        margin: 0,
                        color: textColor === 'primary' ? token.colorPrimary : textColor
                    }}
                >
                    {value}
                </Title>
            </div>
        </Card>
    );
};

export default KPICard;