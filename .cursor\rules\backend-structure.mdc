---
description: This rule describes the structure of the backend and how it should be organized.
alwaysApply: false
---
The backend is organized via firebase functions in the `functions` directory.

# Structure
- The backend is organized via firebase functions in the `functions` directory.
- `functions/index.js` is the entry point for the backend, it contains all the firebase functions for the app.
- Inside of `functions/index.js` there should be oncall/onschedule functions for the different features of the app that should reference helper functions.
- Each oncall/onschedule function should reference a helper function that does the actual work.
- `functions/helpers` contains one file for each platform that the app connects to. For example all functions that connect to shopify are in the `functions/helpers/shopify.js` file.
- Whenever possible, use the `functions/helpers` directory to store the helper functions for the different platforms.
- Try and consolidate code as much as possible, and avoid duplicating code, instead add more parameters to the helper functions if slightly different functionality is needed. Make sure to add a comment to the helper function explaining the parameters and ensure backwards compatibility.
- All functions in the app should accept an object as a parameter instead of multiple parameters (ie exampleFunction({test:1, test2:2}), instead of exampleFunction(test, test2) )
- Remove unnecesary logs from the app once debugging has been completed.
