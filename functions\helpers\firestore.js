// const admin = require("firebase-admin");
const { firestore } = require("firebase-admin");

// Firebase is already initialized in index.js
// admin.initializeApp();

exports.checkDuplicateDoc = async (collectionName, key, val) => {
  try {
    const collectionRef = firestore().collection(collectionName);
    const querySnap = await collectionRef.where(key, "==", val).get();
    if (querySnap.size > 0) {
      return true;
    }
    return false;
  } catch (error) {
    console.error("Error:", error);
    throw new Error("Error checking duplicate key");
  }
};

exports.deleteDocs = async (collectionName, queryList, ids = []) => {
  console.log("deleting list", collectionName, queryList, ids);
  const batchLimit = 500;
  if (ids.length > 0) {
    // console.log("deleting ids", ids);
    try {
      for (let i = 0; i < ids.length; i += batchLimit) {
        // console.log("deleting batch", i, ids.length/batchLimit);
        const batchIds = ids.slice(i, i + batchLimit);
        const batch = firestore().batch();
        batchIds.forEach((id) => {
          const docRef = firestore().collection(collectionName).doc(id.toString());
          // Firestore DocumentReference does not have an 'exists' property.
          // Just add the docRef to the batch for deletion; if it doesn't exist, delete is a no-op.
          batch.delete(docRef);
        });
        await batch.commit();
      }
      console.log(
        `Successfully deleted all documents in ${collectionName} matching the query.`,
      );
    } catch (error) {
      console.error("Error:", error.message, error.stack);
      throw new Error("Error deleting list");
    }
  } else {
    try {
      const batch = firestore().batch();
      let queryRef = firestore().collection(collectionName);
      queryList.forEach(({ key, operator, val }) => {
        queryRef = queryRef.where(key, operator, val);
      });
      const querySnapshot = await queryRef.get();
      console.log("deleting list", querySnapshot.size, collectionName, queryList);
      if (!querySnapshot.empty) {
        // Add all documents to the batch for deletion
        querySnapshot.docs.forEach((doc) => {
          batch.delete(doc.ref);
        });

        // Commit the batch
        await batch.commit();
        console.log(
          `Successfully deleted all documents in ${collectionName} matching the query.`,
        );
      } else {
        console.log(
          `No documents found in ${collectionName} matching the query.`,
        );
      }
    } catch (error) {
      console.error("Error:", error);
      throw new Error("Error deleting list");
    }
  }
};
exports.deleteDoc = async (collectionName, id) => {
  try {
    const docRef = firestore().collection(collectionName).doc(id);
    await docRef.delete();
    console.log("Document successfully deleted!");
    return true;
  } catch (error) {
    console.error("Error deleting thing:", error);
    return false;
  }
};

exports.createDoc = async (collectionName, data, id = "") => {
  try {
    const collectionRef = firestore().collection(collectionName);
    const documentRef = await collectionRef.add(data);
    console.log("id", documentRef.id);
    return documentRef.id;
  } catch (error) {
    console.error("Error creating thing:", error);
    return false;
  }
};
exports.createDocs = async (collection, dataList, ids = []) => {
  const batchSize = 500;
  const batches = [];
  for (let i = 0; i < dataList.length; i += batchSize) {
    batches.push(dataList.slice(i, i + batchSize));
  }
  let commitCount = 0;
  for (let i = 0; i < batches.length; i++) {
    try {
      const batchData = batches[i];
      const batch = firestore().batch();
      batchData.forEach((data) => {
        const collectionRef = firestore().collection(collection);
        batch.set(collectionRef.doc(), { ...data });
      });
      await batch.commit();
      commitCount++;
      console.log("committing batch", commitCount);
    } catch (error) {
      console.error("Error creating batch:" + commitCount, error);
    }
  }
};
exports.modifyDoc = async (collectionName, id, data, merge = true) => {
  try {
    const docRef = firestore().collection(collectionName).doc(id);
    const res = await docRef.update({ ...data }, { merge });
    console.log("Document successfully updated!", res);
    return res;
  } catch (error) {
    console.error("Error updating thing:", error);
    return false;
  }
};
exports.modifyDocs = async (collection, dataList, merge = true, printList = false) => {
  // remove jobs with '/' in id
  dataList = dataList.map((x) => ({ ...x, id: x.id.toString().replace(/\//g, "_") }));
  const removeUndefinedKeys = (obj) => {
    if (Array.isArray(obj)) {
      return obj.map(removeUndefinedKeys);
    } else if (
      obj !== null &&
      typeof obj === "object" &&
      !(obj instanceof Date)
    ) {
      return Object.entries(obj).reduce((acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = removeUndefinedKeys(value);
        }
        return acc;
      }, {});
    }
    return obj;
  };

  dataList = dataList.map(removeUndefinedKeys);
  dataList = dataList.map((data) => {
    return {
      ...data,
      modifiedAt: new Date(),
    };
  });

  if (printList) {
    dataList.forEach((data, index) => {
      console.log(`Data item ${index}:`, JSON.stringify(data, null, 2));
    });
  }

  const batchSize = 250;
  const batches = [];
  for (let i = 0; i < dataList.length; i += batchSize) {
    batches.push(dataList.slice(i, i + batchSize));
  }
  let commitCount = 0;
  const promList = [];
  for (let i = 0; i < batches.length; i++) {
    try {
      const batchData = batches[i];
      const batch = firestore().batch();
      batchData.forEach((job) => {
        const docRef = firestore().collection(collection).doc(job.id);
        batch.update(docRef, job.data, { merge });
      });
      promList.push(batch.commit());
      commitCount++;
      console.log("committing batch", commitCount);
    } catch (error) {
      console.error("Error creating batch:" + commitCount, error);
    }
  }
  const results = await Promise.allSettled(promList);
  results.forEach((result, index) => {
    if (result.status === "rejected") {
      console.error(`Error modifying batch ${index}:`, result.reason);
    }
  });
  return;
};

exports.getDocRef = (collectionName, id) => {
  try {
    const docRef = firestore().collection(collectionName).doc(id);
    return docRef;
  } catch (error) {
    console.error("Error creating thing:", error);
    return false;
  }
};
exports.getDocData = async (collectionName, id) => {
  try {
    const docRef = firestore().collection(collectionName).doc(id);
    const docGet = await docRef.get();
    const docData = docGet.data();
    return { ...docData, id: docGet.id };
  } catch (error) {
    console.error("Error getDocData:", error);
    return false;
  }
};
exports.queryDocs = async (collectionName, queryList, id) => {
  try {
    let queryRef = firestore().collection(collectionName);
    queryList.forEach(({ key, operator, val }) => {
      queryRef = queryRef.where(key, operator, val);
    });
    const querySnapshot = await queryRef.get();
    console.log("querying list", querySnapshot.size, collectionName, queryList);
    if (querySnapshot.empty) {
      console.log(
        `No documents found in ${collectionName} matching the query.`,
      );
      return [];
    }
    console.log("getting query data", querySnapshot.size, collectionName, queryList);
    const queryData = [];
    querySnapshot.forEach((doc) => {
      queryData.push({ id: doc.id, ...doc.data() });
    });
    console.log("queryData", queryData.length);
    return queryData;
  } catch (error) {
    console.error("Error getDocData:", error);
    return false;
  }
};
exports.countDocs = async (collectionName, queryList) => {
  try {
    let queryRef = firestore().collection(collectionName);
    queryList.forEach(({ key, operator, val }) => {
      queryRef = queryRef.where(key, operator, val);
    });
    const querySnapshot = await queryRef.get();
    console.log("querying list", querySnapshot.size, collectionName, queryList);
    if (querySnapshot.empty) {
      return 0;
    }
    return querySnapshot.size;
  } catch (error) {
    console.error("Error getDocData:", error);
    return false;
  }
};
