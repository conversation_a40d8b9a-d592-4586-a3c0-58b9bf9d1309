/* eslint-disable react/jsx-key */
import React, { useEffect, useState, useRef } from 'react';
import { Modal, message, Input, Checkbox, Button, Typography, Transfer, Popconfirm } from 'antd';
import { DndProvider, useDrop, useDrag } from "react-dnd";
import { MenuOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { getDocs, collection, addDoc, updateDoc, doc } from 'firebase/firestore';
import { HTML5Backend } from "react-dnd-html5-backend";
import { db, api } from '../../pages/firebase';
import { v4 as uuidv4 } from 'uuid';
import { setDoc } from 'firebase/firestore';

const { Title } = Typography;

const ItemWrapper = styled.div`
display: flex;
justify-content: space-between;
> .label {
  display: inline-block;
  max-width: calc(100% - 20px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
&.drop-over-downward {
  border-bottom: 2px dashed #1890ff;
}
&.drop-over-upward {
  border-top: 2px dashed #1890ff;
}
`;

const SavedView = ({ fields, savedViewData, setCurrentSavedView, savedViewModalIsOpen, setOpenModal, handleClearFilters, views, setViews, user, viewType = 'Product' }) => {
  const [filterData, setFilterData] = useState([]);
  const [columnData, setColumnData] = useState([]);
  const [targetFilterKeys, setTargetFilterKeys] = useState([]);
  const [targetColumnKeys, setTargetColumnKeys] = useState([]);
  const [isPublic, setIsPublic] = useState(savedViewData.public);
  const [name, setName] = useState(savedViewData.name);
  const [itemAppFields, setItemAppFields] = useState(fields);

  const saveAsSavedView = () => {
    if (!name) {
      message.error('Enter a name.');
      return;
    }

    if (views.find(view => view.name === name)) {
      message.error('The name already exists.');
      return;
    }

    const savedView = {
      id: uuidv4(),
      name: name,
      filters: targetFilterKeys,
      columns: targetColumnKeys,
      public: isPublic,
      createdBy: user.id
    };

    const addResult = setDoc(doc(db, `pim${viewType}Views`, savedView.id), savedView);
    addResult.then(() => {
      setViews([...views, savedView]);

      message.success('The saved view has been added.');
    });

    handleClearFilters();
    setCurrentSavedView(savedView);
    setOpenModal('');
  };

  const saveSavedView = () => {
    if (!name) {
      message.error('Enter a name.');
      return;
    }

    const savedView = {
      id: savedViewData.id,
      name: name,
      filters: targetFilterKeys,
      columns: targetColumnKeys,
      public: isPublic,
      createdBy: savedViewData.createdBy
    };

    const updatedViews = views.map(view =>
      view.id === savedViewData.id ? savedView : view
    );
    setViews(updatedViews);
    handleClearFilters();
    setCurrentSavedView(savedView);
    console.log('savedView', savedView);
    api.modifyDocOnCall({ collectionName: `pim${viewType}Views`, docId: savedViewData.id, info: savedView, merge: false });
    setOpenModal('');

    message.success('The saved view has been saved.');
  };

  const deleteSavedView = () => {
    deleteItemFromFirestore(`pim${viewType}Views`, savedViewData.id); // TODO: Add deleteItemFromFirestore to ../../pages/firebase.js
    setViews(views.filter(view => view.id !== savedViewData.id));
    message.success('The saved view has been deleted.');
    handleClearFilters();
    setCurrentSavedView(views.find(view => view.name === 'Default View'));
    setOpenModal('');
  };

  const moveRow = async (dragIndex, hoverIndex, clonedList, transferId) => {
    const el = clonedList.splice(dragIndex, 1)[0];
    clonedList.splice(hoverIndex, 0, el);

    if (transferId === 'filters')
      setTargetFilterKeys(Array.from(new Set(clonedList)));
    else
      setTargetColumnKeys(Array.from(new Set(clonedList)));
  };

  const type = 'DraggableItem';

  const DraggableItem = ({ index, label, moveRow, clonedList, transferId }) => {
    const ref = useRef();
    const [{ isOver, dropClassName }, drop] = useDrop({
      accept: type,
      collect: (monitor) => {
        const { index: dragIndex } = monitor.getItem() || {};
        if (dragIndex === index) {
          return {};
        }
        return {
          isOver: monitor.isOver(),
          dropClassName:
            dragIndex < index ? ' drop-over-downward' : ' drop-over-upward'
        };
      },
      drop: (item) => {
        moveRow(item.index, index, clonedList, transferId);
      }
    });

    const [{ isDragging }, drag, preview] = useDrag({
      type,
      item: { index },
      collect: (monitor) => ({
        isDragging: monitor.isDragging()
      })
    });

    preview(drop(ref));

    return (
      <ItemWrapper
        key={label}
        ref={ref}
        className={`${isOver ? dropClassName : ""}`}
      >
        <span className="label">{label}</span>
        {index !== -1 && (
          <span ref={drag}>
            <MenuOutlined />
          </span>
        )}
      </ItemWrapper>
    );
  };
  const fetchFields = async () => {
    const querySnapshot = await getDocs(collection(db, `pim${viewType}Fields`));
    const fields = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    setItemAppFields(fields);
  };
  const getSavedViewFields = () => {
    const tempTargetFilterKeys = [];
    const tempTargetColumnKeys = [];
    let tempFilterData = [];
    let tempColumnData = [];

    itemAppFields.map(field => {
      // filters
      if (field.fieldType !== 'image' && field.fieldType !== 'multiimage') {
        const tempFilterDataItem = {
          key: field.fieldId,
          title: field.label + ' [' + field.platform + ']',
          chosen: savedViewData.filters.indexOf(field.fieldId) >= 0,
        };
        tempFilterData.push(tempFilterDataItem);
      }
      // columns
      if (field.fieldType !== 'multiimage') {
        const tempColumnDataItem = {
          key: field.fieldId,
          title: field.label + ' [' + field.platform + ']',
          chosen: savedViewData.columns.indexOf(field.fieldId) >= 0,
        };
        tempColumnData.push(tempColumnDataItem);
      }
    });

    savedViewData.filters.map(fieldId => {
      const dataItem = tempFilterData.find(item => item.key === fieldId);
      if (dataItem && dataItem.chosen) {
        tempTargetFilterKeys.push(dataItem.key);
      }
    });
    savedViewData.columns.map(fieldId => {
      const dataItem = tempColumnData.find(item => item.key === fieldId);
      if (dataItem && dataItem.chosen) {
        tempTargetColumnKeys.push(dataItem.key);
      }
    });

    tempFilterData.sort((a, b) => a.title.localeCompare(b.title));
    tempColumnData.sort((a, b) => a.title.localeCompare(b.title));

    setFilterData(tempFilterData);
    setColumnData(tempColumnData);
    setTargetFilterKeys(tempTargetFilterKeys);
    setTargetColumnKeys(tempTargetColumnKeys);
  };

  useEffect(() => {
    fetchFields();
    setName(savedViewData.name);
  }, [savedViewData]);
  useEffect(() => {
    if (itemAppFields.length > 0) {
      getSavedViewFields();
    }
  }, [itemAppFields, savedViewData]);

  return (savedViewModalIsOpen) ? (
    <Modal maskClosable={false} open={true} width={1000} footer={[
      <div>
        <Button onClick={() => setOpenModal('')} style={{ marginLeft: '5px' }}>
          Cancel
        </Button>
        {(user && user.id === savedViewData.createdBy) && (
          <Popconfirm title="Are you sure you want to delete this saved view?" onConfirm={deleteSavedView} okText="Yes" cancelText="No">
            <Button style={{ marginLeft: '5px' }}>
              Delete
            </Button>
          </Popconfirm>
        )}
        <Button onClick={saveAsSavedView} style={{ marginLeft: '5px' }}>
          Save As
        </Button>
        {(user && user.id === savedViewData.createdBy) ? (
          <Button type="primary" onClick={saveSavedView} style={{ marginLeft: '5px' }}>
            Save
          </Button>) : ''
        }
      </div>
    ]} onCancel={() => setOpenModal('')}>
      <Title level={3} style={{ marginTop: '0px' }}>Manage Saved View</Title>
      <Title level={5} style={{ marginTop: '0px' }}>Name:</Title>
      <Input placeholder="Enter the saved view name." value={name} onChange={(e) => setName(e.target.value)} />
      <Title level={5} style={{ marginTop: '20px' }}>Filters:</Title>
      <DndProvider backend={HTML5Backend}>
        <Transfer
          rowKey={(item) => item.key}
          dataSource={filterData}
          showSearch
          filterOption={(input, option) =>
            (option?.key ?? '').toLowerCase().includes(input.toLowerCase()) ||
            (option?.title ?? '').toLowerCase().includes(input.toLowerCase())
          }
          targetKeys={targetFilterKeys}
          onChange={(newTargetKeys) => {
            setTargetFilterKeys(newTargetKeys);
          }}
          listStyle={{
            width: 450,
            height: 300,
          }}
          render={(item) => (
            <DraggableItem
              index={targetFilterKeys.findIndex((key) => key === item.key)}
              label={item.title}
              moveRow={moveRow}
              clonedList={targetFilterKeys}
              transferId="filters"
            />
          )}
        />
      </DndProvider>
      <Title level={5} style={{ marginTop: '20px' }}>Columns:</Title>
      <DndProvider backend={HTML5Backend}>
        <Transfer
          rowKey={(item) => item.key}
          dataSource={columnData}
          showSearch
          filterOption={(input, option) =>
            (option?.key ?? '').toLowerCase().includes(input.toLowerCase()) ||
            (option?.title ?? '').toLowerCase().includes(input.toLowerCase())
          }
          targetKeys={targetColumnKeys}
          onChange={(newTargetKeys) => {
            setTargetColumnKeys(newTargetKeys);
          }}
          listStyle={{
            width: 450,
            height: 300,
          }}
          render={(item) => (
            <DraggableItem
              index={targetColumnKeys.findIndex((key) => key === item.key)}
              label={item.title}
              moveRow={moveRow}
              clonedList={targetColumnKeys}
              transferId="columns"
            />
          )}
        />
      </DndProvider>
      <Checkbox style={{ marginTop: '15px' }} onChange={(e) => {
        setIsPublic(e.target.checked);
      }} checked={isPublic}>Set to Public</Checkbox>
    </Modal>
  ) : '';
};
export default SavedView;