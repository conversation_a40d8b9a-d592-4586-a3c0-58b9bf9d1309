import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { api } from '../../pages/firebase';

const BackOrderTab = () => {
  const [orderData, setOrderData] = useState([]);
  const [loading, setLoading] = useState(true);
  const parseData = (data) => {
    const itemData = [];
    if (data) {
      const lines = data.split('\n');
      const headers = lines[0].split(',');

      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',');
        const itemObj = {};

        for (let j = 0; j < headers.length; j++) {
          itemObj[headers[j]] = values[j];
        }

        itemData.push(itemObj);
      }
    }
    return itemData;
  };
  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    const fetchOrderData = async () => {
      if (orderData.length == 0) {
        try {
          const orderDataResponse = await axios.get(searchUrl + 103497, {
            signal: signal,
          });
          let orderDataRes = parseData(orderDataResponse.data);
          orderDataRes = orderDataRes
            .map((orderObj) => {
              return {
                id: orderObj['Internal ID'],
                document_number: orderObj['Document Number'],
                customer: orderObj['Customer'],
                po_num: orderObj['PO #'],
                date: orderObj['Date'],
                ship_date: orderObj['Ship Date'],
                total_units: orderObj['Total Units'],
                back_ordered: orderObj['Back Ordered'],
              };
            })
            .sort((a, b) => {
              return new Date(b.name) - new Date(a.name);
            });
          setOrderData(orderDataRes);
          setLoading(false);
        } catch (err) {
          if (axios.isCancel(err)) {
            console.log('Request canceled', err.message);
          } else {
            console.log(err);
            setLoading(false);
          }
        }
      }
    };
    fetchOrderData();

    return () => {
      controller.abort();
    };
  }, []);

  return loading ? (
    <div className="spinner-container">
      <div className="spinner-border text-primary" role="status">
        <span className="sr-only">Loading...</span>
      </div>
    </div>
  ) : (
    <div className="card shadow mt-3 mb-4">
      <div className="card-head">
        <h4 style={{ padding: '20px 20px 0px 20px' }}>Backordered</h4>
      </div>
      <div className="card-body">
        <div className="table-responsive">
          <table className="table">
            <thead>
              <tr>
                <th scope="col">Document Numer</th>
                <th scope="col">Customer</th>
                <th scope="col">PO #</th>
                <th scope="col">Date</th>
                <th scope="col">Ship Date</th>
                <th scope="col">Total Units</th>
                <th scope="col">Back Ordered</th>
              </tr>
            </thead>
            <tbody>
              {orderData.map((order) => {
                return (
                  <tr key={order.id} onClick={() => setModalItem(order.id)}>
                    <td>{order.document_number}</td>
                    <td>{order.customer}</td>
                    <td>{order.po_num}</td>
                    <td>{order.date}</td>
                    <td>{order.dispatch_date}</td>
                    <td>{order.total_units}</td>
                    <td>{order.back_ordered}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};
export default BackOrderTab;
