/* eslint-disable react/no-unknown-property */
/* eslint-disable react/jsx-key */

import React, { useState, useEffect, useMemo } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Popover, Flex, Layout, Tabs, Typography, Input, InputNumber, DatePicker, Select, Checkbox, Image, Button, Form, Popconfirm, Tooltip, Upload, message } from "antd";
import { PlusOutlined, EditOutlined, DoubleLeftOutlined, RightOutlined, LeftOutlined, DoubleRightOutlined } from '@ant-design/icons';

import { PRODUCT_TABLE, VARIANTS_TABLE } from "../../components/pim/template";
import { db, api, storage } from "../firebase";
import { getDocs, collection } from "firebase/firestore";
import { ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";
const { Content } = Layout;
const { Title, Text } = Typography;

const generateRandomImageName = (length = 20) => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let randomString = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    randomString += characters[randomIndex];
  }
  return `IMG_${randomString}`;
};

const getFormattedDate = (date = new Date()) => {
  return new Date(date).toISOString().split("T")[0];
};

const isValidUrl = (string) => {
  let pattern = new RegExp('^(https?:\\/\\/)?' + // protocol
    '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
    '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
    '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
    '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
    '(\\#[-a-z\\d_]*)?$', 'i'); // fragment locator
  return !!pattern.test(string);
};

const updateDependentFields = (fieldName, value, productData, sourceValueMapping) => {
  let tempData = { [fieldName]: value };

  if (sourceValueMapping[fieldName]) {
    sourceValueMapping[fieldName].forEach(dependentField => {
      if (productData[dependentField] === productData[fieldName]) {
        tempData = {
          ...tempData,
          ...updateDependentFields(dependentField, value, productData, sourceValueMapping)
        };
      }
    });
  }

  return tempData;
};

const PimProductEdit = ({ viewType = 'Product' }) => {
  const navigate = useNavigate();
  const { id: productId, mode } = useParams();
  const [productData, setProductData] = useState({});
  const [fields, setFields] = useState([]);
  const [itemImages, setItemImages] = useState([]);
  const [sourceValueMapping, setSourceValueMapping] = useState({});
  const [productsAsOptions, setProductsAsOptions] = useState([]);
  const [saving, setSaving] = useState(false);

  const isMandatory = (field) => {
    return field.mandatory || (field.mandatoryStage && field.lifeStatus && field.mandatoryStage === productData.lifeStatus);
  };

  useEffect(() => {
    const fetchFields = async () => {
      const firestoreFields = await getDocs(collection(db, `pim${viewType}Fields`));
      if (viewType === 'Variant') {
        const itemsQuery = `SELECT id, name FROM ${PRODUCT_TABLE}`;
        const itemsResults = await api.runQueryOnCall({ options: { query: itemsQuery } });
        setProductsAsOptions(itemsResults.data.map(item => ({ id: item.id, label: item.name })));
      }
      // console.log("fields", firestoreFields.docs.map(doc => ({ id: doc.id, ...doc.data() })));
      setFields(firestoreFields.docs.map(doc => ({ id: doc.id, ...doc.data() })));

      let relatedMapping = {};
      firestoreFields.docs.map(field => {
        field = field.data();
        if (field.relatedField && !field.relatedField.startsWith('product.')) {
          if (!relatedMapping[field.relatedField])
            relatedMapping[field.relatedField] = [];
          relatedMapping[field.relatedField].push(field.fieldId);
        }
        if (field.defaultField && !field.defaultField.startsWith('product.')) {
          if (!relatedMapping[field.defaultField])
            relatedMapping[field.defaultField] = [];
          relatedMapping[field.defaultField].push(field.fieldId);
        }
      });
      // console.log('mapping', relatedMapping);
      setSourceValueMapping(relatedMapping);
    };
    fetchFields();
  }, [viewType]);
  useEffect(() => {
    if (productId === "new") return;
    // fetch product data
    const fetchProduct = async () => {
      const productQuery = `SELECT * FROM ${viewType === 'Product' ? PRODUCT_TABLE : VARIANTS_TABLE} WHERE id = '${productId}'`;
      const productResults = await api.runQueryOnCall({ options: { query: productQuery } });
      setProductData(productResults.data[0]);
    };

    fetchProduct();
  }, [productId]);

  const handleChange = (name, value) => {
    if (name === 'productId' && value) {
      handleProductIdChange(value);
    } else {
      handleFieldChange(name, value);
    }
  };
  
  const handleProductIdChange = async (value) => {
    const productQuery = `SELECT * FROM ${PRODUCT_TABLE} WHERE id = '${value}'`;
    const productResults = await api.runQueryOnCall({ options: { query: productQuery } });
    const tempSelectedProduct = productResults.data[0];
  
    // console.log(tempSelectedProduct);
  
    setProductData(prevProductData => {
      let tempData = { ...prevProductData, productId: value };
  
      fields.forEach(field => {
        if (field.relatedField && field.relatedField.startsWith('product.')) {
          const relatedFieldName = field.relatedField.split('.')[1];
          tempData[field.fieldId] = tempSelectedProduct[relatedFieldName];
        }
        if (field.defaultField && field.defaultField.startsWith('product.')) {
          const defaultFieldName = field.defaultField.split('.')[1];
          tempData[field.fieldId] = tempSelectedProduct[defaultFieldName];
        }
      });
  
      Object.keys(tempData).forEach(fieldName => {
        tempData = {
          ...tempData,
          ...updateDependentFields(fieldName, tempData[fieldName], prevProductData, sourceValueMapping)
        };
      });
  
      return { ...prevProductData, ...tempData };
    });
  };
  
  const handleFieldChange = (name, value) => {
    setProductData(prevProductData => {
      let tempData = updateDependentFields(name, value, prevProductData, sourceValueMapping);
  
      Object.keys(tempData).forEach(fieldName => {
        tempData = {
          ...tempData,
          ...updateDependentFields(fieldName, tempData[fieldName], prevProductData, sourceValueMapping)
        };
      });
  
      // console.log(prevProductData, tempData);
  
      return { ...prevProductData, ...tempData };
    });
  };

  const validateData = (data) => {  // TODO: move validation to backend?
    // console.log(data);
    if (data.id === "new") {
      data.id = uuidv4();
      data.dateCreated = getFormattedDate();
    } else {
      data.dateCreated = getFormattedDate(data.dateCreated.value);
    }
    data.dateUpdated = getFormattedDate();
    data.hidden = false;
    Object.keys(data).forEach(key => { // Empty arrays break BigQuery queries
      if (Array.isArray(data[key]) && data[key].length === 0) {
        delete data[key];
      }
      if (data[key] === null) {
        delete data[key];
      }
    });
    data.fieldCompletion = 0;
    const fieldListWithValues = fields.filter(prop => (prop in data && (data[prop] || data[prop] === false || data[prop] === 0)));

    data.fieldCompletion = parseFloat(((fieldListWithValues.length / fields.length) * 100).toFixed(2));

    return data;
  };

  const platforms = useMemo(() => {
    const platforms = [...new Set(fields.map(field => field.platform))].sort((a, b) => (a === 'PRIMARY' ? -1 : b === 'PRIMARY' ? 1 : 0)); // Primary tab first
    // console.log('platforms', platforms);
    return platforms;
  }, [fields]);

  const getFieldPopoverContent = (field) => {
    return (field.platform === 'primary') ? field.label : (
      <Popover content={(
        <Flex vertical gap="middle">
          <Flex gap="small" wrap>
            <Button color="default" variant="link" onClick={() => {
              setCurrentFieldView(field); setFieldViewModalIsOpen(true);
            }}>
              <Tooltip title="Edit the Field"><EditOutlined /></Tooltip>
            </Button>
            <Button color="default" variant="link" onClick={() => moveFieldPosition(field, 'first')}>
              <Tooltip title="Move to First"><DoubleLeftOutlined /></Tooltip>
            </Button>
            <Button color="default" variant="link" onClick={() => moveFieldPosition(field, 'previous')}>
              <Tooltip title="Move to Previous"><LeftOutlined /></Tooltip>
            </Button>
            <Button color="default" variant="link" onClick={() => moveFieldPosition(field, 'next')}>
              <Tooltip title="Move Next"><RightOutlined /></Tooltip>
            </Button>
            <Button color="default" variant="link" onClick={() => moveFieldPosition(field, 'last')}>
              <Tooltip title="Move to Last"><DoubleRightOutlined /></Tooltip>
            </Button>
          </Flex>
        </Flex>
      )} trigger="click">
        {field.label}
      </Popover>
    );
  };
  const FileUpload = ({ directory, fieldId, imageURL, multi }) => {
    const [uploadLoading, setUploadLoading] = useState(false);
    const [previewOpen, setPreviewOpen] = useState(false);
    const [previewImage, setPreviewImage] = useState('');
    const handlePreview = async (file) => {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      setPreviewImage(file.url || file.preview);
      setPreviewOpen(true);
    };

    const handleUploadChange = (info) => {
      // console.log('upload', info);
      if (info.file.status === 'removed') {
        if (multi) {
          let images = (imageURL && imageURL.length > 0) ? imageURL : [];
          handleChange(fieldId, images.filter(image => image !== info.file.url));
        } else {
          handleChange(fieldId, null);
        }
      } else {
        setUploadLoading(true);

        const fileUpload = info.file;
        uploadFileToFirebase(fileUpload);
      }
    };

    const uploadFileToFirebase = (fileUpload) => {
      const imageName = generateRandomImageName();
      const storageRef = ref(storage, `${directory}/${imageName}`);
      const uploadTask = uploadBytesResumable(storageRef, fileUpload);

      uploadTask.on(
        'state_changed',
        (snapshot) => {
          // console.log(snapshot);
        },
        (error) => {
          // console.error('Upload failed', error);
        },
        () => {
          // Get the uploaded image URL
          getDownloadURL(uploadTask.snapshot.ref).then((downloadURL) => {
            // console.log(downloadURL);
            if (multi) {
              let images = (imageURL && imageURL.length > 0) ? imageURL : [];
              if (images.indexOf(downloadURL) === -1)
                images.push(downloadURL);

              handleChange(fieldId, images);
            } else {
              handleChange(fieldId, downloadURL);
            }

            if (itemImages.indexOf(downloadURL) === -1) {
              setItemImages(images => [...images, downloadURL]);
            }
            setUploadLoading(false);
          });
        }
      );
    };

    const handleUploadImageURL = async (imageURL) => {
      if (!isValidUrl(imageURL)) {
        message.error('Invalid URL.');
        return;
      }

      setUploadImageURL(imageURL);

      const response = await axios.get(imageURL, { responseType: 'blob' });
      const fileBlob = response.data;
      uploadFileToFirebase(fileBlob);
      // console.log(fileBlob);
    };

    return (
      <>
        <Upload
          action="/"
          listType="picture-card"
          fileList={(multi) ? ((imageURL) ? imageURL.map((url, uid) => ({ uid, url })) : []) : ((imageURL) ? [{ uid: 0, url: imageURL }] : [])}
          beforeUpload={() => false}
          multiple={(multi) ? true : false}
          onPreview={handlePreview}
          onChange={handleUploadChange}
        >
          <PlusOutlined /> {(multi) ? 'Images' : 'Image'}
        </Upload>
        {previewImage && (
          <Image
            wrapperStyle={{
              display: 'none',
            }}
            preview={{
              visible: previewOpen,
              onVisibleChange: (visible) => setPreviewOpen(visible),
              afterOpenChange: (visible) => !visible && setPreviewImage(''),
            }}
            src={previewImage}
          />
        )}
        <Popconfirm
          title="Enter the Image URL:"
          description={(<Input placeholder="URL" style={{ width: '250px' }} rules={[{ type: 'url', warningOnly: true }]} onPressEnter={(e) => {
            handleUploadImageURL(e.target.value);
          }} />)}
          icon=""
          okText="Cancel"
          okType="text"
          showCancel={false}
        >
          <Button type='link' style={{ 'padding': '0px', marginBottom: '-10px' }}>Upload from URL</Button>
        </Popconfirm>
      </>
    );
  };

  if (productId !== "new" && !productData) {
    // console.log("Loading...");
    return (<div>Loading...</div>);
  }

  const handleSave = async () => {
    const validatedData = validateData({ id: productId, ...productData });
    for (let field of fields) {
      if (isMandatory(field) && !(productData[field.fieldId] || productData[field.fieldId] === false || productData[field.fieldId] === 0)) {
        // console.log(field);
        message.error('Please fill in all mandatory fields.');
        return;
      }
      if (field.relatedField) {
        productData[field.fieldId] = productData[field.relatedField]; // TODO: Check if this works for product.[field]
      }
    }
    setSaving(true);
    const totalFilledValues = Object.keys(validatedData).reduce((acc, field) => {
      if (validatedData[field] || validatedData[field] === false || validatedData[field] === 0) acc++;
      return acc;
    }, 0);
    validatedData.fieldCompletion = Math.round((totalFilledValues / fields.length) * 100);
    await api.upsertOnCall({ datasetId: 'items', table: viewType.toLowerCase() + 's', key: 'id', rows: [validatedData] });
    navigate(`/pim/${viewType.toLowerCase()}s`);
  };

  return (
    <Layout>
      <Content>
        <Title level='2'>{productId === "new" ? `New ${viewType}` : productData.name}</Title>
        <Tabs defaultActiveKey="1"
          type="card"
          size="large"
          style={{ backgroundColor: "white", padding: "10px" }}
        >
          {/* Tabs for different platforms */}
          {platforms.length > 0 && (
            platforms.map(platform => (
              < Tabs.TabPane tab={platform.toUpperCase()} key={platform}>
                {/* Content for NetSuite tab */}
                {Object.entries(fields.filter(field => field.platform === platform).reduce((acc, field) => {
                  if (!acc[field.group]) acc[field.group] = [];
                  acc[field.group].push(field);
                  return acc;
                }, {})).map(([group, fields]) => (
                  <div key={group}>
                    <Title level='6'>{group.toUpperCase()}</Title>
                    {fields.map(field => {
                      if (field.fieldId === 'images') {
                        return (mode === 'view' || field.display === 'disabled' || field.display === 'inline') ? (
                          <div style={{ width: '100%' }}>
                            {(productData[field.fieldId] && productData[field.fieldId].length > 0) ? productData[field.fieldId].map(image => {
                              return (
                                <Image
                                  style={{ padding: '2px' }}
                                  width={180}
                                  src={image || ''}
                                />
                              );
                            }) : ''}
                          </div>
                        ) : (
                          <div style={{ width: '100%' }} rules={[{ required: isMandatory(field) }]}>
                            <Tooltip trigger={(itemImages.length > 0) ? 'hover' : ''} placement="bottom" title={(
                              <Image.PreviewGroup>
                                {itemImages.map(image => {
                                  return (
                                    <Image width={58} preview={false} src={image} style={{ cursor: 'pointer', padding: '2px' }} onClick={() => {
                                      const images = (productData[field.fieldId] && productData[field.fieldId].length > 0) ? productData[field.fieldId] : [];
                                      handleChange(field.fieldId, [...images, image]);
                                    }} />
                                  );
                                })}
                              </Image.PreviewGroup>
                            )} color="#fff">
                              <div style={{ width: '100px', display: 'unset' }}>
                                <FileUpload
                                  directory="item_images"
                                  fieldId={field.fieldId}
                                  multi={true}
                                  imageURL={productData[field.fieldId] || []}
                                />
                              </div>
                            </Tooltip>
                          </div>
                        );
                      } else if (field.fieldId === 'variants') { // TODO: add variants to products?
                        if (productData[field.fieldId] && productData[field.fieldId].length > 0) {
                          const fieldOptions = productsAsOptions;
                          return (
                            <div style={{ width: '49%' }}>
                              <Form.Item label={field.label} name={field.fieldId} style={{ marginBottom: '0px' }} extra={(field.note) ? field.note : ''}>
                                {productData[field.fieldId].map(variant => {
                                  return (<Text strong><Button type="link" onClick={() => {
                                    openViewItemView(variant);
                                  }}>{((fieldOptions.find(opt => opt.id === variant))) ? (fieldOptions.find(opt => opt.id === variant)).label : ''}</Button></Text>);
                                })}
                              </Form.Item>
                            </div>
                          );
                        }
                      } else if (['text', 'currency', 'integer', 'decimal', 'percent'].indexOf(field.fieldType) >= 0) {
                        return (mode === 'view' || field.display === 'inline') ? (
                          <div style={{ width: '49%' }}>
                            <Form.Item label={getFieldPopoverContent(field)} name={field.fieldId} style={{ marginBottom: '0px' }} extra={(field.note) ? field.note : ''}>
                              <Text strong>
                                {(productData[field.fieldId]) ? (
                                  (field.fieldType === 'currency') ? '$' + productData[field.fieldId] :
                                    (field.fieldType === 'percent') ? productData[field.fieldId] + '%' :
                                      productData[field.fieldId]
                                ) : ''}
                              </Text>
                            </Form.Item>
                          </div>
                        ) : (
                          <div style={{ width: '49%' }}>
                            <Form.Item label={getFieldPopoverContent(field)} name={field.fieldId} onChange={(e) => {
                              handleChange(field.fieldId, e.target.value);
                            }} rules={[{ required: isMandatory(field) }]} style={{ marginBottom: '0px' }} extra={(field.note) ? field.note : ''}>
                              {
                                (field.fieldType === 'currency' || field.fieldType === 'integer' || field.fieldType === 'decimal' || field.fieldType === 'percent') ? (
                                  <>
                                    <InputNumber
                                      defaultValue={productData[field.fieldId] || ''}
                                      value={productData[field.fieldId] || ''}
                                      name={field.fieldId}
                                      prefix={field.fieldType === 'currency' ? '$' : ''}
                                      suffix={(field.fieldType === 'percent') ? '%' : ''}
                                      disabled={(field.display === 'disabled')}
                                      style={{ minWidth: '200px' }}
                                    />
                                    <input type="hidden" value={productData[field.fieldId] || ''} />
                                  </>
                                ) : (
                                  <>
                                    <Input
                                      defaultValue={productData[field.fieldId] || ''}
                                      value={productData[field.fieldId] || ''}
                                      name={field.fieldId}
                                      disabled={(field.display === 'disabled')}
                                      style={{ minWidth: '100px' }}
                                      status={(isMandatory(field) && !(productData[field.fieldId])) ? 'error' : ''}
                                    />
                                    <input type="hidden" value={productData[field.fieldId] || ''} />
                                  </>
                                )
                              }
                            </Form.Item>
                          </div>
                        );
                      } else if (field.fieldType === 'url') {
                        return (mode === 'view' || field.display === 'inline') ? (
                          <div style={{ width: '49%' }}>
                            <Form.Item label={getFieldPopoverContent(field)} name={field.fieldId} style={{ marginBottom: '0px' }} extra={(field.note) ? field.note : ''}>
                              <Text strong>{(productData[field.fieldId]) ? (<a href={productData[field.fieldId]} target="_blank" rel="noreferrer">{productData[field.fieldId]}</a>) : ''}</Text>
                            </Form.Item>
                          </div>
                        ) : (
                          <div style={{ width: '49%' }}>
                            <Form.Item label={getFieldPopoverContent(field)} name={field.fieldId} onChange={(e) => {
                              handleChange(field.fieldId, e.target.value);
                            }} rules={[{ required: isMandatory(field) }]} style={{ marginBottom: '0px' }} extra={(field.note) ? field.note : ''}>
                              <>
                                <Input
                                  defaultValue={productData[field.fieldId] || ''}
                                  value={productData[field.fieldId] || ''}
                                  name={field.fieldId}
                                  disabled={(field.display === 'disabled')}
                                  style={{ minWidth: '100px' }}
                                  rules={[{ type: 'url', warningOnly: true }]}
                                  status={(isMandatory(field) && !(productData[field.fieldId])) ? 'error' : ''}
                                />
                                <input type="hidden" value={productData[field.fieldId] || ''} />
                              </>
                            </Form.Item>
                          </div>
                        );
                      } else if (field.fieldType === 'longtext') {
                        return (mode === 'view' || field.display === 'inline') ? (
                          <div style={{ width: '49%' }}>
                            <Form.Item label={getFieldPopoverContent(field)} name={field.fieldId} style={{ marginBottom: '0px' }} extra={(field.note) ? field.note : ''}>
                              <Text strong>{productData[field.fieldId] || ''}</Text>
                            </Form.Item>
                          </div>
                        ) : (
                          <div style={{ width: '49%' }}>
                            <Form.Item label={getFieldPopoverContent(field)} name={field.fieldId} onChange={(e) => {
                              handleChange(field.fieldId, e.target.value);
                            }} rules={[{ required: isMandatory(field) }]} style={{ marginBottom: '0px' }} extra={(field.note) ? field.note : ''}>
                              <>
                                <Input.TextArea
                                  defaultValue={productData[field.fieldId] || ''}
                                  value={productData[field.fieldId] || ''}
                                  name={field.fieldId}
                                  disabled={(field.display === 'disabled')}
                                  style={{ minWidth: '100px' }}
                                  status={(isMandatory(field) && !(productData[field.fieldId])) ? 'error' : ''}
                                />
                                <input type="hidden" value={productData[field.fieldId] || ''} />
                              </>
                            </Form.Item>
                          </div>
                        );
                      } else if (field.fieldType === 'date') {
                        return (mode === 'view' || field.display === 'inline') ? (
                          <div style={{ width: '49%' }}>
                            <Form.Item label={getFieldPopoverContent(field)} name={field.fieldId} style={{ marginBottom: '0px' }} extra={(field.note) ? field.note : ''}>
                              <Text strong>{(productData[field.fieldId]) ? dayjs((productData[field.fieldId].value) ? new Date(productData[field.fieldId].value) : new Date(productData[field.fieldId])).format('MM/DD/YYYY') : ''}</Text>
                            </Form.Item>
                          </div>
                        ) : (
                          <div style={{ width: '49%' }}>
                            <Form.Item label={getFieldPopoverContent(field)} name={field.fieldId} rules={[{ required: isMandatory(field) }]} style={{ marginBottom: '0px' }} extra={(field.note) ? field.note : ''}>
                              <DatePicker
                                defaultValue={(productData[field.fieldId]) ? dayjs((productData[field.fieldId].value) ? new Date(productData[field.fieldId].value) : new Date(productData[field.fieldId])) : ''}
                                value={(productData[field.fieldId]) ? dayjs((productData[field.fieldId].value) ? new Date(productData[field.fieldId].value) : new Date(productData[field.fieldId])) : ''}
                                name={field.fieldId}
                                onChange={(dateVal) => {
                                  handleChange(field.fieldId, (dateVal) ? new Date(dateVal.$d) : '');
                                }}
                                format="MM/DD/YYYY"
                                disabled={(field.display === 'disabled')}
                                style={{ minWidth: '100px' }}
                                status={(isMandatory(field) && !(productData[field.fieldId])) ? 'error' : ''}
                              />
                            </Form.Item>
                          </div>
                        );
                      } else if (field.fieldType === 'select') {
                        const fieldOptions = (field.fieldId.includes('product') ? productsAsOptions : field.options);
                        return (mode === 'view' || field.display === 'inline') && !(field.fieldId === 'productId' && (productId === "new")) ? (
                          <div style={{ width: '49%' }}>
                            <Form.Item label={getFieldPopoverContent(field)} name={field.fieldId} style={{ marginBottom: '0px' }} extra={(field.note) ? field.note : ''}>
                              {field.fieldId === 'productId' ? (
                                <Text strong>
                                  <Button type="link" onClick={() => {
                                    // console.log('fieldOptions', fieldOptions, 'field', field, 'productData', productData);
                                    openViewItemView(productData[field.fieldId]); // TODO: Route to Product
                                  }}>
                                    {fieldOptions.find(opt => opt.id === productData[field.fieldId])?.label || ''}
                                  </Button>
                                </Text>
                              ) : (
                                <Text strong>
                                  {fieldOptions.find(opt => opt.id === productData[field.fieldId])?.label || ''}
                                </Text>
                              )}
                            </Form.Item>
                          </div>
                        ) : (
                          <div style={{ width: '49%' }}>
                            <Form.Item label={getFieldPopoverContent(field)} name={field.fieldId} rules={[{ required: isMandatory(field) }]} style={{ marginBottom: '0px' }} extra={field.note || ''}>
                              <Select
                                showSearch
                                filterOption={(input, option) =>
                                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                                }
                                name={field.fieldId}
                                placeholder="Select..."
                                allowClear
                                disabled={field.display === 'disabled'}
                                defaultValue={productData[field.fieldId] || ''}
                                value={productData[field.fieldId] || ''}
                                onChange={(val) => handleChange(field.fieldId, val)}
                                style={{ minWidth: '100px' }}
                                status={isMandatory(field) && !productData[field.fieldId] ? 'error' : ''}
                                options={fieldOptions.map(option => ({ value: option.id, label: option.label }))}
                              />
                              <input type="hidden" value={productData[field.fieldId] || ''} />
                            </Form.Item>
                          </div>
                        );
                      } else if (field.fieldType === 'checkbox') {
                        return (mode === 'view' || field.display === 'disabled' || field.display === 'inline') ? (
                          <div style={{ width: '49%' }} rules={[{ required: isMandatory(field) }]}>
                            <Form.Item name={field.fieldId} style={{ marginBottom: '0px' }} wrapperCol={{ offset: 8, span: 16, }} extra={(field.note) ? field.note : ''}>
                              <Checkbox checked={productData[field.fieldId] || false}>{getFieldPopoverContent(field)}</Checkbox>
                            </Form.Item>
                          </div>
                        ) : (
                          <div style={{ width: '49%' }} rules={[{ required: isMandatory(field) }]}>
                            <Form.Item name={field.fieldId} style={{ marginBottom: '0px' }} wrapperCol={{ offset: 8, span: 16, }} extra={(field.note) ? field.note : ''}>
                              <Checkbox checked={productData[field.fieldId] || false} onChange={(e) => {
                                handleChange(field.fieldId, e.target.checked);
                              }}>{getFieldPopoverContent(field)}</Checkbox>
                            </Form.Item>
                          </div>
                        );
                      } else if (field.fieldType === 'image') {
                        return (mode === 'view' || field.display === 'disabled' || field.display === 'inline') ? (
                          <div style={{ width: '49%' }}>
                            <Form.Item label={getFieldPopoverContent(field)} name={field.fieldId} style={{ marginBottom: '0px' }} extra={(field.note) ? field.note : ''}>
                              <Image
                                width={200}
                                src={productData[field.fieldId] || ''}
                              />
                            </Form.Item>
                          </div>
                        ) : (
                          <div style={{ width: '49%' }} rules={[{ required: isMandatory(field) }]}>
                            <Form.Item label={getFieldPopoverContent(field)} name={field.fieldId} style={{ marginBottom: '20px' }} extra={(field.note) ? field.note : ''}>
                              <Tooltip trigger={(itemImages.length > 0) ? 'hover' : ''} placement="topLeft" title={(
                                <Image.PreviewGroup>
                                  {itemImages.map(image => {
                                    return (
                                      <Image width={58} preview={false} src={image} style={{ cursor: 'pointer', padding: '2px' }} onClick={() => handleChange(field.fieldId, image)} />
                                    );
                                  })}
                                </Image.PreviewGroup>
                              )} color="#fff">
                                <div style={{ width: '100px', display: 'unset' }}>
                                  <FileUpload
                                    directory="item_images"
                                    fieldId={field.fieldId}
                                    imageURL={productData[field.fieldId] || ''}
                                  />
                                </div>
                              </Tooltip>
                            </Form.Item>
                          </div>
                        );
                      } else if (field.fieldType === 'multiimage') {
                        return (mode === 'view' || field.display === 'disabled' || field.display === 'inline') ? (
                          <div style={{ width: '49%' }}>
                            <Form.Item label={getFieldPopoverContent(field)} name={field.fieldId} style={{ marginBottom: '0px' }} extra={(field.note) ? field.note : ''}>
                              {(productData[field.fieldId] && productData[field.fieldId].length > 0) ? productData[field.fieldId].map(image => {
                                return (
                                  <Image
                                    style={{ padding: '2px' }}
                                    width={120}
                                    src={image || ''}
                                  />
                                );
                              }) : ''}
                            </Form.Item>
                          </div>
                        ) : (
                          <div style={{ width: '49%' }} rules={[{ required: isMandatory(field) }]}>
                            <Form.Item label={getFieldPopoverContent(field)} name={field.fieldId} style={{ marginBottom: '20px' }} extra={(field.note) ? field.note : ''}>
                              <Tooltip trigger={(itemImages.length > 0) ? 'hover' : ''} placement="bottom" title={(
                                <Image.PreviewGroup>
                                  {itemImages.map(image => {
                                    return (
                                      <Image width={58} preview={false} src={image} style={{ cursor: 'pointer', padding: '2px' }} onClick={() => {
                                        const images = (productData[field.fieldId] && productData[field.fieldId].length > 0) ? productData[field.fieldId] : [];
                                        handleChange(field.fieldId, [...images, image]);
                                      }} />
                                    );
                                  })}
                                </Image.PreviewGroup>
                              )} color="#fff">
                                <div style={{ width: '100px', display: 'unset' }}>
                                  <FileUpload
                                    directory="item_images"
                                    fieldId={field.fieldId}
                                    multi={true}
                                    imageURL={productData[field.fieldId] || []}
                                  />
                                </div>
                              </Tooltip>
                            </Form.Item>
                          </div>
                        );
                      }
                    }
                    )}
                  </div>
                ))}
              </Tabs.TabPane>
            )
            ))}
        </Tabs>
        {(mode === 'edit') &&
          <Button type="primary" onClick={handleSave} disabled={saving}>{saving ? 'Saving...' : 'Save'}</Button>}
      </Content>
    </Layout >
  );
};
export default PimProductEdit;