import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, Select, message, Table, Checkbox, Layout, Typography, Spin, ColorPicker } from 'antd';
import { api } from '../../pages/firebase';
import { db } from '../../pages/firebase';
import { collection, onSnapshot, addDoc, deleteDoc, doc, getDoc, setDoc } from 'firebase/firestore';
import { Content } from 'antd/es/layout/layout';
import { SyncOutlined } from '@mui/icons-material';
import { locObj, SALES_PREDICTOR_METHODS } from '../../constants';
const { Title } = Typography;


const NodeList = () => {
  const [nodeList, setNodeList] = useState([]);
  const [open, setOpen] = useState(false);
  const [selectedNode, setSelectedNode] = useState({});
  const [lists, setLists] = useState([]);
  const [originalNodeList, setOriginalNodeList] = useState([]);
  const [loading, setLoading] = useState(true);

  const fetchListValues = async () => {
    // get all the lists from firestore
    setLoading(true);
    const lists = ['regions', 'divisions', 'classes', 'channels', 'customers'];
    const listPromises = lists.map(async (list) => {
      const response = await getDoc(doc(db, 'forecastLists', list));
      // sort the items by id
      const items = response.data().items.sort((a, b) => a.id - b.id);
      return { id: response.id, items };
    });
    setLists(await Promise.all(listPromises));
    setLoading(false);
  };
  const fetchNetsuiteValues = async () => {
    setLoading(true);
    const response = await api.makeSuiteQlQuery({
      query: `
        SELECT 
          id,
          name,
        FROM CUSTOMRECORD_CSEG_REGION
      `
    });
    // console.log(response);
    const classResponse = await api.makeSuiteQlQuery({
      query: `
          SELECT 
            id,
            name,
          FROM CUSTOMRECORD_CSEG_CLASS2
        `
    });
    const divisionResponse = await api.makeSuiteQlQuery({
      query: `
        SELECT 
          id,
          name,
        FROM CUSTOMRECORD_CSEG_DIVISION
      `
    });
    const channelResponse = await api.makeSuiteQlQuery({
      query: `
        SELECT 
          channel.id as id,
          channel.name as name,
          class.name as class,
          class.id as classid
        FROM CUSTOMRECORD_CSEG_CHANNEL2 channel
        left join CUSTOMRECORD_CSEG_CLASS2 class on class.id=channel.cseg_class2
      `
    });
    // console.log(channelResponse);
    const customerResponse = await api.makeSuiteQlQuery({
      query: `
        SELECT
          id,
          companyname,
          custentity7 as iskeycustomer
        FROM Customer
        WHERE custentity7='T' AND category=1 and companyname is not null
      `
    });
    const divisionItems = divisionResponse.data.map((d) => ({ label: d.name, value: d.id, ...d }));
    const classItems = classResponse.data.map((c) => ({ label: c.name, value: c.id, ...c }));
    const regionItems = response.data.map((r) => ({ label: r.name, value: r.id, ...r }));
    const channelItems = channelResponse.data.map((c) => ({ label: c.name, value: c.id, ...c }));
    const customerItems = customerResponse.data.map((c) => ({ label: c.companyname, value: c.id, ...c }));
    setDoc(doc(db, 'forecastLists', 'divisions'), { items: divisionItems });
    setDoc(doc(db, 'forecastLists', 'classes'), { items: classItems });
    setDoc(doc(db, 'forecastLists', 'regions'), { items: regionItems });
    setDoc(doc(db, 'forecastLists', 'channels'), { items: channelItems });
    setDoc(doc(db, 'forecastLists', 'customers'), { items: customerItems });
    setLoading(false);
  };
  const fetchNodeList = async () => {
    setLoading(true);
    try {
      const response = await api.bigQueryRunQueryOnCall({
        options: {
          query: `SELECT region_name, region_id, division_name, division_id, class_name, class_id, channel_name, channel_id, customer_name, customer_id, code, location_id, location_name, source, msrp_discount, color, sales_predictor_method FROM \
            \`hj-reporting.forecast.forecast_nodes\``
        }
      });
      // console.log('response', response);
      setNodeList((response.data || []).map(row => ({
        region_name: row.region_name,
        region_id: row.region_id,
        division_name: row.division_name,
        division_id: row.division_id,
        class_name: row.class_name,
        class_id: row.class_id,
        channel_name: row.channel_name,
        channel_id: row.channel_id,
        customer_name: row.customer_name || '',
        customer_id: row.customer_id || '',
        code: row.code,
        location_id: row.location_id || '',
        location_name: row.location_name || '',
        source: row.source || '',
        msrp_discount: row.msrp_discount || '',
        color: row.color.replace(/['"]/g, '') || '',
        sales_predictor_method: row.sales_predictor_method || ''
      })));
    } catch (error) {
      message.error('Failed to load forecast nodes from BigQuery');
      setNodeList([]);
    }
    setLoading(false);
  };
  useEffect(() => {
    fetchNodeList();
  }, []);
  useEffect(() => {
    if (open) {
      fetchListValues();
    }
  }, [open]);
  const handleSave = async () => {
    await replaceBigQueryTable();
  };
  const replaceBigQueryTable = async () => {
    setLoading(true);
    const result = await api.bigQueryReplaceTableOnCall({
      datasetId: 'forecast',
      tableId: 'forecast_nodes',
      rows: nodeList
    });
    if (result.data && result.data.success) {
      setOriginalNodeList(nodeList);
      message.success('Forecast nodes saved to BigQuery!');
    } else {
      throw new Error(result.data?.error || 'Failed to save nodes.');
    }
    setLoading(false);
  };

  // Track unsaved changes
  useEffect(() => {
    setOriginalNodeList(nodeList);
  }, []);
  const hasChanges = JSON.stringify(nodeList) !== JSON.stringify(originalNodeList);
  const handleDelete = (node) => {
    setNodeList(prev => prev.filter(n =>
      !(
        n.region_name === node.region_name &&
        n.region_id === node.region_id &&
        n.division_name === node.division_name &&
        n.division_id === node.division_id &&
        n.class_name === node.class_name &&
        n.class_id === node.class_id &&
        n.channel_name === node.channel_name &&
        n.channel_id === node.channel_id &&
        n.customer_name === node.customer_name &&
        n.customer_id === node.customer_id
      )
    ));
  };

  const columns = [
    {
      title: 'Node',
      dataIndex: 'code',
      render: (text, record) => (
        <div>
          {text} {record.pendingUpload && <span style={{ color: 'blue' }}>*</span>}
        </div>
      ),
    },
    {
      title: 'Location',
      dataIndex: 'location_id',
      render: (text, record) => (
        <Select
          allowClear
          style={{ width: '150px' }}
          options={Object.entries(locObj).map(([key, value]) => ({ label: value, value: key }))}
          value={record.location_id?.toString()}
          onChange={(value) => {
            setNodeList(prev => prev.map(n => n.code === record.code ? { ...n, location_id: value, location_name: locObj[value] } : n));
          }}
        />
      ),
    },
    {
      title: 'Source',
      dataIndex: 'source',
      render: (text, record) => (
        <Select
          allowClear
          style={{ width: '150px' }}
          options={[{ label: 'Purchase', value: 'purchase' }, ...Object.entries(locObj).map(([key, value]) => ({ label: value, value: key }))]}
          value={record.source}
          onChange={(value) => {
            setNodeList(prev => prev.map(n => n.code === record.code ? { ...n, source: value } : n));
          }}
        />
      ),
    },
    {
      title: 'MSRP Discount',
      dataIndex: 'msrp_discount',
      render: (text, record) => (
        <Input
          type='number'
          min={0}
          max={100}
          value={record.msrp_discount}
          onChange={(e) => {
            let value = e.target.value;
            if (value === '') value = '';
            else {
              value = Math.max(0, Math.min(100, Number(value)));
            }
            setNodeList(prev => prev.map(n => n.code === record.code ? { ...n, msrp_discount: value } : n));
          }}
          addonAfter='%'
        />
      ),
    },
    {
      title: 'Color',
      dataIndex: 'color',
      render: (text, record) => (
        <ColorPicker
          value={record.color || '#000000'}
          onChange={(value) => {
            const newVal = value.toHexString().replace(/['"]/g, '');
            setNodeList(prev => prev.map(n => n.code === record.code ? { ...n, color: newVal } : n));
          }}
        />
      ),
    },
    {
      title: 'Sales Predictor Method',
      dataIndex: 'sales_predictor_method',
      render: (text, record) => (
        <Select
          style={{ width: '150px' }}
          value={record.sales_predictor_method}
          onChange={(value) => {
            setNodeList(prev => prev.map(n => n.code === record.code ? { ...n, sales_predictor_method: value } : n));
          }}
          options={Object.entries(SALES_PREDICTOR_METHODS).map(([key, value]) => ({ label: value, value: key }))}
        />
      ),
    },
    {
      title: 'Delete',
      dataIndex: 'delete',
      render: (text, record) => (
        <Button danger onClick={() => handleDelete(record)}>Delete</Button>
      ),
    },
  ];
  // console.log(nodeList);
  const NodeModal = ({ open, setOpen, selectedNode }) => {
    // Remove Form and use useState for each field
    const [saving, setSaving] = useState(false);
    const [region_name, setRegionName] = useState('1');
    const [region_id, setRegionId] = useState('');
    const [division_name, setDivisionName] = useState('');
    const [division_id, setDivisionId] = useState('');
    const [class_name, setClassName] = useState('');
    const [class_id, setClassId] = useState('');
    const [channel_name, setChannelName] = useState('');
    const [channel_id, setChannelId] = useState('');
    const [customer_name, setCustomerName] = useState('');
    const [customer_id, setCustomerId] = useState('');
    const [modalError, setModalError] = useState('');
    // Set initial values when opening modal
    useEffect(() => {
      if (open && selectedNode && Object.keys(selectedNode).length > 0) {
        setRegionName(selectedNode.region_name || selectedNode.region_name || '1');
        setRegionId(selectedNode.region_id || selectedNode.region_id || '');
        setDivisionName(selectedNode.division_name || selectedNode.division_name || '');
        setDivisionId(selectedNode.division_id || selectedNode.division_id || '');
        setClassName(selectedNode.class_name || selectedNode.class_name || '');
        setClassId(selectedNode.class_id || selectedNode.class_id || '');
        setChannelName(selectedNode.channel_name || selectedNode.channel_name || '');
        setChannelId(selectedNode.channel_id || selectedNode.channel_id || '');
        setCustomerName(selectedNode.customer_name || selectedNode.customer_name || '');
        setCustomerId(selectedNode.customer_id || selectedNode.customer_id || '');
      } else if (open && (!selectedNode || Object.keys(selectedNode).length === 0)) {
        setRegionName('');
        setRegionId('');
        setDivisionName('');
        setDivisionId('');
        setClassName('');
        setClassId('');
        setChannelName('');
        setChannelId('');
        setCustomerName('');
        setCustomerId('');
      }
    }, [open, selectedNode]);
    // Reset all values when modal closes
    useEffect(() => {
      if (!open) {
        setRegionName('1');
        setRegionId('');
        setDivisionName('');
        setDivisionId('');
        setClassName('');
        setClassId('');
        setChannelName('');
        setChannelId('');
        setCustomerName('');
        setCustomerId('');
      }
    }, [open]);
    // Helper to reset all input states
    const resetFields = () => {
      setRegionName('1');
      setRegionId('');
      setDivisionName('');
      setDivisionId('');
      setClassName('');
      setClassId('');
      setChannelName('');
      setChannelId('');
      setCustomerName('');
      setCustomerId('');
    };
    // Save to Firestore
    const handleSave = async () => {
      // Validate required fields
      if (!region_name || !division_name || !class_name || !channel_name || (class_name === '1' && !customer_name)) {
        setModalError('Please fill all required fields.');
        return;
      }
      // Check for duplicate node
      const isDuplicate = nodeList.some(node => {
        const sameRegion = (node.region_name || node.region_name) === region_name;
        const sameDivision = (node.division_name || node.division_name) === division_name;
        const sameClass = (node.class_name || node.class_name) === class_name;
        const sameChannel = (node.channel_name || node.channel_name) === channel_name;
        const sameCustomer = (node.customer_name || node.customer_name) === customer_name;
        if (node.customer_name) {
          return sameRegion && sameDivision && sameClass && sameChannel && sameCustomer;
        } else {
          return sameRegion && sameDivision && sameClass && sameChannel;
        }
      });
      if (isDuplicate) {
        setModalError('A node with this combination already exists.');
        return;
      }
      setModalError('');
      try {
        setSaving(true);

        // Get objects from lists
        const regionObj = (lists.find(l => l.id === 'regions')?.items || []).find(r => r.value === region_id);
        const divisionObj = (lists.find(l => l.id === 'divisions')?.items || []).find(d => d.value === division_id);
        const classObj = (lists.find(l => l.id === 'classes')?.items || []).find(c => c.value === class_id);
        const channelObj = (lists.find(l => l.id === 'channels')?.items || []).find(c => c.value === channel_id);
        let customerObj = undefined;
        if (class_id === '1' && customer_id) {
          customerObj = (lists.find(l => l.id === 'customers')?.items || []).find(c => c.value === customer_id);
        }

        // Create new node object
        const newNode = {
          region: { id: regionObj.value, name: regionObj.label },
          division: { id: divisionObj.value, name: divisionObj.label },
          class: { id: classObj.value, name: classObj.label },
          channel: { id: channelObj.value, name: channelObj.label },
          ...(customerObj ? { customer: { id: customerObj.value, name: customerObj.label } } : {}),
          nodeCode: regionObj.label + '_' + divisionObj.label + '_' + classObj.label + '_' + channelObj.label + (customerObj ? '_' + customerObj.label : ''),
          pendingUpload: true
        };

        // Prepare data for BigQuery load (normalize color)
        const bqData = {
          code: newNode.nodeCode,
          region_name: newNode.region.name,
          region_id: newNode.region.id,
          division_name: newNode.division.name,
          division_id: newNode.division.id,
          class_name: newNode.class.name,
          class_id: newNode.class.id,
          channel_name: newNode.channel.name,
          channel_id: newNode.channel.id,
          ...(newNode.customer ? {
            customer_name: newNode.customer.name,
            customer_id: newNode.customer.id
          } : {}),
          color: (newNode.color || '#000000').replace(/['"]/g, '')
        };
        setNodeList(prevNodes => [...prevNodes, bqData]);
        setSaving(false);
        setOpen(false);
        resetFields();
        setModalError('');
      } catch (error) {
        console.error('Save error:', error);
        message.error('Failed to save node: ' + error.message);
      } finally {
        setSaving(false);
      }
    };
    return (
      <Modal
        open={open}
        onCancel={() => {
          resetFields();
          setOpen(false);
          setModalError('');
        }}
        footer={null}
      >

        {loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
            <Spin size="large" />
          </div>
        ) : (
          <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
            <div>
              <label>Region</label>
              <Select
                style={{ width: '100%' }}
                value={region_name}
                onChange={(value) => {
                  setRegionId(value);
                  setRegionName(lists.find(l => l.id === 'regions')?.items.find(r => r.value === value)?.label || '');
                }}
                options={lists.find(l => l.id === 'regions')?.items}
                placeholder="Select a region"
              />
            </div>
            <div>
              <label>Division</label>
              <Select
                style={{ width: '100%' }}
                value={division_name}
                onChange={(value) => {
                  setDivisionId(value);
                  setDivisionName(lists.find(l => l.id === 'divisions')?.items.find(d => d.value === value)?.label || '');
                }}
                options={lists.find(l => l.id === 'divisions')?.items}
                placeholder="Select a division"
              />
            </div>
            <div>
              <label>Class</label>
              <Select
                style={{ width: '100%' }}
                value={class_name}
                onChange={(value) => {
                  setClassId(value);
                  setClassName(lists.find(l => l.id === 'classes')?.items.find(c => c.value === value)?.label || '');
                  setChannelId('');
                  setChannelName('');
                }}
                options={lists.find(l => l.id === 'classes')?.items}
                placeholder="Select a class"
              />
            </div>
            <div>
              <label>Channel</label>
              <Select
                style={{ width: '100%' }}
                value={channel_name}
                onChange={(value) => {
                  setChannelId(value);
                  setChannelName(lists.find(l => l.id === 'channels')?.items.find(c => c.value === value)?.label || '');
                }}
                options={(() => {
                  if (!class_id) return [];
                  const allChannels = lists.find(l => l.id === 'channels')?.items || [];
                  return allChannels.filter(c => c.classid === class_id);
                })()}
                placeholder="Select a channel"
                disabled={!class_id}
              />
            </div>
            {class_id === '1' && (
              <div>
                <label>Customer</label>
                <Select
                  style={{ width: '100%' }}
                  value={customer_name}
                  onChange={(value) => {
                    setCustomerId(value);
                    setCustomerName(lists.find(l => l.id === 'customers')?.items.find(c => c.value === value)?.label || '');
                  }}
                  options={lists.find(l => l.id === 'customers')?.items}
                  placeholder="Select a customer"
                  allowClear
                  showSearch
                  filterOption={(input, option) => option.label.toLowerCase().includes(input.toLowerCase())}
                />
              </div>
            )}
            {modalError && <div style={{ color: 'red', marginBottom: 8 }}>{modalError}</div>}
            <Button type="primary" onClick={handleSave} loading={saving} style={{ width: '100%' }}>Save</Button>
          </div>

        )}
      </Modal>
    );
  };
  return (
    <Layout>
      <Title level={3}>Forecast Nodes</Title>
      <Content>
        <Button onClick={() => {
          setSelectedNode({}); setOpen(true);
        }}>Add Node</Button>
        <Button onClick={handleSave} disabled={!hasChanges}>Save</Button>
        <Button icon={<SyncOutlined />} onClick={() => {
          fetchNetsuiteValues();
        }} />
        <Table dataSource={nodeList} columns={columns} loading={loading} rowKey={(record) => record.code} pagination={false} />
      </Content>
      {open && <NodeModal open={open} setOpen={setOpen} selectedNode={selectedNode} />}
    </Layout >
  );
};

export default NodeList;
