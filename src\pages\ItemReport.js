/* eslint-disable new-cap */
/* eslint-disable react/prop-types */
/* eslint-disable require-jsdoc */
/* eslint-disable guard-for-in */

// todo move inventory report to this page
// import logo from './logo.svg';
import '../App.css';
import {useEffect, useState} from 'react';
// import {writeFile, utils} from 'xlsx';
import * as ExcelJS from 'exceljs';
import {saveAs} from 'file-saver';
import React from 'react';
import Select from 'react-select';
import Header from '../components/headers';
import {useAuthState} from 'react-firebase-hooks/auth';
import {auth} from './firebase';
import {useNavigate} from 'react-router-dom';

function ItemReport({userObj}) {
  const [data, setData] = useState(null);
  const [lifeStatus, setLifeStatus] = useState([]);
  const [productType, setProductType] = useState([]);
  const [filters, setFilters] = useState({
    lifeStatus: '',
    productType: '',
    viewType:
      (userObj && userObj.permissions && userObj.permissions.defaultView) ?
        userObj.permissions.defaultView :
        '',
  });
  // console.log({filters});
  const viewMap = {
    marketing: [
      {id: 'productType', label: 'Product Type', canSort: true},
      {id: 'sku', label: 'SKU', canSort: true},
      {id: 'prodSpec', label: 'Prod Spec', canSort: true},
    ],
    operations: [
      {id: 'productType', label: 'Product Type', canSort: true},
      {id: 'sku', label: 'SKU', canSort: true},
      {id: 'upc', label: 'UPC', canSort: true},
      {id: 'prodSpec', label: 'Prod Spec', canSort: true},
      {id: 'color', label: 'Color', canSort: true},
    ],
  };

  const [sortKey, setSortKey] = useState('productType');
  const [sortOrder, setSortOrder] = useState('asc');

  const [user, authLoading] = useAuthState(auth);

  const getText = (val) => {
    if (typeof val === 'string') {
      return val;
    } else if (typeof val === 'object') {
      if (val.length === 0) return '';
      return val[0] ? val[0].text : '';
    } else {
      return '';
    }
  };

  const downloadExcel = async (data) => {
    const headerObj = {
      // 'id': 'Item ID',
      desc: 'Item Description',
      lifeStatus: 'Life Status',
      basePrice: 'Base Price',
      color: 'Color',
      productType: 'Product Type',
      sku: 'SKU',
      upc: 'UPC',
      qtyAvail: 'Qty Available',
      imageUrl: 'Image URL',
      launchDate: 'Launch Date',
    };
    // console.log('poLines', data);
    const headers = Object.values(headerObj);
    if (data.some((item) => item.poLines && item.poLines.length > 0)) {
      const maxLength = Math.max(
          ...data.map((item) => (item.poLines ? item.poLines.length : 0)),
      );
      // console.log('maxLength: ', maxLength);
      for (let i = 0; i < maxLength; i++) {
        headers.push(`Inbound ${i + 1} PO`);
        headers.push(`Inbound ${i + 1} Qty`);
        headers.push(`Inbound ${i + 1} Expected Date`);
      }
    }
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');
    worksheet.columns = headers;
    worksheet.addRow(headers);

    for (let rowInd = 0; rowInd < data.length; rowInd++) {
      const rowArray = data[rowInd];
      // your existing code to process rowArray and add extra headers...
      // add rowArray to wsData
      for (let i = 0; i < rowArray.length; i++) {
        const val = rowArray[i];
        if (i === 'poLines') {
          continue;
        }
        rowArray[i] = val.includes(',') ? `${val}` : val;
      }
      const cvalues = [];
      let hasUrl = false;
      let url = false;
      for (const header in headerObj) {
        if (header === 'imageUrl' && rowArray[header]) {
          hasUrl = true;
          url = rowArray[header];
        }
        cvalues.push(rowArray[header]);
      }
      if (rowArray.poLines) {
        for (let i = 0; i < rowArray.poLines.length; i++) {
          const line = rowArray.poLines[i];
          cvalues.push(`${line.poNumber}`);
          cvalues.push(line.qty);
          cvalues.push(`${line.expectedDate.toLocaleDateString()}`);
          // debugger
        }
      }
      worksheet.addRow(cvalues);
      if (hasUrl) {
        // console.log({rowInd, colInd: headers.indexOf('Image URL')});
        worksheet.getCell(rowInd + 2, headers.indexOf('Image URL') + 1).value =
          {
            text: url,
            hyperlink: url,
          };
      }
    }
    // create worksheet
    workbook.xlsx.writeBuffer().then((buffer) => {
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      saveAs(blob, 'output.xlsx');
    });
    // const ws = utils.aoa_to_sheet(wsData);

    // // create new workbook
    // const wb = utils.book_new();
    // utils.book_append_sheet(wb, ws, 'Sheet1');
    // write workbook to a file
    // writeFile(wb, 'output.xlsx');
  };

  const fetchItemData = async (locs) => {
    setData(null);
    let url =
      // eslint-disable-next-line max-len
      'https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=2418&deploy=1&compid=6810379&ns-at=AAEJ7tMQ-8spb70Ks4Zz8gBeb1WLtwrhjBx_OuvzRQUsbKLvYfk';
    if (locs) {
      url += `&locs=${locs}`;
    } else if (filters.locs) {
      url += `&locs=${filters.locs}`;
    }
    let res = await fetch(url);
    const tempData = await res.json();
    // console.log({tempData});
    const itemData = tempData.itemResults;
    // const itemObj = {};
    const finalData = [];
    for (const item of itemData) {
      // console.log({item});
      let launchDate = '';
      if (
        item.values['GROUP(custitem24)'] !== '' &&
        item.values['GROUP(custitem24)']
      ) {
        launchDate = `${new Date(
            getText(item.values['GROUP(custitem24)']),
        ).getFullYear()}-${(
          new Date(getText(item.values['GROUP(custitem24)'])).getMonth() + 1
        )
            .toString()
            .padStart(2, '0')}`;
      }
      const itemData = {
        id: getText(item['GROUP(upccode)']),
        desc: getText(item.values['GROUP(salesdescription)']),
        lifeStatus: getText(item.values['GROUP(custitem20)']),
        color: getText(item.values['GROUP(custitem_color)']),
        productType: getText(item.values['GROUP(custitem_product_type)']),
        sku: getText(item.values['GROUP(externalid)']),
        upc: getText(item.values['GROUP(upccode)']),
        qtyAvail: getText(item.values['SUM(formulanumeric)']),
        imageUrl: getText(item.values.imageurl),
        qtyOnHand: getText(item.values['SUM(locationquantityonhand)']),
        launchDate,
        basePrice: item.values['MAX(baseprice)'],
        prodSpec: getText(item.values['GROUP(custitem2)']),
      };
      // if (!itemObj[itemData.id]) {
      //   itemObj[itemData.id] = itemData;
      // }
      // itemObj[itemData.id].qtyAvail +=
      //   (item.values.locationquantityavailable ?
      //     parseFloat(item.values.locationquantityavailable) :
      //     0) -
      //   (item.values.locationquantitybackordered ?
      //     parseFloat(
      //         item.values.locationquantityavailable -
      //           item.values.locationquantitybackordered,
      //     ) :
      //     0);
      finalData.push(itemData);
    }
    // for (const key in itemObj) {
    // }
    if (locs) {
      url += `&locs=${locs}`;
    } else if (filters.locs) {
      url += `&locs=${filters.locs}`;
    }
    // eslint-disable-next-line max-len
    url = `https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=2417&deploy=1&compid=6810379&ns-at=AAEJ7tMQgO545hv9RpDZohHE4uawyHEvVJRc0BIlSFL0j8af2Ww&reqType=json&locs=${locs}`;
    res = await fetch(url);
    const poData = await res.json();
    // console.log('poData', poData);
    for (const d of finalData) {
      const sku = d.sku;
      if (!d.poLines) {
        d.poLines = [];
      }
      for (const line of poData) {
        if (line[2] === sku) {
          const cobj = {
            poNumber: line[0],
            qty: line[5],
            expectedDate: new Date(line[6]),
            type: line[8],
          };
          if (line[8] == 'Inbound') {
            cobj.containerNum = line[9];
          }
          d.poLines.push(cobj);
          // console.log('has po lines', line[2]);
        }
      }
    }
    // console.log(
    //   "setting po data",
    //   data.filter((x) => x.poLines.length > 0)
    // );
    for (const d of finalData) {
      d.totalIncoming = 0;
      if (d.poLines.length > 0) {
        d.totalIncoming = d.poLines.reduce((a, b) => a + parseInt(b.qty), 0);
        const sortedLines = d.poLines.sort(
            (a, b) => a.expectedDate - b.expectedDate,
        );
        // console.log(sortedLines);
        // d.poLines = sortedLines;
        d.expectedDate =
          sortedLines[0].expectedDate.toLocaleDateString() +
          ` (${sortedLines[0].qty})`;
      }
    }
    const onlyActive = finalData.filter((item) => {
      // Phasing out items
      if (item.lifeStatus == 'Phasing Out') {
        // None incoming
        if (!item.totalIncoming || item.totalIncoming < 1) {
          // None on hand
          if (!item.qtyOnHand || item.qtyOnHand < 1) {
            return false;
          }
        }
      }
      return true;
    });
    // console.log('Lengths', {
    //   finalData: finalData.length,
    //   onlyActive: onlyActive.length,
    // });
    setData(onlyActive);
    // get unique life status
    const uniqueLifeStatus = [
      ...new Set(finalData.map((item) => item.lifeStatus)),
    ];
    setLifeStatus(uniqueLifeStatus);
    // get unique product type
    const uniqueProductType = [
      ...new Set(finalData.map((item) => item.productType)),
    ];
    setProductType(uniqueProductType);
  };

  const navigate = useNavigate();
  useEffect(() => {
    if (authLoading) {
      // maybe trigger a loading screen
      return;
    }
    if (!user) navigate('/login');
  }, [user, authLoading]);

  useEffect(() => {
    fetchItemData();
  }, []);

  const updateFilterHeight = () => {
    const el = document.querySelector('div.filters');
    if (!el || !el.clientHeight) {
      return false;
    }
    // console.log('ClientHeight', el.clientHeight);
    document.documentElement.style.setProperty(
        '--filter-height',
        `${el.clientHeight}px`,
    );
    return true;
  };

  useEffect(() => {
    updateFilterHeight();
  }, [filters]);
  // console.log({data: data});
  if (!data) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <h1>Loading...</h1>
      </div>
    );
  }

  // console.log({productType: filters.productType});

  const filterList = (item) => {
    if (
      filters.lifeStatus.length > 0 &&
      !filters.lifeStatus.map((x) => x.value).includes(item.lifeStatus)
    ) {
      return false;
    }
    if (filters.inStock === 'true' && item.qtyAvail <= 0) {
      return false;
    }
    if (filters.inStock === 'false' && item.qtyAvail > 0) {
      return false;
    }
    if (
      filters.productType.length > 0 &&
      !filters.productType.map((x) => x.value).includes(item.productType)
    ) {
      return false;
    }
    if (
      filters.search &&
      !item.sku.toLowerCase().includes(filters.search.toLowerCase()) &&
      !item.desc.toLowerCase().includes(filters.search.toLowerCase()) &&
      !item.productType.toLowerCase().includes(filters.search.toLowerCase()) &&
      !item.upc.toLowerCase().includes(filters.search.toLowerCase()) &&
      !item.prodSpec.toLowerCase().includes(filters.search.toLowerCase())
    ) {
      return false;
    }
    return true;
  };
  const handleSort = (key) => {
    if (sortKey === key) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortOrder('asc');
      setSortKey(key);
    }
  };

  const calcClass = (key) => {
    if (sortKey === key) {
      return sortOrder === 'asc' ? 'sortAsc' : 'sortDesc';
    }
    return '';
  };
  if (!userObj || !userObj.permissions.itemReportViews.length) {
    navigate('/');
  }
  const toProperCase = (str) => {
    const newStr = str
        .split(' ')
        .map((w) => w[0].toUpperCase() + w.substring(1).toLowerCase())
        .join(' ');
    // console.log(newStr);
    return newStr;
  };

  return (
    <div className="page">
      <Header userObj={userObj} />
      <div>
        <h1>Item Report</h1>
      </div>
      <div className="filters">
        <div className="filter-item">
          <label htmlFor="productTypeFilter">View</label>
          <Select
            id="viewTypeFilter"
            defaultValue={filters.viewType}
            onChange={(e) => {
              // console.log('e', e);
              setFilters({...filters, viewType: e.value});
            }}
            options={userObj.permissions.itemReportViews.map((x) => {
              return {
                value: x.toLowerCase(),
                label: toProperCase(x),
              };
            })}
            classNamePrefix={'react-select'}
            styles={{
              container: (provided) => ({
                ...provided,
                minWidth: '200px',
                maxWidth: '250px',
                display: 'inline-block',
              }),
            }}
            isMulti={false}
            value={filters.viewType}
          />
        </div>
        <div className="filter-item">
          <label htmlFor="productTypeFilter">Product Type</label>
          <Select
            id="productTypeFilter"
            onChange={(e) => {
              setFilters({...filters, productType: e});
            }}
            options={productType.map((val) => {
              return {
                value: val,
                label: val,
              };
            })}
            classNamePrefix={'react-select'}
            styles={{
              container: (provided) => ({
                ...provided,
                minWidth: '200px',
                maxWidth: '250px',
                display: 'inline-block',
              }),
            }}
            isMulti={true}
            value={filters.productType}
          />
        </div>
        <div className="filter-item">
          <label htmlFor="lifeStatusFilter">Life Status</label>
          <Select
            id="lifeStatusFilter"
            onChange={(e) => setFilters({...filters, lifeStatus: e})}
            options={lifeStatus.map((val, i) => {
              return {
                value: val,
                label: val,
              };
            })}
            classNamePrefix={'react-select'}
            styles={{
              container: (provided) => ({
                ...provided,
                maxWidth: '250px',
                display: 'inline-block',
              }),
            }}
            isMulti={true}
            value={filters.lifeStatus}
          />
        </div>
        <div className="filter-item">
          <label htmlFor="instockFilter">Stock Status</label>
          <select
            id="instockFilter"
            onChange={(e) =>
              setFilters({...filters, inStock: e.target.value})
            }
            value={filters.inStock}
          >
            <option value="all">All</option>
            <option value="true">In Stock</option>
            <option value="false">Out of Stock</option>
          </select>
        </div>
        <div className="filter-item">
          <label htmlFor="searchFilter">Search</label>
          <input
            id="searchFilter"
            type="text"
            placeholder="Search"
            value={filters.search}
            onChange={(e) => setFilters({...filters, search: e.target.value})}
          />
        </div>
        <button
          onClick={() => {
            const locs = '3,11,9,10,19';
            if (filters.locs != '3,11,9,10,19') {
              fetchItemData(locs);
            }
            setFilters({
              search: '',
              lifeStatus: [],
              inStock: 'all',
              productType: [],
              locs,
            });
          }}
        >
          Clear Filters
        </button>
        <button
          onClick={() => downloadExcel(data.filter((x) => filterList(x)))}
        >
          Download CSV
        </button>
      </div>
      <div className="results">
        {filters.viewType ? (
          <table>
            <thead>
              <tr className="itemReportTableHeaders">
                <th>Image</th>
                {filters.viewType &&
                  viewMap[filters.viewType].map((obj) => {
                    // console.log('obj', obj);
                    if (obj.canSort) {
                      return (
                        <th
                          onClick={() => handleSort(obj.id)}
                          className={calcClass(obj.id)}
                          key={obj.id}
                        >
                          {obj.label}
                        </th>
                      );
                    }
                    return (
                      <th key={obj.id} className={obj.id}>
                        {obj.label}
                      </th>
                    );
                  })}
              </tr>
            </thead>
            <tbody>
              {data
                  .filter((x) => filterList(x))
                  .sort((a, b) => {
                    let first = a[sortKey];
                    let second = b[sortKey];
                    if (sortKey === 'expectedDate' || sortKey === 'launchDate') {
                    // console.log('date', a[sortKey]);
                      first = new Date(
                      !a[sortKey] ? null : a[sortKey].split('(')[0],
                      );
                      second = new Date(
                      !b[sortKey] ? null : b[sortKey].split('(')[0],
                      );
                    }
                    if (sortKey.includes('qty')) {
                      first = parseInt(!a[sortKey] ? 0 : a[sortKey]);
                      second = parseInt(!b[sortKey] ? 0 : b[sortKey]);
                    }
                    if (sortKey === 'totalIncoming') {
                      first = parseInt(!a[sortKey] ? 0 : a[sortKey]);
                      second = parseInt(!b[sortKey] ? 0 : b[sortKey]);
                    }
                    if (sortOrder === 'desc') {
                      const temp = first;
                      first = second;
                      second = temp;
                    }
                    if (first < second) {
                      return -1;
                    }
                    if (first > second) {
                      return 1;
                    }
                    return 0;
                  })
                  .map((row, i) => {
                    return (
                      <tr
                        key={i}
                      >
                        <td>
                          <img src={row.imageUrl} height={100} />
                        </td>
                        {viewMap[filters.viewType].map((vtCol, i) => {
                          // console.log(vtCol);
                          return <td key={i}>{row[vtCol.id]}</td>;
                        })}
                      </tr>
                    );
                  })}
            </tbody>
          </table>
        ) : (
          <h2>Pick a view</h2>
        )}
      </div>
    </div>
  );
}
export default ItemReport;
