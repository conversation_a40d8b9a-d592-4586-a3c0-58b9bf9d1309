/* eslint-disable react/jsx-key */
/* eslint-disable require-jsdoc */
import React from 'react';
import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import { signOut } from 'firebase/auth';
import { auth } from './pages/firebase';

import './App.css';
import './components/forecast/ItemNodeMatrix.css';
import InventoryReport from './pages/InventoryReport';
import Login from './pages/Login';
import ItemReport from './pages/ItemReport';
import ItemTable from './pages/Items.js';
import Wholesale from './pages/Wholesale';
import ARReport from './pages/ArReport.js.js';
// import OpsForecast from './pages/OpsForecast.js';
import Header from './components/headers.js';
import Settings from './pages/Settings.js';
import KPIDashboard from './pages/KPIDashboard.js';
// import PimVariantTable from './pages/PIM/PimVariantTable.js';
// import PimProductTable from './pages/PIM/PimProductTable.js';
// import PimProductEdit from './pages/PIM/PimProductEdit.js';
import UploadData from './pages/UploadData.js';
import Utilities from './pages/Utilities.js';
import Data from './pages/Data.js';
import ShippingExceptionReport from './pages/ShippingExceptions.js';
import InventoryExceptionReport from './pages/InventoryExceptionReport.js';
import HistoricalSalesPage from './pages/HistoricalSalesPage.jsx';
import ForecastBeta from './pages/ForecastBeta';
import OrderAllocation from './pages/OrderAllocation';
import { ModuleRegistry } from "ag-grid-community";
import { AllEnterpriseModule, LicenseManager, IntegratedChartsModule } from "ag-grid-enterprise";
import { AgChartsEnterpriseModule } from "ag-charts-enterprise";
import { UserProvider, useUser } from './contexts/UserContext';
import ErrorBoundary from './components/ErrorBoundary';

ModuleRegistry.registerModules([
  AllEnterpriseModule,
  IntegratedChartsModule.with(AgChartsEnterpriseModule)
]);

LicenseManager.setLicenseKey("Using_this_{AG_Charts_and_AG_Grid}_Enterprise_key_{AG-088250}_in_excess_of_the_licence_granted_is_not_permitted___Please_report_misuse_to_legal@ag-grid.com___For_help_with_changing_this_key_please_contact_info@ag-grid.com___{HydroJug}_is_granted_a_{Multiple_Applications}_Developer_License_for_{1}_Front-End_JavaScript_developer___All_Front-End_JavaScript_developers_need_to_be_licensed_in_addition_to_the_ones_working_with_{AG_Charts_and_AG_Grid}_Enterprise___This_key_has_not_been_granted_a_Deployment_License_Add-on___This_key_works_with_{AG_Charts_and_AG_Grid}_Enterprise_versions_released_before_{15_May_2026}____[v3]_[0102]_MTc3ODc5OTYwMDAwMA==1c4ba535e88c2ab75650260e161f46c2");

function AppContent() {
  const { user, userData, loading, isAuthenticated } = useUser();

  // Show loading while authentication state is being determined
  if (loading) {
    return (
      <div style={{ 
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <div>Loading...</div>
      </div>
    );
  }

  // If no user at all, show login
  if (!user) {
    return (
      <Router>
        <Routes>
          <Route path="*" element={<Login />} />
        </Routes>
      </Router>
    );
  }

  // If user exists but userData is still loading, show loading
  if (user && !userData) {
    return (
      <div style={{ 
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <div>Loading user permissions...</div>
      </div>
    );
  }

  // Check if user has proper permissions
  if (!userData.userPermissions || !Array.isArray(userData.userPermissions)) {
    console.log('userData', userData);
    // User document doesn't exist or is malformed - redirect to login
    signOut(auth);
    return (
      <Router>
        <Routes>
          <Route path="*" element={<Login />} />
        </Routes>
      </Router>
    );
  }

  const routeList = [
    <Route exact path="/login" element={<Login />} />,
    <Route
      exact
      path="/"
      key={'read:inventoryreport'}
      element={<InventoryReport userObj={userData} />}
    />,
    <Route
      exact
      path="/itemreport"
      key={'read:itemreport'}
      element={<ItemReport userObj={userData} />}
    />,
    // <Route
    //   exact
    //   path="/items"
    //   key={'read:items'}
    //   element={<ItemTable userObj={userData} />}
    // />,
    // <Route
    //   exact
    //   path="/wholesale"
    //   key={'read:wholesale'}
    //   element={<Wholesale userObj={userData} />}
    // />,
    <Route
      exact
      path="/arreport"
      key={'read:arreport'}
      element={<ARReport userObj={userData} />}
    />,
    <Route
      exact
      path="/settings"
      key={'read:settings'}
      element={<Settings />}
    />,
    // <Route
    //   exact
    //   path="/dashboard"
    //   key={'read:dashboard'}
    //   element={<KPIDashboard userObj={userData} />}
    // />,
    <Route
      exact
      path="/uploadData"
      key={'read:upload'}
      element={<UploadData userObj={userData} />}
    />,
    <Route
      exact
      path="/data"
      key={'read:data'}
      element={<Data userObj={userData} />}
    />,
    <Route
      exact
      path="/utilities"
      key={'read:utilities'}
      element={<Utilities userObj={userData} />}
    />,
    <Route
      exact
      path="/shippingExceptions"
      key={'read:shippingExceptions'}
      element={<ShippingExceptionReport userObj={userData} />}
    />,
    <Route
      exact
      path="/inventoryExceptions"
      key={'read:inventoryExceptions'}
      element={<InventoryExceptionReport />}
    />,
    <Route
      exact
      path="/order-allocation"
      key={'read:orderAllocation'}
      element={<OrderAllocation />}
    />,
    <Route
      exact
      path="/forecastBeta"
      key={'read:forecastBeta'}
      element={<ForecastBeta userObj={userData} />}
    />,
    <Route
      exact
      path="/historicalSales"
      key={'read:historicalSales'}
      element={<HistoricalSalesPage />}
    />,
  ];

  const userUrls = userData.userPermissions.filter((x) => x.hasAccess).map((x) => x.technicalName);
  userUrls.push('read:utilities');
  userUrls.push('read:orderAllocation');
  
  const userRoutes = routeList.filter((route) => userUrls.includes(route.key));
  
  return (
    <div className="app">
      <Router>
        <Header userObj={userData} />
        <Routes>
          <>
            {userRoutes.map((x) => x)}
          </>
        </Routes>
      </Router>
    </div>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <UserProvider>
        <AppContent />
      </UserProvider>
    </ErrorBoundary>
  );
}

export default App;
