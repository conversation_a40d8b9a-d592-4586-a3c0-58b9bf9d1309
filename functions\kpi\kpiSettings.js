const {onCall} = require("firebase-functions/v2/https");
const admin = require("firebase-admin");


// KPI Goals

// Default KPI Goals with goal conditions
const defaultKPIGoals = {
  "Financial KPIs": {
    "DIO": {
      value: "30",
      goalCondition: "lower",
      unit: "days",
    },
    "DPO": {
      value: "60",
      goalCondition: "higher",
      unit: "days",
    },
    "Cost of Goods Sold (COGS) % (Profit Margin)": {
      value: "18",
      goalCondition: "lower",
      unit: "%",
    },
    "Inbound Transportation Costs": {
      value: "3",
      goalCondition: "lower",
      unit: "%",
    },
    "Outbound Shipping Costs": {
      value: "13",
      goalCondition: "lower",
      unit: "%",
    },
  },
  "Inventory Management KPIs": {
    "Inventory Turnover Ratio": {
      value: "6",
      goalCondition: "higher",
      unit: "",
    },
    "Stock Out Percentage": {
      value: "3",
      goalCondition: "lower",
      unit: "%",
    },
    "Backorder Rate": {
      value: "1",
      goalCondition: "lower",
      unit: "%",
    },
    "Inventory dollar projected based on forecasted sales and forecasted inventory": {
      value: "1.5",
      goalCondition: "inRange",
      unit: ":1",
    },
    "Safety Stock Goal": {
      min: "1",
      max: "2",
      goalCondition: "inRange",
      unit: "months",
    },
  },
  "Order Management KPIs": {
    "Order Fulfillment Cycle Time": {
      min: "1",
      max: "3",
      goalCondition: "lowerThanMax",
      unit: "days",
    },
    "Perfect Order Rate": {
      min: "95",
      max: "99",
      goalCondition: "higherThanMin",
      unit: "%",
    },
    "Return Rate": {
      min: "2",
      max: "5",
      goalCondition: "lowerThanMax",
      unit: "%",
    },
    "Order Accuracy Rate": {
      min: "98",
      max: "99.9",
      goalCondition: "higherThanMin",
      unit: "%",
    },
    "Total Unfulfilled Orders": {
      value: "5",
      goalCondition: "lower",
      unit: "%",
    },
    "On-Time Delivery (OTD) Score": {
      min: "95",
      max: "98",
      goalCondition: "higherThanMin",
      unit: "%",
    },
  },
  "Supplier Management KPIs": {
    "Supplier Defect Rate": {
      value: "1",
      goalCondition: "lower",
      unit: "%",
    },
    "Supplier Lead Time": {
      min: "2",
      max: "4",
      goalCondition: "lowerThanMax",
      unit: "weeks",
    },
    "Supplier Quality Score": {
      min: "95",
      max: "100",
      goalCondition: "higherThanMin",
      unit: "%",
    },
  },
  "Forecasting and Planning KPIs": {
    "Forecast Accuracy": {
      min: "90",
      max: "95",
      goalCondition: "higherThanMin",
      unit: "%",
    },
    "Procurement Cycle Time": {
      min: "1",
      max: "2",
      goalCondition: "lowerThanMax",
      unit: "weeks",
    },
  },
  "Production and Efficiency KPIs": {
    "Capacity Utilization": {
      min: "85",
      max: "90",
      goalCondition: "higherThanMin",
      unit: "%",
    },
    "Production Efficiency": {
      min: "85",
      max: "95",
      goalCondition: "higherThanMin",
      unit: "%",
    },
    "Utilization Rate": {
      min: "85",
      max: "95",
      goalCondition: "higherThanMin",
      unit: "%",
    },
  },
  "Product Development KPIs": {
    "Timelines Hit Score": {
      value: "0",
      goalCondition: "higher",
      unit: "",
    },
    "Performance of Product Types and Colors": {
      value: "0",
      goalCondition: "higher",
      unit: "",
    },
    "Product Lifecycle Visual Score": {
      value: "0",
      goalCondition: "higher",
      unit: "",
    },
  },
};

// Function to initialize KPI goals
exports.initializeKPIGoals = onCall({
  enforceAppCheck: false,
  maxInstances: 10,
}, async () => {
  try {
    await admin
        .firestore()
        .collection("settings")
        .doc("kpiGoals")
        .set(defaultKPIGoals);

    return {
      success: true,
      message: "KPI goals initialized successfully",
      data: defaultKPIGoals,
    };
  } catch (error) {
    console.error("Error initializing KPI goals:", error);
    throw new Error("Error initializing KPI goals");
  }
});

// Helper function to validate KPI goal structure
const validateKPIGoalStructure = (kpi, data) => {
  const requiredFields = Object.prototype.hasOwnProperty.call(data, "min") && Object.prototype.hasOwnProperty.call(data, "max") ?
      ["min", "max", "goalCondition", "unit"] :
      ["value", "goalCondition", "unit"];

  const missingFields = requiredFields.filter((field) => !Object.prototype.hasOwnProperty.call(data, field));

  if (missingFields.length > 0) {
    throw new Error(`Missing required fields for KPI "${kpi}": ${missingFields.join(", ")}`);
  }

  const validGoalConditions = ["higher", "lower", "inRange", "higherThanMin", "lowerThanMax"];
  if (!validGoalConditions.includes(data.goalCondition)) {
    throw new Error(`Invalid goal condition for KPI "${kpi}": ${data.goalCondition}`);
  }
};

// Function to get KPI goals
exports.getKPIGoals = onCall({
  enforceAppCheck: false,
  maxInstances: 10,
}, async () => {
  try {
    const doc = await admin
        .firestore()
        .collection("settings")
        .doc("kpiGoals")
        .get();

    if (!doc.exists) {
      // If no goals exist, initialize with defaults
      await admin
          .firestore()
          .collection("settings")
          .doc("kpiGoals")
          .set(defaultKPIGoals);

      return defaultKPIGoals;
    }

    return doc.data();
  } catch (error) {
    console.error("Error getting KPI goals:", error);
    throw new Error("Error getting KPI goals");
  }
});

// Function to update KPI goals
exports.updateKPIGoals = onCall({
  enforceAppCheck: false,
  maxInstances: 10,
}, async (request) => {
  try {
    const {kpiGoals} = request.data;
    if (!kpiGoals) {
      throw new Error("Missing KPI goals data in request");
    }

    // Validate the structure of the KPI goals
    const requiredCategories = Object.keys(defaultKPIGoals);
    const receivedCategories = Object.keys(kpiGoals);

    // Check if all required categories are present
    const missingCategories = requiredCategories.filter(
        (category) => !receivedCategories.includes(category),
    );

    if (missingCategories.length > 0) {
      throw new Error(`Missing required KPI categories: ${missingCategories.join(", ")}`);
    }

    // Check if each category has all required KPIs and validate their structure
    for (const category of requiredCategories) {
      const requiredKPIs = Object.keys(defaultKPIGoals[category]);
      const receivedKPIs = Object.keys(kpiGoals[category] || {});

      const missingKPIs = requiredKPIs.filter(
          (kpi) => !receivedKPIs.includes(kpi),
      );

      if (missingKPIs.length > 0) {
        throw new Error(`Missing required KPIs in ${category}: ${missingKPIs.join(", ")}`);
      }

      // Validate structure of each KPI
      for (const kpi of requiredKPIs) {
        validateKPIGoalStructure(kpi, kpiGoals[category][kpi]);
      }
    }

    await admin
        .firestore()
        .collection("settings")
        .doc("kpiGoals")
        .set(kpiGoals);

    return {
      success: true,
      message: "KPI goals updated successfully",
      data: kpiGoals,
    };
  } catch (error) {
    console.error("Error updating KPI goals:", error);
    throw new Error(error.message || "Error updating KPI goals");
  }
});

// Function to reset KPI goals to defaults
exports.resetKPIGoals = onCall({
  enforceAppCheck: false,
  maxInstances: 10,
}, async () => {
  try {
    await admin
        .firestore()
        .collection("settings")
        .doc("kpiGoals")
        .set(defaultKPIGoals);

    return {
      success: true,
      message: "KPI goals reset to defaults successfully",
      data: defaultKPIGoals,
    };
  } catch (error) {
    console.error("Error resetting KPI goals:", error);
    throw new Error("Error resetting KPI goals");
  }
});

// Function to get KPI goals for a specific report
exports.getKPIGoalsForReport = onCall({
  enforceAppCheck: false,
  maxInstances: 10,
}, async (request) => {
  try {
    const {reportName} = request.data;
    if (!reportName) {
      throw new Error("Missing reportName in request data");
    }

    const doc = await admin
        .firestore()
        .collection("settings")
        .doc("kpiGoals")
        .get();

    let goals = {};

    if (!doc.exists) {
      await admin
          .firestore()
          .collection("settings")
          .doc("kpiGoals")
          .set(defaultKPIGoals);
      goals = defaultKPIGoals;
    } else {
      goals = doc.data();
    }

    const reportGoals = {};

    // Complete mapping of report names to KPIs
    const kpiMappings = {
      // Financial KPIs
      "dio": ["DIO"],
      "dpo": ["DPO"],
      "cogs": ["Cost of Goods Sold (COGS) % (Profit Margin)"],
      "inbound-transportation": ["Inbound Transportation Costs"],
      "outbound-transportation": ["Outbound Shipping Costs"],

      // Inventory Management KPIs
      "inventory-turnover": ["Inventory Turnover Ratio"],
      "stock-out": ["Stock Out Percentage"],
      "backorder": ["Backorder Rate"],
      "inventory-projection": ["Inventory dollar projected based on forecasted sales and forecasted inventory"],
      "safety-stock": ["Safety Stock Goal"],

      // Order Management KPIs
      "order-fulfillment": ["Order Fulfillment Cycle Time"],
      "perfect-order": ["Perfect Order Rate"],
      "return-rate": ["Return Rate"],
      "order-accuracy": ["Order Accuracy Rate"],
      "unfulfilled-orders": ["Total Unfulfilled Orders"],
      "otd": ["On-Time Delivery (OTD) Score"],

      // Supplier Management KPIs
      "supplier-defect": ["Supplier Defect Rate"],
      "supplier-quality": ["Supplier Quality Score"],
      "supplier-lead-time": ["Supplier Lead Time"],

      // Forecasting and Planning KPIs
      "forecast-accuracy": ["Forecast Accuracy"],
      "procurement-cycle": ["Procurement Cycle Time"],

      // Production and Efficiency KPIs
      "capacity-utilization": ["Capacity Utilization"],
      "production-efficiency": ["Production Efficiency"],
      "utilization-rate": ["Utilization Rate"],

      // Product Development KPIs
      "timelines-score": ["Timelines Hit Score"],
      "product-performance": ["Performance of Product Types and Colors"],
      "lifecycle-score": ["Product Lifecycle Visual Score"],

      // Multiple KPI Reports
      "inventory-overview": [
        "Inventory Turnover Ratio",
        "Stock Out Percentage",
        "Safety Stock Goal",
      ],
      "order-management-overview": [
        "Order Fulfillment Cycle Time",
        "Perfect Order Rate",
        "Return Rate",
        "Order Accuracy Rate",
      ],
      "supplier-overview": [
        "Supplier Defect Rate",
        "Supplier Quality Score",
        "Supplier Lead Time",
        "On-Time Delivery (OTD) Score",
      ],
      "financial-overview": [
        "DIO",
        "DPO",
        "Cost of Goods Sold (COGS) % (Profit Margin)",
        "Inbound Transportation Costs",
        "Outbound Shipping Costs",
      ],
    };

    Object.entries(goals).forEach(([category, kpis]) => {
      Object.entries(kpis).forEach(([kpiName, kpiConfig]) => {
        const relevantKPIs = kpiMappings[reportName.toLowerCase()] || [];
        if (relevantKPIs.includes(kpiName)) {
          reportGoals[kpiName] = {
            ...kpiConfig,
            category,
          };
        }
      });
    });

    if (Object.keys(reportGoals).length === 0) {
      throw new Error(`No KPI goals found for report: ${reportName}`);
    }

    return {
      success: true,
      data: reportGoals,
    };
  } catch (error) {
    console.error(`Error getting KPI goals for report ${request.data && request.data.reportName}:`, error);
    throw new Error(error.message || `Error getting KPI goals for report`);
  }
});
