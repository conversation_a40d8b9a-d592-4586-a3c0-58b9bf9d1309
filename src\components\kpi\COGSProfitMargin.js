import React, { useState, useEffect } from 'react';
import { Box, Grid, Paper, CircularProgress, Alert, Typography } from '@mui/material';
import {
    Composed<PERSON>hart, Line, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import { useFormatAxisTick } from '../../hooks/useFormatAxisTick';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import ChartExportWrapper from './ChartExportWrapper';

const COGSProfitMargin = () => {
    const [cogsData, setCOGSData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const formatAxisTick = useFormatAxisTick();

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const functions = getFunctions();
            const getCogsPercentage = httpsCallable(functions, 'getCogsPercentage');
            const getKPIGoalsForReport = httpsCallable(functions, 'getKPIGoalsForReport');

            const [cogsResult, goalsResult] = await Promise.all([
                getCogsPercentage(),
                getKPIGoalsForReport({ reportName: 'cogs' })
            ]);

            setCOGSData(cogsResult.data);

            if (goalsResult.data.success) {
                setKpiGoals(goalsResult.data.data);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            setError('Failed to fetch data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    const calculateCOGSTrend = () => {
        if (!cogsData?.runningData || cogsData.runningData.length < 2) return 0;
        const lastTwo = cogsData.runningData.slice(-2);
        return lastTwo[1].cogsPercentage - lastTwo[0].cogsPercentage;
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                    <Typography variant="body2">{`Date: ${label}`}</Typography>
                    <Typography variant="body1" color="error" fontWeight="bold">
                        {`COGS %: ${data.cogsPercentage.toFixed(2)}%`}
                    </Typography>
                    <Typography variant="body2" color="primary">
                        {`Revenue: $${data.revenue.toLocaleString()}`}
                    </Typography>
                    <Typography variant="body2" color="secondary">
                        {`COGS: $${data.cogs.toLocaleString()}`}
                    </Typography>
                </Paper>
            );
        }
        return null;
    };


    // Calculate domains including the goal values
    const calculateDomains = () => {
        if (!cogsData?.runningData) return { cogsDomain: [0, 100], amountDomain: [0, 1000] };

        const cogsValues = cogsData.runningData.map(item => item.cogsPercentage);
        const amountValues = cogsData.runningData.map(item => Math.max(item.revenue, item.cogs));

        // Include goal values in COGS domain calculation
        const goalValue = kpiGoals?.['Cost of Goods Sold (COGS) % (Profit Margin)']?.value;
        const maxCOGS = Math.max(...cogsValues, goalValue ? parseFloat(goalValue) : 0);

        return {
            cogsDomain: [0, Math.ceil(maxCOGS * 1.1)],
            amountDomain: [0, Math.ceil(Math.max(...amountValues) * 1.1)]
        };
    };

    // Use the KPI reference lines hook
    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: kpiGoals?.['Cost of Goods Sold (COGS) % (Profit Margin)'],
        yAxisId: "left",
        styles: {
            stroke: "#ff9800",
            strokeDasharray: "3 3",
            label: {
                fill: "#ff9800",
                fontSize: 12,
                position: "right"
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Min",
            max: "Max"
        }
    });

    if (isLoading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Alert severity="error" sx={{ mt: 2 }}>
                {error}
            </Alert>
        );
    }

    if (!cogsData || !kpiGoals) return null;

    const { cogsDomain, amountDomain } = calculateDomains();
    const cogsConfig = kpiGoals['Cost of Goods Sold (COGS) % (Profit Margin)'];

    return (
        <ChartExportWrapper title="COGS_Profit_Margin">
            <Box sx={{ p: { xs: 2, sm: 3 }, mb: 3 }}>
                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <ResponsiveContainer width="100%" height={500}>
                        <ComposedChart data={cogsData.runningData}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                            <XAxis dataKey="date" />
                            <YAxis
                                yAxisId="left"
                                domain={cogsDomain}
                                label={{ value: 'COGS %', angle: -90, position: 'insideLeft' }}
                            />
                            <YAxis
                                yAxisId="right"
                                orientation="right"
                                domain={amountDomain}
                                label={{ value: 'Amount', angle: 90, position: 'insideRight' }}
                                tickFormatter={formatAxisTick}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            {goalReferenceLines}
                            <Bar yAxisId="right" dataKey="cogs" fill="#b3ffb3" name="COGS" />
                            <Bar yAxisId="right" dataKey="revenue" fill="#b3e0ff" name="Revenue" />
                            <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="cogsPercentage"
                                stroke="#ff0000"
                                strokeWidth={3}
                                dot={{ r: 4, fill: "#ff0000" }}
                                activeDot={{ r: 8 }}
                                name="COGS %"
                            />
                        </ComposedChart>
                    </ResponsiveContainer>

                    <Grid container spacing={2} sx={{ mt: 2 }}>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Average COGS % Profit Margin"
                                value={`${cogsData.averageCogs.toFixed(2)}%`}
                                bgColor="#f0f4ff"
                                textColor="error"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Total Revenue"
                                value={`$${cogsData.totalRevenue.toLocaleString()}`}
                                bgColor="#e3f2fd"
                                textColor="primary"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Total COGS"
                                value={`$${cogsData.totalCogs.toLocaleString()}`}
                                bgColor="#fff8e1"
                                textColor="warning.main"
                            />
                        </Grid>
                    </Grid>
                </Paper>

                <GoalStatusDisplay
                    currentValue={cogsData.averageCogs}
                    goalConfig={cogsConfig}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculateCOGSTrend()}
                    size="medium"
                    title="COGS % Performance"
                />
            </Box>
        </ChartExportWrapper>
    );
};

export default COGSProfitMargin;