import React, { useState, useEffect } from 'react';
import { Box, Typography, Grid, Paper, CircularProgress, Alert, LinearProgress } from '@mui/material';
import {
    ComposedChart, Line, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import { useFormatAxisTick } from '../../hooks/useFormatAxisTick';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import ChartExportWrapper from './ChartExportWrapper';

const PerfectOrderRate = () => {
    const [perfectOrderData, setPerfectOrderData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const formatAxisTick = useFormatAxisTick();

    const formatNumber = (value) => {
        return new Intl.NumberFormat('en-US').format(value);
    };

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const functions = getFunctions();
            const getPerfectOrderRate = httpsCallable(functions, 'getPerfectOrderRate');
            const getKPIGoalsForReport = httpsCallable(functions, 'getKPIGoalsForReport');

            const [perfectOrderResult, goalsResult] = await Promise.all([
                getPerfectOrderRate(),
                getKPIGoalsForReport({ reportName: 'perfect-order' })
            ]);

            setPerfectOrderData(perfectOrderResult.data);

            if (goalsResult.data.success) {
                setKpiGoals(goalsResult.data.data);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            setError('Failed to fetch data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    const calculatePerfectOrderTrend = () => {
        if (!perfectOrderData?.monthlyData || perfectOrderData.monthlyData.length < 2) return 0;
        const lastTwo = perfectOrderData.monthlyData.slice(-2);
        return parseFloat(lastTwo[1].perfectOrderRate) - parseFloat(lastTwo[0].perfectOrderRate);
    };

    const calculateDomains = () => {
        if (!perfectOrderData?.monthlyData) return { rateDomain: [0, 100], ordersDomain: [0, 1000] };

        const orderValues = perfectOrderData.monthlyData.map(item =>
            Math.max(item.totalOrders || 0, item.perfectOrders || 0, item.nonPerfectOrders || 0)
        );

        const goalValue = kpiGoals?.['Perfect Order Rate']?.value;
        const maxRate = Math.max(...perfectOrderData.monthlyData.map(item => parseFloat(item.perfectOrderRate)), goalValue ? parseFloat(goalValue) : 0);

        return {
            rateDomain: [95, Math.min(Math.ceil(maxRate * 1.1), 100)],
            ordersDomain: [0, Math.ceil(Math.max(...orderValues) * 1.1)]
        };
    };

    const formatPerfectOrderRate = (rate) => {
        const numRate = parseFloat(rate);
        if (numRate < 100) {
            return Math.min(numRate, 99.99).toFixed(2);
        }
        return numRate === 100 ? "100.00" : "99.99";
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                    <Typography variant="body2">{`Month: ${label}`}</Typography>
                    <Typography variant="body2" color="#2196f3">
                        {`Perfect Order Rate: ${formatPerfectOrderRate(data.perfectOrderRate)}%`}
                    </Typography>
                    <Typography variant="body2" color="#4caf50">
                        {`Perfect Orders: ${formatNumber(data.perfectOrders)}`}
                    </Typography>
                    <Typography variant="body2" color="#f44336">
                        {`Non-Perfect Orders: ${formatNumber(data.nonPerfectOrders)}`}
                    </Typography>
                    <Typography variant="body2" color="#9e9e9e">
                        {`Total Orders: ${formatNumber(data.totalOrders)}`}
                    </Typography>
                </Paper>
            );
        }
        return null;
    };

    const CustomYAxisTick = (props) => {
        const { x, y, payload } = props;
        return (
            <g transform={`translate(${x},${y})`}>
                <text x={0} y={0} dy={16} textAnchor="end" fill="#666">
                    {formatNumber(payload.value)}
                </text>
            </g>
        );
    };

    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: kpiGoals?.['Perfect Order Rate'],
        yAxisId: "left",
        styles: {
            stroke: "#2196f3",
            strokeDasharray: "3 3",
            label: {
                fill: "#2196f3",
                fontSize: 12,
                position: "right"
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Min",
            max: "Max"
        }
    });

    if (isLoading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Alert severity="error" sx={{ mt: 2 }}>
                {error}
            </Alert>
        );
    }

    if (!perfectOrderData || !kpiGoals) return null;

    const { rateDomain, ordersDomain } = calculateDomains();
    const perfectOrderConfig = kpiGoals['Perfect Order Rate'];

    const maxIssueCount = Math.max(...perfectOrderData.qualityIssues.types.map(issue => issue.count));

    return (
        <ChartExportWrapper title="Perfect_Order_Rate">
            <Box sx={{ p: { xs: 2, sm: 3 }, mb: 3 }}>
                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <ResponsiveContainer width="100%" height={400}>
                        <ComposedChart
                            data={perfectOrderData.monthlyData}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="monthYear" />
                            <YAxis
                                yAxisId="left"
                                domain={rateDomain}
                                label={{
                                    value: 'Perfect Order Rate (%)',
                                    angle: -90,
                                    position: 'insideLeft'
                                }}
                            />
                            <YAxis
                                yAxisId="right"
                                orientation="right"
                                domain={ordersDomain}
                                label={{
                                    value: 'Number of Orders',
                                    angle: 90,
                                    position: 'insideRight'
                                }}
                                tickFormatter={formatNumber}
                                tick={<CustomYAxisTick />}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            {goalReferenceLines}
                            <Bar yAxisId="right" dataKey="totalOrders" fill="#9e9e9e" name="Total Orders" />
                            <Bar yAxisId="right" dataKey="perfectOrders" fill="#4caf50" name="Perfect Orders" />
                            <Bar yAxisId="right" dataKey="nonPerfectOrders" fill="#f44336" name="Non-Perfect Orders" />
                            <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="perfectOrderRate"
                                stroke="#2196f3"
                                strokeWidth={2}
                                dot={{ r: 4 }}
                                name="Perfect Order Rate (%)"
                            />
                        </ComposedChart>
                    </ResponsiveContainer>

                    <Grid container spacing={2} sx={{ mt: 2 }}>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Overall Perfect Order Rate"
                                value={`${formatPerfectOrderRate(perfectOrderData.overallPerfectOrderRate)}%`}
                                bgColor="#e3f2fd"
                                textColor="#2196f3"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Perfect Orders"
                                value={formatNumber(perfectOrderData.summary.perfectOrders)}
                                bgColor="#e8f5e9"
                                textColor="#4caf50"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Non-Perfect Orders"
                                value={formatNumber(perfectOrderData.summary.nonPerfectOrders)}
                                bgColor="#ffebee"
                                textColor="#f44336"
                            />
                        </Grid>
                    </Grid>
                </Paper>

                <GoalStatusDisplay
                    currentValue={parseFloat(formatPerfectOrderRate(perfectOrderData.overallPerfectOrderRate))}
                    goalConfig={perfectOrderConfig}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculatePerfectOrderTrend()}
                    title="Perfect Order Performance"
                    size="medium"
                />

                {perfectOrderData.qualityIssues?.types?.length > 0 && (
                    <Paper elevation={3} sx={{ p: 3, mt: 3 }}>
                        <Typography variant="h6" gutterBottom>
                            Top Quality Issues
                        </Typography>
                        {perfectOrderData.qualityIssues.types.slice(0, 5).map((issue, index) => (
                            <Box key={issue.type} sx={{ mb: 3 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                                        {`${index + 1}. ${issue.type}`}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        {`${issue.count} ${issue.count === 1 ? 'occurrence' : 'occurrences'}`}
                                    </Typography>
                                </Box>
                                <LinearProgress
                                    variant="determinate"
                                    value={(issue.count / maxIssueCount) * 100}
                                    sx={{
                                        height: 8,
                                        borderRadius: 4,
                                        backgroundColor: '#f5f5f5',
                                        '& .MuiLinearProgress-bar': {
                                            backgroundColor: index === 0 ? '#f44336' :
                                                index === 1 ? '#ff9800' :
                                                    index === 2 ? '#ffc107' :
                                                        index === 3 ? '#4caf50' : '#2196f3'
                                        }
                                    }}
                                />
                            </Box>
                        ))}
                    </Paper>
                )}
            </Box>
        </ChartExportWrapper>
    );
};

export default PerfectOrderRate;