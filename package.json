{"name": "netsuite-inventory-report", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@mui/icons-material": "^6.1.8", "@mui/material": "^6.1.8", "@silevis/reactgrid": "^4.1.17", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "ag-charts-enterprise": "^12.1.1", "ag-charts-react": "^12.1.1", "ag-grid-community": "^34.1.1", "ag-grid-enterprise": "^34.1.1", "ag-grid-react": "^34.1.1", "antd": "^5.21.5", "axios": "^1.7.2", "chartjs-adapter-date-fns": "^3.0.0", "dayjs": "^1.11.13", "dexie": "^4.0.11", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "firebase": "^10.6.0", "firebase-functions": "^6.3.2", "gapi-script": "^1.2.0", "html2canvas": "^1.4.1", "idb-keyval": "^6.2.2", "jquery": "^3.7.1", "lz-string": "^1.5.0", "moment": "^2.30.1", "papaparse": "^5.5.2", "pdf-lib": "^1.17.1", "popper.js": "^1.16.1", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-data-table-component": "^7.6.2", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-firebase-hooks": "^5.1.1", "react-multi-select-component": "^4.3.4", "react-router-dom": "^6.18.0", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "react-spreadsheet": "^0.9.5", "recharts": "^2.13.3", "scheduler": "^0.23.2", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint-config-google": "^0.14.0"}}