/* eslint-disable guard-for-in */
import React, { useState, useEffect, useRef, useCallback, useMemo, forwardRef, useImperativeHandle } from 'react';
import { Spin, Tabs, Button, Select, Form, Modal, Input, Upload, message, Table, Dropdown, Menu, Tooltip, Checkbox, Row, Col, Popconfirm, Switch, Typography, DatePicker, Space } from 'antd';
const { Text } = Typography;
import { AgGridReact } from 'ag-grid-react';
import { themeBalham } from 'ag-grid-community';
import Papa from 'papaparse';
import * as XLSX from 'xlsx';
import { useSearchParams } from 'react-router-dom';
import { api, db as firestoreDb } from '../../pages/firebase';
import { getDocs, collection, addDoc, doc, setDoc, deleteDoc, onSnapshot, query, where, or } from 'firebase/firestore';
import { useUser } from '../../contexts/UserContext';
import { ReloadOutlined, CameraOutlined, MenuOutlined, CloseCircleOutlined, DeleteOutlined, SaveOutlined, UploadOutlined, DownloadOutlined, LockOutlined, UnlockOutlined, HistoryOutlined, EditOutlined } from '@ant-design/icons';
import { FileTextOutlined } from '@ant-design/icons';
import HistoricalSales from './HistoricalSales';
import UploadModal from './UploadModal';
import NotesModal from './NotesModal';
import HistoricalSalesModal from './HistoricalSalesModal';
import SavedViews from '../SavedViews';
import { FORECAST_METHODS, averageSoldQuery, averageBookedQuery, buildForecastNode, getValidDaysForMonth, lifeStatusColors, DEMAND_PLAN_URL } from '../../constants';
import ImageCell from '../ImageCell';
import dayjs from 'dayjs';
import '../../App.css';


// Static month names for mapping
const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
// Precompute next 12 month labels (e.g. 'Jul 2025') once on module load
const next12MonthsGlobal = (() => {
  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = parseInt(now.getFullYear().toString().slice(-2));
  const names = Array.from({ length: 12 }, (_, i) => {
    const idx = (currentMonth + i) % 12;
    const yr = (currentYear + Math.floor((currentMonth + i) / 12)).toString().slice(-2);
    return `${monthNames[idx]} ${yr}`;
  });
  // console.log('monthNames', names);
  return names;
})();

const isDateInRange = (dateStr, dateRange) => {
  if (!dateRange || !dateRange.start || !dateRange.end) return false;

  // Convert date strings to Date objects and extract just the date part
  const startDate = new Date(dateRange.start);
  const endDate = new Date(dateRange.end);
  const checkDate = new Date(dateStr);

  // Reset time to midnight UTC for accurate date comparison (avoid timezone issues)
  const startDateOnly = new Date(Date.UTC(startDate.getUTCFullYear(), startDate.getUTCMonth(), startDate.getUTCDate()));
  const endDateOnly = new Date(Date.UTC(endDate.getUTCFullYear(), endDate.getUTCMonth(), endDate.getUTCDate()));
  const checkDateOnly = new Date(Date.UTC(checkDate.getUTCFullYear(), checkDate.getUTCMonth(), checkDate.getUTCDate()));

  return checkDateOnly >= startDateOnly && checkDateOnly <= endDateOnly;
};

// comp methods
// saved views
// notes
// historical sales
// bulk edit
// upload
// proxy items
// formatting for comp values 
// real time updates

const DemandPlan = () => {
  const { userData } = useUser();
  const gridRef = useRef(null);

  //* State
  const [demandLoading, setDemandLoading] = useState(false);
  const [rowData, setRowData] = useState([]);
  const [compData, setCompData] = useState({});
  const [compMethod, setCompMethod] = useState('original');
  const [showDollars, setShowDollars] = useState(false);

  //* Notes States
  const [notes, setNotes] = useState([]);
  const [showNotesModal, setShowNotesModal] = useState(false);
  const [notesLastUpdated, setNotesLastUpdated] = useState(null);

  //* Historical Sales States
  const [showHistoricalSalesModal, setShowHistoricalSalesModal] = useState(false);

  //* Bulk Edit States
  const [showBulkMethodModal, setShowBulkMethodModal] = useState(false);
  const [bulkMethodValue, setBulkMethodValue] = useState(null);
  const [showBulkProxyItemModal, setShowBulkProxyItemModal] = useState(false);
  const [bulkProxyItemValue, setBulkProxyItemValue] = useState(null);
  const [proxyItemOptions, setProxyItemOptions] = useState([]);

  //* Saved View States (now fully managed by SavedViews component)

  //* Upload States
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [selectedCount, setSelectedCount] = useState(0);
  const [selectedCellInfo, setSelectedCellInfo] = useState(null);

  //* Firestore loading state
  const [demandPlanLoading, setDemandPlanLoading] = useState(false);

  //* Available months extracted from data
  const [availableMonths, setAvailableMonths] = useState([]);

  //* Global forecast data - generated once on page load
  const [globalMonths, setGlobalMonths] = useState({});
  const [globalDays, setGlobalDays] = useState({});
  const [globalMonthDays, setGlobalMonthDays] = useState({});

  //* Refresh state
  const [lastUpdated, setLastUpdated] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const getDaysInMonth = (monthStr) => {
    // monthStr: "YYYY-MM"
    const today = dayjs().startOf("day");
    const d = dayjs(monthStr + "-01");
    const numDays = d.daysInMonth();
    const days = [];
    for (let i = 1; i <= numDays; i++) {
      const day = d.date(i);
      // Compare using valueOf (timestamp) to avoid plugin issues
      if (day.valueOf() >= today.valueOf()) {
        days.push(day.format("YYYY-MM-DD"));
      }
    }
    return days;
  };
  // Initialize global forecast data once on page load
  const initializeForecastData = () => {
    const today = dayjs().startOf("day");
    const now = dayjs();
    const months = {};
    const days = {};
    const monthDayTemp = {};

    for (let i = 0; i < 24; i++) {
      const monthDate = now.add(i, "month");
      const monthStr = monthDate.format("YYYY-MM");
      const monthLabel = monthDate.format("MMM YY");

      months[monthStr] = {
        monthId: monthStr,
        label: monthLabel,
        total: 0,
        numDays: 0,
        validDays: 0,
      };

      const daysInMonth = getDaysInMonth(monthStr);
      months[monthStr].numDays = daysInMonth.length;
      monthDayTemp[monthStr] = daysInMonth;

      for (const day of daysInMonth) {
        days[day] = {
          total: 0,
        };
      }
    }

    // Set all global state variables
    setGlobalMonths(months);
    setGlobalDays(days);
    setGlobalMonthDays(monthDayTemp);

    console.log('Initialized global forecast data:', { months, days, monthDayTemp });
  };

  const enrichDemandPlanData = async (demandPlanData) => {
    const upcList = Array.from(new Set(demandPlanData.map(row => row.upc)));
    const nodeList = Array.from(new Set(demandPlanData.map(row => row.forecastNode)));
    const bqProms = [];
    const itemQuery = `SELECT upc, producttype, color, size, launchdate, enddate, productdivision, productcategory, productform, productspecification, lifestatus FROM \`hj-reporting.items.items_netsuite\` WHERE upc IN (${upcList.map(upc => `'${upc.replace(/'/g, "\\'")}'`).join(',')})`;
    bqProms.push(api.bigQueryRunQueryOnCall({ options: { query: itemQuery } }));
    let whereClause = '';
    if (nodeList.length > 0) {
      whereClause = `WHERE code IN (${nodeList.map(node => `'${node.replace(/'/g, "\\'")}'`).join(',')})`;
    }
    const forecastNodeQuery = `SELECT code, color FROM \`hj-reporting.forecast.forecast_nodes\` ${whereClause}`;
    console.log('forecastNodeQuery', forecastNodeQuery);
    bqProms.push(api.bigQueryRunQueryOnCall({ options: { query: forecastNodeQuery } }));
    const [{ data: items }, { data: forecastNodes }] = await Promise.all(bqProms);
    console.log('items', items.length, items[0]);
    console.log('forecastNodes', forecastNodes.length, forecastNodes[0]);
    const itemMap = new Map(items.map(item => [item.upc, item]));
    const forecastNodeMap = new Map(forecastNodes.map(node => [node.code, node]));

    // Use global state (guaranteed to be initialized)
    const months = globalMonths;
    const days = globalDays;

    const data = demandPlanData.map(demandDoc => {
      const item = itemMap.get(demandDoc.upc);
      const forecastNode = forecastNodeMap.get(demandDoc.forecastNode);
      if (!demandDoc.months) {
        demandDoc.months = {};
      }
      if (!demandDoc.days) {
        demandDoc.days = {};
      }

      // Initialize months structure using available months data
      Object.keys(months).forEach(m => {
        if (!demandDoc.months[m]) {
          demandDoc.months[m] = {};
        }
        // Handle both new flattened structure and old nested structure for backward compatibility
        demandDoc.months[m] = {
          ...months[m],
          ...demandDoc.months?.[m],
          total: demandDoc.months?.[m]?.total || 0, // Ensure total is always present
          isValid: isValidDateRange(demandDoc, m, true),
          numDays: months[m]?.numDays || 0,
          validDays: getValidDaysCount(demandDoc, m)
        };
      });

      Object.keys(days).forEach(d => {
        // Handle both new flattened structure and old nested structure for backward compatibility
        // Calculate if day is valid for item using isValidDateRange (assumes function exists)
        demandDoc.days[d] = {
          ...days[d],
          ...demandDoc.days?.[d],
          total: demandDoc.days?.[d]?.total || 0, // Ensure total is always present
          isValid: isValidDateRange(demandDoc, d, false)
        };
      });

      // Calculate validDays for each month after days are initialized
      return {
        ...item,
        ...demandDoc,
        id: `${demandDoc.upc}_${demandDoc.forecastNode}`, // Ensure ID matches document ID format
        nodeColor: forecastNode?.color,
      };
    });
    return data;
  };
  // Fetch demand plan data directly from Firestore
  const fetchDemandPlanData = async () => {
    setDemandPlanLoading(true);
    try {
      // Get the demand plan data from Firestore using getDocs
      const queryList = [where("active", "==", true)];
      const demandPlanRef = query(collection(firestoreDb, 'demandPlan'), queryList);
      const querySnapshot = await getDocs(demandPlanRef);

      const demandPlanData = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      console.log('getDocs - demandPlanData', demandPlanData.length, demandPlanData[0]);

      const enrichedData = await enrichDemandPlanData(demandPlanData);
      console.log('getDocs - enrichedData', enrichedData.length, enrichedData[0]);

      setRowData(enrichedData);

      // Set available months for column generation
      if (enrichedData.length > 0) {
        const monthObj = enrichedData[0].months;
        const monthIds = Object.keys(monthObj);
        // Sort months in chronological order
        const sortedMonthIds = monthIds.sort((a, b) => {
          const dateA = dayjs(a + '-01');
          const dateB = dayjs(b + '-01');
          return dateA.diff(dateB);
        });
        const monthCols = sortedMonthIds.map(m => ({ monthId: m, label: monthObj[m].label }));
        setAvailableMonths(monthCols);
      }

      // Update last updated timestamp
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching demand plan data:', error);
      message.error('Failed to load demand plan data');
    } finally {
      setDemandPlanLoading(false);
    }
  };

  // Manual refresh function
  const refreshData = async () => {
    setIsRefreshing(true);
    try {
      await fetchDemandPlanData();
      message.success('Data refreshed successfully');
    } catch (error) {
      message.error('Failed to refresh data');
    } finally {
      setIsRefreshing(false);
    }
  };


  // Helper function to calculate month total from individual days
  const calculateMonthTotal = useCallback((rowData, month) => {
    if (!rowData.days) return 0;

    // Get all days in the month
    const daysInMonth = Object.keys(rowData.days).filter(day =>
      day.startsWith(month)
    );

    return daysInMonth.reduce((total, day) => {
      return total + (Number(rowData.days[day]?.total) || 0);
    }, 0);
  }, []);

  // Helper function to get valid days for redistribution (not past, within launch/end dates)
  const getValidDaysForRedistribution = useCallback((rowData, month) => {
    const today = dayjs().startOf("day");
    const launchDate = rowData.launchdate ? dayjs(rowData.launchdate) : null;
    const endDate = rowData.enddate ? dayjs(rowData.enddate) : null;

    return Object.keys(rowData.days).filter(day => {
      const dayDate = dayjs(day);

      // Must be in the specified month
      if (!day.startsWith(month)) return false;

      // Must not be in the past
      if (dayDate.isBefore(today)) return false;

      // Must be on or after launch date
      if (launchDate && dayDate.isBefore(launchDate)) return false;

      // Must be before or on end date
      if (endDate && dayDate.isAfter(endDate)) return false;

      return true;
    });
  }, []);

  // Helper function to count valid days for a month
  const getValidDaysCount = (data, month) => {
    const today = dayjs().startOf("day");
    const launchDate = data.launchdate ? dayjs(data.launchdate?.value) : null;
    const endDate = data.enddate ? dayjs(data.enddate?.value) : null;

    // Get all days in the month from global state
    const daysInMonth = globalMonthDays[month] || [];

    // Count valid days using the existing days array
    return daysInMonth.filter(day => {
      const dayDate = dayjs(day);

      // Must not be in the past
      if (dayDate.isBefore(today)) return false;

      // Must be on or after launch date
      if (launchDate && dayDate.isBefore(launchDate)) return false;

      // Must be before or on end date
      if (endDate && dayDate.isAfter(endDate)) return false;

      return true;
    }).length;
  };

  // Helper function to check if a date range is valid for an item
  const isValidDateRange = (item, dateStr, isMonth = false) => {
    const today = dayjs().startOf("day");
    const launchDate = item.launchdate ? dayjs(item.launchdate?.value) : null;
    const endDate = item.enddate ? dayjs(item.enddate?.value) : null;
    if (isMonth) {
      // For months, check if ANY day in the month is valid
      const daysInMonth = globalMonthDays[dateStr] || [];
      return daysInMonth.some(day => {
        const checkDate = dayjs(day);

        // Must not be in the past
        if (checkDate.isBefore(today)) return false;

        // Must be on or after launch date
        if (launchDate && checkDate.isBefore(launchDate)) return false;

        // Must be before or on end date
        if (endDate && checkDate.isAfter(endDate)) return false;

        return true;
      });
    } else {
      // For days, check the specific date
      const checkDate = dayjs(dateStr);

      // Must not be in the past
      if (checkDate.isBefore(today)) return false;

      // Must be on or after launch date
      if (launchDate && checkDate.isBefore(launchDate)) return false;

      // Must be before or on end date
      if (endDate && checkDate.isAfter(endDate)) return false;

      return true;
    }
  };

  // Helper function to distribute month total across valid days
  const distributeMonthTotal = useCallback((total, rowData, month) => {
    const validDays = getValidDaysForRedistribution(rowData, month);

    if (validDays.length === 0) return {};

    const baseValue = Math.floor(total / validDays.length);
    const remainder = total % validDays.length;

    const distribution = {};
    validDays.forEach((day, index) => {
      distribution[day] = { total: baseValue + (index < remainder ? 1 : 0) };
    });

    return distribution;
  }, [getValidDaysForRedistribution]);

  // Handle cell value changes - simplified for Firestore operations only
  const onCellValueChanged = async (params) => {
    if (!params.data) return;

    const { colDef, data, newValue, oldValue } = params;
    const field = colDef.field;

    if (newValue === oldValue) return;

    try {
      const forecastNode = data.forecastNode;
      const documentId = `${data.upc}_${forecastNode}`;

      // Save to Firestore (data is already updated by valueSetter)
      setDoc(doc(firestoreDb, 'demandPlan', documentId), data, { merge: true }).then(() => {
        console.log('Cell value changed and saved to Firestore:', { field, oldValue, newValue, documentId });
      }).catch((error) => {
        console.error('Error saving to Firestore:', error);
        message.error('Failed to save changes ' + error.message);
      });
    } catch (error) {
      console.error('Error saving to Firestore:', error);
      message.error('Failed to save changes');
    }
  };

  const fetchNotes = async () => {
    const notesRef = collection(firestoreDb, 'demandPlanNotes');
    getDocs(notesRef).then((snap) => {
      const notesData = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      setNotes(notesData);
      setNotesLastUpdated(new Date());

      // Refresh grid cells to show note indicators
      if (gridRef.current && gridRef.current.api) {
        gridRef.current.api.refreshCells({ force: true });
      }
    }).catch((error) => {
      console.error('Error fetching notes:', error);
      message.error('Failed to fetch notes');
    });
  };


  //* useEffects
  useEffect(() => {
    initializeForecastData();
  }, []);

  useEffect(() => {
    if (Object.keys(globalMonths).length === 0) return;
    fetchDemandPlanData();
    fetchNotes();
  }, [globalMonths]);


  useEffect(() => {
    if (gridRef.current && gridRef.current.api) {
      const api = gridRef.current.api;
      const columnState = api.getColumnState();

      api.refreshCells({ force: true });

      if (columnState) {
        api.applyColumnState({ state: columnState });
      }
      api.refreshHeader();
    }
  }, [showDollars, compMethod, notes, proxyItemOptions]);

  // Handle comparison method changes - fetch comparison data
  // useEffect(() => {
  //   const fetchCompData = async () => {
  //     if (!compMethod || rowData.length === 0) return;

  //     try {
  //       console.log('Fetching comparison data for method:', compMethod);

  //       // Extract unique UPCs and forecast nodes from current data
  //       const upcs = [...new Set(rowData.map(row => row.upc))];
  //       const forecastNodes = [...new Set(rowData.map(row => row.forecast_node))];

  //       if (upcs.length === 0 || forecastNodes.length === 0) return;

  //       // Fetch comparison data
  //       const compData = await fetchComparisonData(compMethod, upcs, forecastNodes);
  //       setComparisonData(compData);

  //       // Update rowData with comparison values
  //       const updatedRowData = rowData.map(row => {
  //         const compKey = `${row.upc}_${row.forecast_node}`;
  //         const compValues = compData[compKey] || {};

  //         const updatedRow = { ...row };

  //         // Update month totals
  //         const next12Months = getNext12Months();
  //         next12Months.forEach(({ label, key }) => {
  //           if (updatedRow[label]) {
  //             updatedRow[label] = {
  //               ...updatedRow[label],
  //               [compMethod]: compValues[key] || 0
  //             };
  //           }
  //         });

  //         // Update daily values if loaded
  //         getDaysInMonth(label).forEach(dayLabel => {
  //           if (updatedRow[dayLabel] && updatedRow[dayLabel].loaded) {
  //             updatedRow[dayLabel] = {
  //               ...updatedRow[dayLabel],
  //               [compMethod]: compValues[dayLabel] || 0
  //             };
  //           }
  //         });

  //         return updatedRow;
  //       });
  //       setRowData(updatedRowData);
  //     } catch (error) {
  //       console.error('Error fetching comparison data:', error);
  //       // Continue without comparison data
  //     }
  //   };

  //   // keep ref in sync and refresh grid so renderers read the latest compMethod
  //   compMethodRef.current = compMethod;

  //   // Fetch comparison data for new method
  //   fetchCompData();

  //   if (gridRef.current && gridRef.current.api) {
  //     const api = gridRef.current.api;
  //     api.refreshCells({ force: true });
  //   }
  // }, [compMethod, fetchComparisonData, getNext12Months, getDaysInMonth]);

  // Refresh cells when notes are updated
  useEffect(() => {
    if (gridRef.current && gridRef.current.api && notesLastUpdated) {
      gridRef.current.api.refreshCells({ force: true });
    }
  }, [notesLastUpdated, notes]);

  // Helper function to calculate month total from comparison data
  const getComparisonMonthTotal = (row, monthLabel) => {
    if (!row || !row[monthLabel]) return 0;
    let proxyItem = null;
    if (row.proxy_item) {
      // Find the proxy item in the current rowData state
      proxyItem = rowData.find(item => item.upc === row.proxy_item);
    }
    const effectiveMethod = compMethodRef.current;
    switch (effectiveMethod) {
      case 'original':
        if (row.proxy_item && proxyItem) return proxyItem[monthLabel]?.original || 0;
        return row[monthLabel]?.original || 0;
      case 'external':
        if (row.proxy_item && proxyItem) return proxyItem[monthLabel]?.external || 0;
        return row[monthLabel]?.external || 0;
      case 'seasonalTas7':
        if (row.proxy_item && proxyItem) return proxyItem[monthLabel]?.seasonal_tas7 || 0;
        return row[monthLabel]?.seasonal_tas7 || 0;
      case 'seasonalTas30':
        if (row.proxy_item && proxyItem) return proxyItem[monthLabel]?.seasonal_tas30 || 0;
        return row[monthLabel]?.seasonal_tas30 || 0;
      case 'seasonalTas90':
        if (row.proxy_item && proxyItem) return proxyItem[monthLabel]?.seasonal_tas90 || 0;
        return row[monthLabel]?.seasonal_tas90 || 0;
      default:
        return 0;
    }
  };

  // Helper function to calculate percentage difference
  const getDifference = (currentValue, comparisonValue) => {
    if (!comparisonValue || comparisonValue === 0) comparisonValue = .00001;
    const diff = Math.round(currentValue - comparisonValue);
    const percentDiff = diff / comparisonValue;
    return { diff, percentDiff };
  };


  const onClearFilters = () => {
    if (gridRef.current && gridRef.current.api) {
      gridRef.current.api.setFilterModel(null);
    }
  };

  // const handleProcessUpload = () => {
  //   if (!uploadedFile) {
  //     message.error("No file selected.");
  //     return;
  //   }

  //   const reader = new FileReader();
  //   reader.onload = async (e) => {
  //     const fileContent = e.target.result;

  //     const process = async (data) => {
  //       if (!data || data.length === 0) {
  //         message.error("No data found in file or file is empty.");
  //         return;
  //       }

  //       const fileHeaders = Object.keys(data[0]).map(h => h.trim());
  //       const hasDate = fileHeaders.includes('date');
  //       const hasMonth = fileHeaders.includes('month');
  //       const hasRequiredBaseHeaders = ['upc', 'forecast_node', 'qty'].every(h => fileHeaders.includes(h));

  //       if (!hasRequiredBaseHeaders || (!hasDate && !hasMonth)) {
  //         message.error(<div>File is missing required headers. Required: <code>upc, forecast_node, qty</code> and either <code>date</code> or <code>month</code>.</div>, 5);
  //         return;
  //       }

  //       if (hasDate && hasMonth) {
  //         message.error("File cannot contain both 'date' and 'month' columns. Please use separate files for daily and monthly uploads.", 5);
  //         return;
  //       }

  //       const validDateSet = new Set();
  //       next12MonthsGlobal.forEach(monthLabel => {
  //         getDaysInMonth(monthLabel).forEach(day => validDateSet.add(day));
  //       });

  //       const currentDataMap = new Map(rowData.map(r => [r.pivotKey, r]));
  //       const updates = {};
  //       let errors = [];

  //       data.forEach((row, index) => {
  //         const { upc, forecast_node, qty } = row;
  //         if (!upc || !forecast_node || qty === undefined || qty === null) {
  //           const missingFields = [];
  //           if (!upc) missingFields.push('upc');
  //           if (!forecast_node) missingFields.push('forecast_node');
  //           if (qty === undefined || qty === null) missingFields.push('qty');
  //           errors.push(`Row ${index + 2}: Missing required value(s): ${missingFields.join(', ')}.`);
  //           return;
  //         }
  //         if (upc.includes('+')) {
  //           errors.push(`Row ${index + 2}: UPC '${upc}' contains a '+'. Please remove the '+' and try again.`);
  //           return;
  //         }

  //         const pivotKey = `${upc}_${forecast_node}`;
  //         if (!currentDataMap.has(pivotKey)) {
  //           errors.push(`Row ${index + 2}: Item with UPC '${upc}' and Node '${forecast_node}' not found in the plan.`);
  //           return;
  //         }
  //         const existingRow = currentDataMap.get(pivotKey);
  //         if (!updates[pivotKey]) updates[pivotKey] = {};


  //         if (hasDate) {
  //           let { date } = row;
  //           if (!date) {
  //             errors.push(`Row ${index + 2}: Missing 'date' value.`);
  //             return;
  //           }

  //           if (!/^\d{4}-\d{2}-\d{2}$/.test(date) && !/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(date)) {
  //             errors.push(`Row ${index + 2}: Invalid date format for '${date}'. Expected YYYY-MM-DD or MM/DD/YYYY.`);
  //             return;
  //           }

  //           if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(date)) {
  //             const [month, day, year] = date.split('/');
  //             date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  //           }

  //           if (!validDateSet.has(date)) {
  //             errors.push(`Row ${index + 2}: Date '${date}' is outside the visible 12-month forecast window.`);
  //             return;
  //           }
  //           updates[pivotKey][date] = Number(qty);
  //         } else { // hasMonth
  //           const { month } = row;
  //           if (!month) {
  //             errors.push(`Row ${index + 2}: Missing 'month' value.`);
  //             return;
  //           }

  //           const monthLabel = month.trim();
  //           if (!next12MonthsGlobal.includes(monthLabel)) {
  //             errors.push(`Row ${index + 2}: Month '${monthLabel}' is not a valid or visible forecast month (e.g., 'Jul 24').`);
  //             return;
  //           }

  //           const launchDate = existingRow.launchdate ? new Date(existingRow.launchdate + 'T00:00:00Z') : null;
  //           const endDate = existingRow.enddate ? new Date(existingRow.enddate + 'T00:00:00Z') : null;

  //           const allMonthDays = getDaysInMonth(monthLabel);
  //           const validDays = allMonthDays.filter(dayField => {
  //             const currentDate = new Date(dayField + 'T00:00:00Z');
  //             if (launchDate && currentDate < launchDate) return false;
  //             if (endDate && currentDate > endDate) return false;
  //             return true;
  //           });

  //           if (validDays.length > 0) {
  //             const valueToDistribute = Number(qty) || 0;
  //             const baseValue = Math.floor(valueToDistribute / validDays.length);
  //             const remainder = valueToDistribute % validDays.length;
  //             validDays.forEach((dayField, i) => {
  //               updates[pivotKey][dayField] = baseValue + (i < remainder ? 1 : 0);
  //             });
  //           }
  //         }
  //       });

  //       if (errors.length > 0) {
  //         message.error(
  //           <div>
  //             <div>Upload Failed for some rows:</div>
  //             <ul style={{ margin: 0, paddingLeft: 18 }}>
  //               {errors.slice(0, 10).map((err, i) => (
  //                 <li key={i} style={{ textAlign: 'left' }}>{err}</li>
  //               ))}
  //             </ul>
  //           </div>,
  //           10
  //         );
  //         // return;
  //       }

  //       const newRowData = rowData.map(gridRow => {
  //         if (updates[gridRow.pivotKey]) {
  //           const updatedRow = { ...gridRow };
  //           const rowUpdates = updates[gridRow.pivotKey];

  //           // Apply updates to the current values in the new data structure
  //           Object.keys(rowUpdates).forEach(dateField => {
  //             if (updatedRow[dateField] && typeof updatedRow[dateField] === 'object') {
  //               updatedRow[dateField].current = rowUpdates[dateField];
  //             }
  //           });

  //           return updatedRow;
  //         }
  //         return gridRow;
  //       });

  //       // Save changes directly to Firestore
  //       try {
  //         for (const pivotKey in updates) {
  //           if (updates.hasOwnProperty(pivotKey)) {
  //             const dailyUpdates = updates[pivotKey];
  //             await api.updateDemandPlanValuesOnCall({
  //               upcForecastNode: pivotKey,
  //               dailyUpdates
  //             });
  //           }
  //         }
  //         message.success('Upload data saved successfully!');
  //       } catch (error) {
  //         console.error('Error saving upload data:', error);
  //         message.error('Failed to save upload data: ' + error.message);
  //       }

  //       setRowData(newRowData);
  //       gridRef.current.api.refreshCells({ force: true });

  //       message.success('Data updated and saved from file.');
  //       setShowUploadModal(false);
  //       setUploadedFile(null);
  //     };

  //     if (uploadedFile.name.endsWith('.csv')) {
  //       Papa.parse(fileContent, {
  //         header: true,
  //         skipEmptyLines: true,
  //         transformHeader: header => header.trim(),
  //         complete: async (results) => {
  //           const processedData = results.data.map(row => {
  //             const trimmedRow = {};
  //             for (const key in row) {
  //               if (Object.prototype.hasOwnProperty.call(row, key)) {
  //                 trimmedRow[key] = typeof row[key] === 'string' ? row[key].trim() : row[key];
  //               }
  //             }
  //             return trimmedRow;
  //           });
  //           await process(processedData);
  //         },
  //         error: (err) => {
  //           message.error("Error parsing CSV: " + err.message);
  //         }
  //       });
  //     } else if (uploadedFile.name.endsWith('.xlsx') || uploadedFile.name.endsWith('.xls')) {
  //       try {
  //         const workbook = XLSX.read(fileContent, { type: 'binary' });
  //         const sheetName = workbook.SheetNames[0];
  //         const worksheet = workbook.Sheets[sheetName];
  //         let jsonData = XLSX.utils.sheet_to_json(worksheet);
  //         jsonData = jsonData.map(row => {
  //           const trimmedRow = {};
  //           for (const key in row) {
  //             if (Object.prototype.hasOwnProperty.call(row, key)) {
  //               const trimmedKey = key.trim();
  //               trimmedRow[trimmedKey] = typeof row[key] === 'string' ? row[key].trim() : row[key];
  //             }
  //           }
  //           return trimmedRow;
  //         });
  //         await process(jsonData);
  //       } catch (e) {
  //         message.error("Error reading Excel file: " + e.message);
  //       }
  //     } else {
  //       message.error('Unsupported file type. Please upload a CSV or Excel file.');
  //     }
  //   };

  //   if (uploadedFile.name.endsWith('.csv')) {
  //     reader.readAsText(uploadedFile);
  //   } else {
  //     reader.readAsBinaryString(uploadedFile);
  //   }
  // };

  const onSelectionChanged = useCallback((event) => {
    setSelectedCount(event.api.getSelectedRows().length);
  }, []);

  // const handleBulkMethodUpdate = async () => {
  //   if (!bulkMethodValue) return;

  //   const selectedRows = gridRef.current.api.getSelectedNodes();
  //   const selectedRowsData = selectedRows.map(node => node.data);

  //   // Create a copy of the current rowData to update
  //   const updatedRowData = [...rowData];

  //   selectedRowsData.forEach(selectedRow => {
  //     // Find the corresponding row in the main rowData array
  //     const rowIndex = updatedRowData.findIndex(row => row.pivotKey === selectedRow.pivotKey);
  //     if (rowIndex === -1) return;

  //     const row = updatedRowData[rowIndex];

  //     // Update all month and day values to use the comparison data from the selected method
  //     next12MonthsGlobal.forEach(monthLabel => {
  //       // Update month totals
  //       if (row[monthLabel]) {
  //         const comparisonValue = getComparisonValue(row, monthLabel, bulkMethodValue);
  //         row[monthLabel].current = comparisonValue;
  //       }

  //       // Update daily values
  //       getDaysInMonth(monthLabel).forEach(dayLabel => {
  //         if (row[dayLabel]) {
  //           const comparisonValue = getComparisonValue(row, dayLabel, bulkMethodValue);
  //           row[dayLabel].current = comparisonValue;
  //         }
  //       });
  //     });
  //   });

  //   // Update the state with the new data
  //   setRowData(updatedRowData);

  //   // Save changes directly to Firestore for each row
  //   try {
  //     for (const selectedRow of selectedRowsData) {
  //       const pivotKey = selectedRow.pivotKey;
  //       const dailyUpdates = {};

  //       // Add all updated dates to save
  //       next12MonthsGlobal.forEach(monthLabel => {
  //         getDaysInMonth(monthLabel).forEach(dayLabel => {
  //           const comparisonValue = getComparisonValue(selectedRow, dayLabel, bulkMethodValue);
  //           dailyUpdates[dayLabel] = comparisonValue;
  //         });
  //       });

  //       // Save to Firestore
  //       await api.updateDemandPlanValuesOnCall({
  //         upcForecastNode: pivotKey,
  //         dailyUpdates
  //       });
  //     }

  //     message.success(`Updated and saved forecast method for ${selectedCount} rows.`);
  //   } catch (error) {
  //     console.error('Error saving bulk changes:', error);
  //     message.error('Failed to save bulk changes: ' + error.message);
  //   }

  //   // Refresh the grid to show the changes
  //   if (gridRef.current?.api) {
  //     gridRef.current.api.refreshCells({ force: true });
  //   }

  //   setShowBulkMethodModal(false);
  //   setBulkMethodValue(null);
  //   message.success(`Updated forecast method for ${selectedCount} rows.`);
  // };

  const handleBulkProxyItemUpdate = () => {
    gridRef.current.api.getSelectedNodes().forEach(node => {
      node.setDataValue('proxy_item', bulkProxyItemValue);
    });
    setShowBulkProxyItemModal(false);
    setBulkProxyItemValue(null);
    if (gridRef.current?.api) {
      gridRef.current.api.deselectAll();
    }
    message.success(`Updated proxy item for ${selectedCount} rows.`);
  };

  const columnDefs = useMemo(() => {
    return [
      // { TODO implement this on each date row, and make it editable
      //   field: "locked",
      //   headerName: "🔒",
      //   enableRowGroup: false,
      //   sortable: false,
      //   filter: false,
      //   width: 30,
      //   cellStyle: {
      //     textAlign: 'center',
      //   },
      //   suppressHeaderMenuButton: true,
      // },
      {
        field: "producttype",
        headerName: 'Product',
        enableRowGroup: true,
        filter: true,
        sortable: true,
        cellRenderer: (params) => {
          if (!params.data) return params.value; // Handle group rows
          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip title={params.data.forecast_node}>
                <div
                  style={{
                    height: '12px',
                    width: '12px',
                    backgroundColor: params.data.nodeColor || 'grey',
                    borderRadius: '50%',
                    marginRight: '8px',
                    border: '1px solid #e0e0e0'
                  }}
                >
                </div>
              </Tooltip>
              <div style={{ color: lifeStatusColors[params.data.lifestatus] || 'black' }}>
                {params.value}
              </div>
            </div>
          );
        },
      },
      {
        field: "color",
        headerName: 'Color',
        enableRowGroup: true,
        sortable: true,
        filter: true,
        cellStyle: params => {
          if (!params.data) return null;
          return { color: lifeStatusColors[params.data.lifestatus] || 'black' };
        },
      },
      {
        field: "size",
        headerName: 'Size',
        enableRowGroup: true,
        sortable: true,
        filter: true,
        cellStyle: params => {
          if (!params.data) return null;
          return { color: lifeStatusColors[params.data.lifestatus] || 'black' };
        },
      },
      {
        field: "upc",
        headerName: 'UPC',
        enableRowGroup: true,
        sortable: true,
        filter: true,
        onCellDoubleClicked: (params) => {
          console.log('clicked on upc', params);
          window.open(`https://6810379.app.netsuite.com/app/common/item/item.nl?id=${params.data.netsuite_id}&whence=`, '_blank');
        },
        cellStyle: {
          cursor: 'pointer',
          textDecoration: 'underline',
          color: 'blue',
        },
      },
      {
        field: "forecastNode",
        headerName: "Node",
        enableRowGroup: true,
        sortable: true,
        filter: true,
      },
      {
        field: "proxyItem",
        width: 300,
        headerName: 'Proxy Item',
        enableRowGroup: true,
        sortable: true,
        filter: true,
        editable: true,
        cellEditor: 'agRichSelectCellEditor',
        cellEditorParams: {
          allowTyping: true,
          searchType: 'fuzzy',
          filterList: true,
          highlightMatch: true,
          values: proxyItemOptions.map(opt => opt.value),
          cellRenderer: (params) => {
            const found = proxyItemOptions.find(opt => opt.value === params.value);
            return found ? found.label : params.value;
          }
        },

        valueFormatter: params => {
          if (!params.value) return '';
          const proxyItem = proxyItemOptions.find(p => p.value === params.value);
          return proxyItem ? proxyItem.label : params.value;
        },
      },
      {
        field: "launchdate",
        headerName: 'Launch Date',
        valueGetter: params => {
          return params?.data?.launchdate?.value || '';
        },
        sortable: true,
        filter: true,
      },
      {
        field: "enddate",
        headerName: 'End Date',
        sortable: true,
        filter: true,
        valueGetter: params => {
          return params?.data?.enddate?.value || '';
        },
      },
      {
        field: "baseprice",
        headerName: 'Base Price',
        sortable: true,
        filter: true,
        valueFormatter: params => {
          return params.value ? new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(params.value) : '';
        },
      },
      {
        field: "lifestatus",
        headerName: 'Life Status',
        sortable: true,
        filter: true,
      },
      {
        field: "productspecification",
        headerName: 'Specification',
        sortable: true,
        filter: true,
      },
      {
        field: "productcategory",
        headerName: 'Category',
        sortable: true,
        filter: true,
      },
      {
        field: "productdivision",
        headerName: 'Division',
        sortable: true,
        filter: true,
      },
      {
        field: "tas7",
        headerName: 'TAS7',
        sortable: true,
        filter: true,
      },
      {
        field: "tas30",
        headerName: 'TAS30',
        sortable: true,
        filter: true,
      },
      {
        field: "tas90",
        headerName: 'TAS90',
        sortable: true,
        filter: true,
      },
      // Generate month group columns dynamically from available months
      ...availableMonths.map(monthCol => {
        const monthId = monthCol.monthId;
        const daysInMonth = globalMonthDays[monthId] || [];
        // console.log('daysInMonth', daysInMonth, monthId);
        return {
          headerName: monthCol.label,
          children: [
            // Month total (shown when collapsed)
            {
              columnGroupShow: 'closed',
              headerName: 'Total',
              field: `${monthId}`,
              editable: (params) => {
                if (!params.data) return false;
                return params.data.months?.[monthId]?.isValid || false;
              },
              width: 100,
              valueGetter: params => {
                if (!params.data?.months?.[monthId]) return 0;
                return params.data.months[monthId].total || 0;
              },
              valueSetter: params => {
                if (!params.data) return false;
                const { data, newValue } = params;
                const field = params.colDef.field;

                // Input validation and sanitization
                const sanitizedValue = (() => {
                  if (newValue == null || newValue === '') return 0;
                  const num = Number(newValue);
                  if (isNaN(num)) return 0;
                  if (Math.abs(num) > 999999999) return 0;
                  return Math.round(num * 100) / 100;
                })();

                // Ensure months and days objects exist
                if (!data.months) data.months = {};
                if (!data.days) data.days = {};

                // Update month total
                data.months[field] = {
                  ...data.months[field],
                  total: sanitizedValue,
                  label: data.months[field]?.label || field,
                  numDays: data.months[field]?.numDays || 0,
                  validDays: getValidDaysCount(data, field)
                };

                // Redistribute across valid days
                const distribution = distributeMonthTotal(sanitizedValue, data, field);
                Object.keys(distribution).forEach(day => {
                  if (!data.days[day]) data.days[day] = { total: 0 };
                  data.days[day].total = distribution[day].total;
                });

                return true;
              },
              valueFormatter: params => {
                if (params.value == null) return '';
                if (showDollars) {
                  const basePrice = params.data?.baseprice || 0;
                  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(Math.round(params.value * basePrice));
                }
                return Math.round(params.value).toLocaleString();
              },
              cellStyle: (params) => {
                const baseStyle = { textAlign: 'right' };
                if (!params.data) return baseStyle;

                const isValid = params.data.months?.[monthId]?.isValid || false;
                const total = params.data.months?.[monthId]?.total || 0;

                if (!isValid) {
                  return {
                    ...baseStyle,
                    backgroundColor: 'black',
                    color: total === 0 ? 'black' : 'white'
                  };
                }
                return baseStyle;
              }
            },
            // Individual day columns (shown when expanded)
            ...daysInMonth.map(day => ({
              columnGroupShow: 'open',
              headerName: dayjs(day).format('M/D'),
              field: `${day}`,
              editable: (params) => {
                if (!params.data) return false;
                return params.data.days?.[day]?.isValid || false;
              },
              width: 60,
              valueGetter: params => {
                if (!params.data?.days?.[day]) return 0;
                return params.data.days[day].total || 0;
              },
              valueSetter: params => {
                if (!params.data) return false;
                const { data, newValue } = params;
                const field = params.colDef.field;

                // Input validation and sanitization
                const sanitizedValue = (() => {
                  if (newValue == null || newValue === '') return 0;
                  const num = Number(newValue);
                  if (isNaN(num)) return 0;
                  if (Math.abs(num) > 999999999) return 0;
                  return Math.round(num * 100) / 100;
                })();

                // Ensure days and months objects exist
                if (!data.days) data.days = {};
                if (!data.months) data.months = {};

                // Update day value
                data.days[field] = { total: sanitizedValue };

                // Recalculate month total
                const month = field.substring(0, 7); // Extract YYYY-MM from YYYY-MM-DD
                const monthTotal = calculateMonthTotal(data, month);
                data.months[month] = {
                  ...data.months[month],
                  total: monthTotal,
                  label: data.months[month]?.label || month,
                  numDays: data.months[month]?.numDays || 0,
                  validDays: getValidDaysCount(data, month)
                };

                return true;
              },
              valueFormatter: params => {
                if (params.value == null) return '';
                if (showDollars) {
                  const basePrice = params.data?.baseprice || 0;
                  return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(Math.round(params.value * basePrice));
                }
                return Math.round(params.value).toLocaleString();
              },
              cellStyle: (params) => {
                const baseStyle = { textAlign: 'right' };
                if (!params.data) return baseStyle;

                const isValid = params.data.days?.[day]?.isValid || false;
                const total = params.data.days?.[day]?.total || 0;

                if (!isValid) {
                  return {
                    ...baseStyle,
                    backgroundColor: 'black',
                    color: total === 0 ? 'black' : 'white'
                  };
                }
                return baseStyle;
              }
            }))
          ]
        };
      }),
    ];
  }, [showDollars, compMethod, notes, proxyItemOptions, availableMonths, globalMonthDays]);

  // TODO: Clean up orphaned commented code
  // Temporarily commenting out orphaned code to get basic functionality working
  /*
      //         const isInvalidDateRange = !isValidDateRange(row, monthLabel, true);
      //         if (isInvalidDateRange) {
      //           if (params.value > 0) {
      //             return { backgroundColor: 'black', color: 'white', fontWeight: 'bold' };
      //           }
      //           return { backgroundColor: 'black', color: 'black' };
      //         }
  
      //         return null;
      //       },
      //       valueFormatter: params => {
      //         if (params.value == null) return '';
      //         if (showDollars) {
      //           return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(Math.round(params.value));
      //         }
      //         return Math.round(params.value).toLocaleString();
      //       },
      //       valueGetter: params => {
      //         if (!params.data) return 0;
      //         const monthDays = getDaysInMonth(monthLabel);
      //         let total = 0;
      //         monthDays.forEach(dayField => {
      //           if (isValidDateRange(params.data, dayField, false)) {
      //             const qty = Number(getCurrentValue(params.data, dayField));
      //             if (showDollars) {
      //               const basePrice = params.data.baseprice || 0;
      //               const msrpDiscount = params.data.msrp_discount || 1;
      //               total += qty * basePrice * msrpDiscount;
      //             } else {
      //               total += qty;
      //             }
      //           }
      //         });
      //         return showDollars ? Math.round(total) : Math.round(total);
      //       },
      //       cellRenderer: params => {
      //         const { data: row, value } = params;
      //         let displayValue = params.valueFormatted || params.value || 0;
      //         const comparisonTotal = getComparisonMonthTotal(row, monthLabel);
      //         const { diff: compDiff, percentDiff: compPercentDiff } = getDifference(value, comparisonTotal);
      //         const absDiff = Math.abs(compPercentDiff);
  
      //         // With live saving, changes are saved immediately
      //         const hasUserChanges = false;
  
      //         // Check for notes
      //         let hasNotes = false;
      //         let noteType = null;
  
      //         // Debug logging for the first cell to understand what's happening
      //         if (row && row.upc === '810084159057' && monthLabel === next12MonthsGlobal[0]) {
      //           console.log('Checking notes for row:', row.upc, row.forecast_node, 'month:', monthLabel);
      //           console.log('Available notes:', notes.length);
      //           console.log('Notes data sample:', notes.slice(0, 2));
      //         }
  
      //         if (notes && notes.length > 0 && row) {
      //           hasNotes = notes.some(note => {
      //             const upcMatch = note.upcs && note.upcs.includes(row.upc);
      //             const nodeMatch = note.forecast_nodes && note.forecast_nodes.includes(row.forecast_node);
      //             const hasDateRange = note.date_range && note.date_range.start && note.date_range.end;
  
      //             // Debug for specific row
      //             if (row.upc === '810084159057' && monthLabel === next12MonthsGlobal[0]) {
      //               console.log('Note check:', note.id,
      //                 'upcMatch:', upcMatch,
      //                 'nodeMatch:', nodeMatch,
      //                 'hasDateRange:', hasDateRange);
      //             }
  
      //             if (!upcMatch || !nodeMatch || !hasDateRange) return false;
  
      //             // Check if any day in the month falls within the note's date range
      //             const monthDays = getDaysInMonth(monthLabel);
      //             const dateInRange = monthDays.some(dayStr => isDateInRange(dayStr, note.date_range));
  
      //             if (row.upc === '810084159057' && monthLabel === next12MonthsGlobal[0] && upcMatch && nodeMatch) {
      //               console.log('Date range check for note:', note.id, 'result:', dateInRange);
      //               console.log('Note date range:', note.date_range);
      //               console.log('First few days in month:', monthDays.slice(0, 3));
      //             }
  
      //             return dateInRange;
      //           });
  
      //           // Get highest priority note type
      //           noteType = hasNotes ? notes
      //             .filter(note =>
      //               note.upcs.includes(row.upc) &&
      //               note.forecast_nodes.includes(row.forecast_node) &&
      //               note.date_range &&
      //               (() => {
      //                 // Check if any day in the month falls within the note's date range
      //                 const monthDays = getDaysInMonth(monthLabel);
      //                 return monthDays.some(dayStr => isDateInRange(dayStr, note.date_range));
      //               })()
      //             )
      //             .sort((a, b) => {
      //               const typePriority = { todo: 1, review: 2, general: 3 };
      //               return typePriority[a.type] - typePriority[b.type];
      //             })[0]?.type : null;
      //         }
  
      //         let direction = '';
      //         let backgroundColor = 'inherit';
      //         if (absDiff > 0.15) {
      //           direction = compPercentDiff > 0 ? '↑' : '↓';
      //           backgroundColor = compPercentDiff > 0 ? '#d6c6f7' : '#ffa940';
      //         } else if (absDiff > 0.2) {
      //           direction = compPercentDiff > 0 ? '↑' : '↓';
      //           backgroundColor = compPercentDiff > 0 ? '#d6c6f7' : '#ffa940';
      //         } else if (absDiff > 0.5) {
      //           direction = compPercentDiff > 0 ? '↑' : '↓';
      //           backgroundColor = compPercentDiff > 0 ? '#d6c6f7' : '#ffa940';
      //         }
  
      //         // Debug for specific row
      //         if (row && row.upc === '810084159057' && monthLabel === next12MonthsGlobal[0]) {
      //           console.log('Final hasNotes value:', hasNotes, 'noteType:', noteType);
      //         }
  
      //         return (
      //           <div style={{ position: 'relative', padding: '2px' }}>
      //             {hasNotes && (
      //               <div
      //                 className={`note-indicator ${noteType}`}
      //                 style={{
      //                   position: 'absolute',
      //                   top: 0,
      //                   left: 0,
      //                   width: 0,
      //                   height: 0,
      //                   borderStyle: 'solid',
      //                   borderWidth: '8px 8px 0 0',
      //                   borderColor: noteType === 'todo' ? '#ff4d4f transparent transparent transparent' :
      //                     noteType === 'review' ? '#fa8c16 transparent transparent transparent' :
      //                       '#1890ff transparent transparent transparent',
      //                   zIndex: 1
      //                 }}
      //               />
      //             )}
      //             <span
      //               style={{
      //                 color: hasUserChanges ? 'blue' : 'inherit',
      //                 fontWeight: hasUserChanges ? 'bold' : 'normal',
      //                 backgroundColor: backgroundColor,
      //                 marginLeft: hasNotes ? '4px' : 0
      //               }}
      //               title={
      //                 (() => {
      //                   // Gather percent diff and summary stats for all comparison methods
      //                   const methods = [
      //                     { key: 'original', label: 'Original' },
      //                     // { key: 'external', label: 'External' },
      //                     { key: 'seasonalTas7', label: 'Seasonal TAS7' },
      //                     { key: 'seasonalTas30', label: 'Seasonal TAS30' },
      //                     { key: 'seasonalTas90', label: 'Seasonal TAS90' }
      //                   ];
      //                   const stats = methods.map(m => {
      //                     const compVal = (() => {
      //                       if (!row || !row[monthLabel]) return 0;
      //                       switch (m.key) {
      //                         case 'original': return row[monthLabel].original || 0;
      //                         case 'external': return row[monthLabel].external || 0;
      //                         case 'seasonalTas7': return row[monthLabel].seasonal_tas7 || 0;
      //                         case 'seasonalTas30': return row[monthLabel].seasonal_tas30 || 0;
      //                         case 'seasonalTas90': return row[monthLabel].seasonal_tas90 || 0;
      //                         default: return 0;
      //                       }
      //                     })();
      //                     const { diff, percentDiff } = getDifference(value, compVal);
      //                     let percentDisplay;
      //                     if (Math.abs(percentDiff) > 10) {
      //                       percentDisplay = (percentDiff > 0 ? '+' : '') + '∞';
      //                     } else {
      //                       percentDisplay = (percentDiff > 0 ? '+' : '') + (percentDiff * 100).toFixed(1) + '%';
      //                     }
      //                     return `${m.label}: ${compVal.toLocaleString()} (${percentDisplay}, Δ${diff.toLocaleString()})`;
      //                   });
      //                   // Add summary stats
      //                   const allVals = methods.map(m => {
      //                     if (!row || !row[monthLabel]) return 0;
      //                     switch (m.key) {
      //                       case 'original': return row[monthLabel].original || 0;
      //                       case 'external': return row[monthLabel].external || 0;
      //                       case 'seasonalTas7': return row[monthLabel].seasonal_tas7 || 0;
      //                       case 'seasonalTas30': return row[monthLabel].seasonal_tas30 || 0;
      //                       case 'seasonalTas90': return row[monthLabel].seasonal_tas90 || 0;
      //                       default: return 0;
      //                     }
      //                   });
      //                   const min = Math.min(...allVals);
      //                   const max = Math.max(...allVals);
      //                   const avg = allVals.reduce((a, b) => a + b, 0) / allVals.length;
      //                   stats.push(`Min: ${min.toLocaleString()}`);
      //                   stats.push(`Max: ${max.toLocaleString()}`);
      //                   stats.push(`Avg: ${avg.toLocaleString(undefined, { maximumFractionDigits: 1 })}`);
  
      //                   // Add note information to tooltip
      //                   if (hasNotes) {
      //                     const noteInfo = notes
      //                       .filter(note =>
      //                         note.upcs.includes(row.upc) &&
      //                         note.forecast_nodes.includes(row.forecast_node) &&
      //                         note.date_range &&
      //                         (() => {
      //                           const startDate = new Date(note.date_range.start);
      //                           const endDate = new Date(note.date_range.end);
      //                           const monthStart = new Date(monthLabel.split(' ')[1], monthNames.indexOf(monthLabel.split(' ')[0]), 1);
      //                           const monthEnd = new Date(monthStart.getFullYear(), monthStart.getMonth() + 1, 0);
      //                           return monthStart <= endDate && monthEnd >= startDate;
      //                         })()
      //                       )
      //                       .map(note => `${note.type.toUpperCase()}: ${note.note}`)
      //                       .join('\n');
      //                     stats.push(`\nNotes:\n${noteInfo}`);
      //                   }
  
      //                   return stats.join('\n');
      //                 })()
      //               }
      //             >
      //               {hasUserChanges ? '*' : ''}
      //               {displayValue}
      //               {compDiff !== 0 ? ` (${compDiff})` : ''}
      //               {direction}
      //             </span>
      //           </div>
      //         );
      //       },
      //       valueParser: params => {
      //         console.log("params", params);
      //         let value = String(params.newValue);
  
      //         // Remove common formatting characters
      //         value = value.replace(/[,]/g, ''); // Remove commas
      //         value = value.replace(/[^\d.-]/g, ''); // Keep only digits, decimal points, and minus signs
  
      //         const numberValue = Number(value);
      //         return isNaN(numberValue) ? 0 : numberValue;
      //       },
      //       valueSetter: params => {
      //         const { data, newValue } = params;
      //         if (!data) return false;
      //         console.log("newValue", newValue);
  
      //         const allMonthDays = getDaysInMonth(monthLabel);
  
      //         const validDays = allMonthDays.filter(dayField =>
      //           isValidDateRange(data, dayField, false)
      //         );
  
      //         // Set invalid days to 0 first
      //         allMonthDays.forEach(dayField => {
      //           if (!validDays.includes(dayField)) {
      //             if (!data[dayField]) data[dayField] = {};
      //             data[dayField].current = 0;
      //           }
      //         });
  
      //         const totalValidDays = validDays.length;
      //         if (totalValidDays > 0) {
      //           const valueToDistribute = Number(newValue) || 0;
      //           const baseValue = Math.floor(valueToDistribute / totalValidDays);
      //           const remainder = valueToDistribute % totalValidDays;
      //           validDays.forEach((dayField, index) => {
      //             if (!data[dayField]) data[dayField] = {};
      //             data[dayField].current = baseValue + (index < remainder ? 1 : 0);
      //           });
      //         }
  
      //         return true; // Indicate success
      //       },
      //     },
      //     // show the per-day columns when the group is open
      //     ...getDaysInMonth(monthLabel).filter(dayStr => {
      //       // Only include dates that are today or in the future
      //       // Fix timezone issue by parsing both dates consistently in local time
      //       const [year, month, day] = dayStr.split('-').map(Number);
      //       const currentDate = new Date(year, month - 1, day); // Local time
      //       const today = new Date();
      //       const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate()); // Today at local midnight
      //       return currentDate >= todayStart;
      //     }).map(dayStr => {
      //       const [year, month, day] = dayStr.split('-').map(Number);
      //       return {
      //         columnGroupShow: 'open',
      //         headerName: `${month}/${day}`,
      //         field: dayStr,
      //         editable: (params) => {
      //           if (!params.data || showDollars) return false;
      //           return isValidDateRange(params.data, dayStr, false);
      //         },
      //         cellStyle: params => {
      //           if (!params.data) return null;
      //           let cellStyle = {
      //             fontStyle: 'italic',
      //             fontSize: 11,
      //           };
      //           // Check if date range is invalid
      //           const isInvalidDateRange = !isValidDateRange(params.data, dayStr, false);
  
      //           if (isInvalidDateRange) {
      //             if (params.value > 0) {
      //               cellStyle.backgroundColor = 'black';
      //               cellStyle.color = 'white';
      //               cellStyle.fontWeight = 'bold';
      //             } else {
      //               cellStyle.backgroundColor = 'black';
      //               cellStyle.color = 'black';
      //             }
      //             return cellStyle;
      //           }
  
      //           // For dollar mode, keep existing comparison-based text coloring
      //           if (showDollars) {
      //             // Unified comparison logic for daily columns
      //             const comparisonValue = getComparisonValue(params.data, dayStr);
      //             const currentValue = params.data[dayStr] || 0;
  
      //             const shouldShowDifference = currentValue !== comparisonValue;
  
      //             if (shouldShowDifference) {
      //               const delta = currentValue - comparisonValue;
      //               cellStyle.color = delta > 0 ? '#52c41a' : '#ff4d4f';
      //               cellStyle.fontWeight = 'bold';
      //               return cellStyle;
      //             }
      //           }
  
      //           return cellStyle;
      //         },
      //         cellEditor: 'agNumberCellEditor',
      //         valueParser: params => Number(params.newValue),
      //         valueSetter: params => {
      //           const { data, newValue } = params;
      //           if (!data || !data[dayStr]) return false;
  
      //           data[dayStr].current = Number(newValue) || 0;
      //           return true;
      //         },
      //         valueGetter: params => {
      //           if (!params.data) return 0;
      //           const qty = Number(getCurrentValue(params.data, dayStr));
      //           if (showDollars) {
      //             const basePrice = params.data.baseprice || 0;
      //             const msrpDiscount = params.data.msrp_discount || 1;
      //             return Math.round(qty * basePrice * msrpDiscount);
      //           }
      //           return qty;
      //         },
      //         valueFormatter: params => {
      //           if (params.value == null) return '';
      //           if (showDollars) {
      //             return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(Math.round(params.value));
      //           }
      //           return Math.round(params.value);
      //         },
      //         cellRenderer: params => {
      //           const rowData = params.data;
      //           let displayValue = params.valueFormatted || params.value || 0;
      //           const currentVal = params.value || 0;
  
      //           // With live saving, changes are saved immediately
      //           const hasUserChanges = false;
  
      //           // Check for notes
      //           const hasNotes = notes.some(note =>
      //             note.upcs.includes(rowData.upc) &&
      //             note.forecast_nodes.includes(rowData.forecast_node) &&
      //             note.date_range &&
      //             isDateInRange(dayStr, note.date_range)
      //           );
  
      //           // Get highest priority note type
      //           const noteType = hasNotes ? notes
      //             .filter(note =>
      //               note.upcs.includes(rowData.upc) &&
      //               note.forecast_nodes.includes(rowData.forecast_node) &&
      //               note.date_range &&
      //               isDateInRange(dayStr, note.date_range)
      //             )
      //             .sort((a, b) => {
      //               const typePriority = { todo: 1, review: 2, general: 3 };
      //               return typePriority[a.type] - typePriority[b.type];
      //             })[0]?.type : null;
  
      //           let direction = '';
      //           let backgroundColor = 'inherit';
      //           let color = hasUserChanges ? 'blue' : 'inherit';
      //           let fontWeight = hasUserChanges ? 'bold' : 'normal';
      //           let fontStyle = 'italic';
  
      //           if (!showDollars) {
      //             // Quantity mode: show comparison indicators
      //             const comparisonValue = getComparisonValue(rowData, dayStr);
      //             const { percentDiff } = getDifference(currentVal, comparisonValue);
      //             const absDiff = Math.abs(percentDiff);
  
      //             if (absDiff > 0.15) {
      //               direction = percentDiff > 0 ? '↑' : '↓';
      //               backgroundColor = percentDiff > 0 ? '#d6c6f7' : '#ffa940';
      //             }
  
      //             if (hasUserChanges) displayValue = `${displayValue} *`;
      //             if (direction) displayValue = `${displayValue} ${direction}`;
      //           } else {
      //             // Dollar mode: show delta if different
      //             const comparisonQty = getComparisonValue(rowData, dayStr);
      //             const comparisonValue = Math.round(comparisonQty * (rowData.baseprice || 0) * (rowData.msrp_discount || 1));
      //             if (currentVal !== comparisonValue) {
      //               const delta = currentVal - comparisonValue;
      //               let deltaText = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(delta);
      //               if (delta > 0) deltaText = `+${deltaText}`;
      //               displayValue = `${displayValue} (${deltaText})`;
      //             }
      //           }
  
      //           return (
      //             <div style={{ position: 'relative', padding: '2px' }}>
      //               {hasNotes && (
      //                 <div
      //                   style={{
      //                     position: 'absolute',
      //                     top: 0,
      //                     left: 0,
      //                     width: 0,
      //                     height: 0,
      //                     borderStyle: 'solid',
      //                     borderWidth: '8px 8px 0 0',
      //                     borderColor: noteType === 'todo' ? '#ff4d4f transparent transparent transparent' :
      //                       noteType === 'review' ? '#fa8c16 transparent transparent transparent' :
      //                         '#1890ff transparent transparent transparent',
      //                     zIndex: 1
      //                   }}
      //                 />
      //               )}
      //               <span
      //                 style={{
      //                   color,
      //                   fontWeight,
      //                   fontStyle,
      //                   backgroundColor,
      //                   marginLeft: hasNotes ? '4px' : 0
      //                 }}
      //                 title={
      //                   (() => {
      //                     // Gather percent diff and summary stats for all comparison methods
      //                     const methods = [
      //                       { key: 'original', label: 'Original' },
      //                       { key: 'external', label: 'External' },
      //                       { key: 'seasonalTas7', label: 'Seasonal TAS7' },
      //                       { key: 'seasonalTas30', label: 'Seasonal TAS30' },
      //                       { key: 'seasonalTas90', label: 'Seasonal TAS90' }
      //                     ];
      //                     const stats = methods.map(m => {
      //                       const compVal = (() => {
      //                         if (!rowData || !rowData[dayStr]) return 0;
      //                         switch (m.key) {
      //                           case 'original': return rowData[dayStr].original || 0;
      //                           case 'external': return rowData[dayStr].external || 0;
      //                           case 'seasonalTas7': return rowData[dayStr].seasonal_tas7 || 0;
      //                           case 'seasonalTas30': return rowData[dayStr].seasonal_tas30 || 0;
      //                           case 'seasonalTas90': return rowData[dayStr].seasonal_tas90 || 0;
      //                           default: return 0;
      //                         }
      //                       })();
      //                       const { diff, percentDiff } = getDifference(currentVal, compVal);
      //                       let percentDisplay;
      //                       if (Math.abs(percentDiff) > 10) {
      //                         percentDisplay = (percentDiff > 0 ? '+' : '') + '∞';
      //                       } else {
      //                         percentDisplay = (percentDiff > 0 ? '+' : '') + (percentDiff * 100).toFixed(1) + '%';
      //                       }
      //                       return `${m.label}: ${compVal.toLocaleString()} (${percentDisplay}, Δ${diff.toLocaleString()})`;
      //                     });
      //                     // Add summary stats
      //                     const allVals = methods.map(m => {
      //                       if (!rowData || !rowData[dayStr]) return 0;
      //                       switch (m.key) {
      //                         case 'original': return rowData[dayStr].original || 0;
      //                         case 'external': return rowData[dayStr].external || 0;
      //                         case 'seasonalTas7': return rowData[dayStr].seasonal_tas7 || 0;
      //                         case 'seasonalTas30': return rowData[dayStr].seasonal_tas30 || 0;
      //                         case 'seasonalTas90': return rowData[dayStr].seasonal_tas90 || 0;
      //                         default: return 0;
      //                       }
      //                     });
      //                     const min = Math.min(...allVals);
      //                     const max = Math.max(...allVals);
      //                     const avg = allVals.reduce((a, b) => a + b, 0) / allVals.length;
      //                     stats.push(`Min: ${min.toLocaleString()}`);
      //                     stats.push(`Max: ${max.toLocaleString()}`);
      //                     stats.push(`Avg: ${avg.toLocaleString(undefined, { maximumFractionDigits: 1 })}`);
  
      //                     // Add note information to tooltip
      //                     if (hasNotes) {
      //                       const noteInfo = notes
      //                         .filter(note =>
      //                           note.upcs.includes(rowData.upc) &&
      //                           note.forecast_nodes.includes(rowData.forecast_node) &&
      //                           note.date_range &&
      //                           isDateInRange(dayStr, note.date_range)
      //                         )
      //                         .map(note => `${note.type.toUpperCase()}: ${note.note}`)
      //                         .join('\n');
      //                       stats.push(`\nNotes:\n${noteInfo}`);
      //                     }
  
      //                     return stats.join('\n');
      //                   })()
      //                 }
      //               >
      //                 {displayValue}
      //               </span>
      //             </div>
      //           );
      //         },
      //       };
      //     })
        ]
      })),
  */

  // Additional grid event handlers and other functions...

  // const getContextMenuItems = useCallback((params) => {
  //   // get all cells that are selected
  //   const selectedCells = gridRef.current.api.getCellRanges();
  //   if (selectedCells.length === 0) return [];

  //   const uniqueUpcs = new Set();
  //   const uniqueForecastNodes = new Set();
  //   let earliestDate = null;
  //   let latestDate = null;

  //   selectedCells.forEach(range => {
  //     const { startRow, endRow, columns } = range;

  //     // Get all rows in the range
  //     for (let rowIdx = startRow.rowIndex; rowIdx <= endRow.rowIndex; rowIdx++) {
  //       const row = rowData[rowIdx];
  //       if (row) {
  //         uniqueUpcs.add(row.upc);
  //         uniqueForecastNodes.add(row.forecast_node);
  //       }
  //     }

  //     // Get all columns in the range
  //     for (let { colDef } of columns) {
  //       if (colDef) {
  //         console.log('Column in range:', {
  //           colId: colDef.colId,
  //           field: colDef.field,
  //           headerName: colDef.headerName,
  //           columnGroupShow: colDef.columnGroupShow
  //         });
  //         if (colDef.columnGroupShow === 'closed') {
  //           // Month column - add month label (e.g., "Jul 25")
  //           // Extract the month label from the column ID
  //           const monthLabel = colDef.colId.replace('-total', '');
  //           if (monthLabel && monthLabel.match(/^[A-Za-z]{3} \d{2}$/)) {
  //             // get all dates in the month
  //             const dates = getDaysInMonth(monthLabel);
  //             console.log('Month column dates:', dates);
  //             dates.forEach(date => {
  //               // Use UTC to avoid timezone issues
  //               const [year, month, day] = date.split('-').map(Number);
  //               const dateObj = new Date(Date.UTC(year, month - 1, day));
  //               console.log('Converting date string:', date, 'to UTC Date object:', dateObj);
  //               if (!earliestDate || dateObj < earliestDate) {
  //                 earliestDate = dateObj;
  //               }
  //               if (!latestDate || dateObj > latestDate) {
  //                 latestDate = dateObj;
  //               }
  //             });
  //           }
  //         } else if (colDef.colId && colDef.colId.match(/^\d{4}-\d{2}-\d{2}$/)) {
  //           // Day column - add specific date
  //           console.log('Day column selected by colId:', colDef.colId);
  //           // Use UTC to avoid timezone issues
  //           const [year, month, day] = colDef.colId.split('-').map(Number);
  //           const dateObj = new Date(Date.UTC(year, month - 1, day));
  //           console.log('Day column UTC date object:', dateObj);
  //           console.log('Day column - before update - earliestDate:', earliestDate, 'latestDate:', latestDate);
  //           if (!earliestDate || dateObj < earliestDate) {
  //             earliestDate = dateObj;
  //             console.log('Day column - updated earliestDate to:', earliestDate);
  //           }
  //           if (!latestDate || dateObj > latestDate) {
  //             latestDate = dateObj;
  //             console.log('Day column - updated latestDate to:', latestDate);
  //           }
  //           console.log('Day column - after update - earliestDate:', earliestDate, 'latestDate:', latestDate);
  //         } else if (colDef.field && colDef.field.match(/^\d{4}-\d{2}-\d{2}$/)) {
  //           // Day column - add specific date (using field instead of colId)
  //           console.log('Day column selected by field:', colDef.field);
  //           // Use UTC to avoid timezone issues
  //           const [year, month, day] = colDef.field.split('-').map(Number);
  //           const dateObj = new Date(Date.UTC(year, month - 1, day));
  //           console.log('Day column UTC date object:', dateObj);
  //           console.log('Day column - before update - earliestDate:', earliestDate, 'latestDate:', latestDate);
  //           if (!earliestDate || dateObj < earliestDate) {
  //             earliestDate = dateObj;
  //             console.log('Day column - updated earliestDate to:', earliestDate);
  //           }
  //           if (!latestDate || dateObj > latestDate) {
  //             latestDate = dateObj;
  //             console.log('Day column - updated latestDate to:', latestDate);
  //           }
  //           console.log('Day column - after update - earliestDate:', earliestDate, 'latestDate:', latestDate);
  //         }
  //       }
  //     }
  //   });

  //   const result = [
  //     {
  //       name: "Notes",
  //       action: () => {
  //         console.log('Context menu action - earliestDate:', earliestDate);
  //         console.log('Context menu action - latestDate:', latestDate);
  //         console.log('Context menu action - earliestDate type:', typeof earliestDate, 'value:', earliestDate);
  //         console.log('Context menu action - latestDate type:', typeof latestDate, 'value:', latestDate);

  //         // If only one cell is selected, set the date range to just that day
  //         let dateRange = null;
  //         if (earliestDate && latestDate) {
  //           console.log('Both dates exist, checking if they are the same...');
  //           console.log('earliestDate.getTime():', earliestDate.getTime());
  //           console.log('latestDate.getTime():', latestDate.getTime());
  //           console.log('Are they equal?', earliestDate.getTime() === latestDate.getTime());

  //           if (earliestDate.getTime() === latestDate.getTime()) {
  //             // Single day selected
  //             dateRange = {
  //               start: earliestDate.toISOString().split('T')[0], // Just the date part YYYY-MM-DD
  //               end: earliestDate.toISOString().split('T')[0]
  //             };
  //             console.log('Single day dateRange:', dateRange);
  //           } else {
  //             // Multiple days selected
  //             dateRange = {
  //               start: earliestDate.toISOString().split('T')[0], // Just the date part YYYY-MM-DD
  //               end: latestDate.toISOString().split('T')[0]
  //             };
  //             console.log('Multiple days dateRange:', dateRange);
  //           }
  //         } else {
  //           console.log('No dates selected - earliestDate:', earliestDate, 'latestDate:', latestDate);
  //         }

  //         console.log('Setting selectedCellInfo with dateRange:', dateRange);
  //         setSelectedCellInfo({
  //           upcs: Array.from(uniqueUpcs),
  //           forecastNodes: Array.from(uniqueForecastNodes),
  //           dateRange: dateRange,
  //         });
  //         setShowNotesModal(true);
  //       },
  //       cssClasses: ["red", "bold"],
  //     },
  //     {
  //       name: "Historical Sales",
  //       action: () => {
  //         setSelectedCellInfo({
  //           upcs: Array.from(uniqueUpcs),
  //           forecastNodes: Array.from(uniqueForecastNodes),
  //           includeForecast: true,
  //           dateRange: '365'
  //         });
  //         setShowHistoricalSalesModal(true);
  //       },
  //       cssClasses: ["blue", "bold"],
  //     },
  //     "copy",
  //     "copyWithHeaders",
  //     "csvExport",
  //     "excelExport",
  //     "chartRange",
  //   ];

  //   if (params.column?.getColId() === "country") {
  //     return new Promise((res) => setTimeout(() => res(result), 150));
  //   }
  //   return result;
  // }, [rowData, columnDefs]);

  return (
    <>
      <div style={{ marginBottom: 10 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'set-method',
                    label: 'Forecast Method',
                    onClick: () => setShowBulkMethodModal(true),
                  },
                  {
                    key: 'set-proxy',
                    label: 'Proxy Item',
                    onClick: () => setShowBulkProxyItemModal(true),
                  },
                ],
              }}
              disabled={selectedCount === 0}
            >
              <Button
                icon={<MenuOutlined />}
              />
            </Dropdown>
            <Tooltip title="Clear all filters">
              <Button onClick={onClearFilters} icon={<CloseCircleOutlined />} />
            </Tooltip>

            <Tooltip title="Refresh data from Firestore">
              <Button
                onClick={refreshData}
                icon={<ReloadOutlined />}
                loading={isRefreshing}
              />
            </Tooltip>

            <Tooltip title="Upload a CSV or Excel file to update forecast quantities in bulk.">
              <Button
                icon={<UploadOutlined />}
                onClick={() => setShowUploadModal(true)}
              // disabled={forecastDataLoading}
              />
            </Tooltip>

            <Tooltip title="Notes">
              <Button
                icon={<FileTextOutlined />}
                onClick={() => {
                  setSelectedCellInfo({
                    upcs: [],
                    forecastNodes: [],
                    dateRange: null
                  });
                  setShowNotesModal(true);
                }}
              />
            </Tooltip>
            <Tooltip title="Sales History">
              <Button
                icon={<HistoryOutlined />}
                onClick={() => {
                  setSelectedCellInfo({
                    upcs: [],
                    forecastNodes: [],
                    includeForecast: true,
                    dateRange: '365'
                  });
                  setShowHistoricalSalesModal(true);
                }}
              />
            </Tooltip>
            {rowData.length > 0 && (
              <Text>{rowData.length} items</Text>
            )}
          </Col>
          <Col>
            <Row align="middle" gutter={8}>
              <Col>
                <Tooltip title="Compare current values against different data sources. 'Original' shows differences from the original data.">
                  <Select
                    style={{ minWidth: 150 }}
                    placeholder="Select Comparison"
                    value={compMethod}
                    onChange={(value) => {
                      setCompMethod(value);
                      gridRef.current.api.refreshCells({ force: true });
                    }}
                    options={[
                      { label: 'Original', value: 'original' },
                      { label: 'Last Year Same Month', value: 'lastYearSameMonth' },
                      { label: 'Seasonal (TAS7)', value: 'seasonalTas7' },
                      { label: 'Seasonal (TAS30)', value: 'seasonalTas30' },
                      { label: 'Seasonal (TAS90)', value: 'seasonalTas90' }
                    ]}
                  />
                </Tooltip>
              </Col>
              <Col>
                <Tooltip title="Switch between viewing forecast quantities in units or in calculated dollar amounts based on price.">
                  <Switch
                    checkedChildren="Dollars"
                    unCheckedChildren="Quantity"
                    checked={showDollars}
                    onChange={setShowDollars}
                  />
                </Tooltip>
              </Col>
              <Col>
                <SavedViews
                  gridRef={gridRef}
                  collectionName="demandPlanViews"
                  userPreferenceKey="lastDemandPlanView"
                  selectWidth="200px"
                  saveButtonText="Save View"
                />
              </Col>

            </Row>
          </Col>
        </Row>
      </div>

      {/* Last Updated Timestamp */}
      {lastUpdated && (
        <div style={{
          padding: '8px 16px',
          backgroundColor: '#f5f5f5',
          borderTop: '1px solid #d9d9d9',
          fontSize: '12px',
          color: '#666'
        }}>
          Last updated: {lastUpdated.toLocaleString()}
        </div>
      )}

      <div style={{ height: 'calc(90vh - 25px)', width: '100%' }}>
        <AgGridReact
          loading={demandPlanLoading}
          size="small"
          ref={gridRef}
          columnDefs={columnDefs}
          rowData={rowData}
          onCellValueChanged={onCellValueChanged}
          // onColumnGroupOpened={handleColumnGroupExpanded}
          pagination={false}
          // paginationPageSize={50}
          animateRows={false}
          cellSelection={true}
          rowGroupPanelShow="always"
          suppressAggFuncInHeader={true}
          autoSizeStrategy={{
            type: 'fitCellContents',
            defaultMinWidth: 80,
          }}
          // initialState={initialState}
          rowClassRules={{
            // Moved invalid date range styling to enddate column's cellStyle
            'ag-row-even': params => params.rowIndex % 2 === 0,
          }}
          rowSelection={{
            mode: 'multiRow',
            checkboxes: true,
            headerCheckbox: true
          }}
          onSelectionChanged={onSelectionChanged}
          sideBar={{
            toolPanels: [
              {
                id: 'columns',
                labelDefault: 'Columns',
                labelKey: 'columns',
                iconKey: 'columns',
                toolPanel: 'agColumnsToolPanel',
              },
              {
                id: 'filters',
                labelDefault: 'Filters',
                labelKey: 'filters',
                iconKey: 'filter',
                toolPanel: 'agFiltersToolPanel',
              }
            ],
            // defaultToolPanel: 'filters',
          }}
          getRowId={params => params.data?.id}
        // getContextMenuItems={getContextMenuItems}

        // onSelectionChanged={onSelectionChanged}
        // onCellDoubleClicked={params => {
        //   // Only open daily forecast for month columns
        //   if (next12MonthsGlobal.includes(params.colDef.field)) {
        //     setSelectedCellForDaily({
        //       rowData: params.data,
        //       month: params.colDef.field,
        //       currentValue: params.value || 0
        //     });
        //     setShowDailyForecastModal(true);
        //   }
        // }}
        />
      </div>
      {/* <Modal
        title="Upload Demand Data"
        open={showUploadModal}
        // onOk={handleProcessUpload}
        onCancel={() => {
          setShowUploadModal(false);
          setUploadedFile(null);
        }}
        okText="Apply Upload"
        okButtonProps={{ disabled: !uploadedFile }}
        destroyOnClose
      >
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <p>Upload a CSV or Excel file to update forecast quantities. You can upload by individual days or by monthly totals.</p>
            <p>The upload will only update existing items already in the plan.</p>
          </Col>
          <Col span={12}>
            <Button icon={<DownloadOutlined />} onClick={() => handleDownloadTemplate('daily')} block>
              Daily Template
            </Button>
            <small>Use <code>date</code> column (YYYY-MM-DD).</small>
          </Col>
          <Col span={12}>
            <Button icon={<DownloadOutlined />} onClick={() => handleDownloadTemplate('monthly')} block>
              Monthly Template
            </Button>
            <small>Use <code>month</code> column (e.g., Jul 24).</small>
          </Col>
          <Col span={24}>
            <Upload
              beforeUpload={(file) => {
                setUploadedFile(file);
                return false;
              }}
              onRemove={() => {
                setUploadedFile(null);
              }}
              fileList={uploadedFile ? [uploadedFile] : []}
              maxCount={1}
            >
              <Button icon={<UploadOutlined />}>Select File</Button>
            </Upload>
          </Col>
        </Row>
      </Modal>
      <Modal
        title="Set Forecast Method for Selected Rows"
        open={showBulkMethodModal}
        // onOk={handleBulkMethodUpdate}
        onCancel={() => {
          setShowBulkMethodModal(false);
          setBulkMethodValue(null);
        }}
        okButtonProps={{ disabled: !bulkMethodValue }}
      >
        <Select
          style={{ width: '100%' }}
          placeholder="Select Forecast Method"
          value={bulkMethodValue}
          onChange={setBulkMethodValue}
          // options={forecastMethodOptions}
        />
      </Modal>
      <Modal
        title="Set Proxy Item for Selected Rows"
        open={showBulkProxyItemModal}
        onOk={handleBulkProxyItemUpdate}
        onCancel={() => {
          setShowBulkProxyItemModal(false);
          setBulkProxyItemValue(null);
        }}
        okButtonProps={{ disabled: !bulkProxyItemValue }}
      >
        <Select
          showSearch
          style={{ width: '100%' }}
          placeholder="Select Proxy Item"
          value={bulkProxyItemValue}
          onChange={setBulkProxyItemValue}
          options={proxyItemOptions}
          optionFilterProp="label"
          filterOption={(input, option) => {
            const text = option.label?.toLowerCase() || '';
            return input
              .toLowerCase()
              .split(' ')
              .filter(term => term)
              .every(term => text.includes(term));
          }}
          allowClear
        />
      </Modal> */}
      {showNotesModal && (
        <NotesModal
          visible={showNotesModal}
          onClose={() => {
            setShowNotesModal(false);
            // Refresh cells to update note indicators
            if (gridRef.current && gridRef.current.api) {
              gridRef.current.api.refreshCells({ force: true });
            }
          }}
          initialSelection={selectedCellInfo}
          rowData={rowData}
        />
      )}
      {showHistoricalSalesModal && (
        <HistoricalSalesModal
          visible={showHistoricalSalesModal}
          onClose={() => setShowHistoricalSalesModal(false)}
          initialSelection={selectedCellInfo}
          rowData={rowData}
          autoSearch={true}
        />
      )}

    </>
  );
};

export default DemandPlan;
