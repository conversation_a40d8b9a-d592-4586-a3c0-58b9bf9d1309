import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Paper,
  Grid,
  CircularProgress
} from '@mui/material';
import {
  <PERSON><PERSON>hart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import ChartExportWrapper from './ChartExportWrapper';
// Enhanced color palette for better visualization
const CHART_COLORS = [
  '#2196f3', // blue
  '#4caf50', // green
  '#ff9800', // orange
  '#e91e63', // pink
  '#9c27b0', // purple
  '#00acc1', // cyan
  '#3f51b5', // indigo
  '#8bc34a', // light green
  '#ffc107', // amber
  '#795548', // brown
  '#607d8b', // blue grey
  '#ff5722'  // deep orange
];

const VIEW_LIMITS = {
  productType: 15,
  color: 25
};

const KPI_COLORS = {
  products: { bg: '#e3f2fd', text: '#1976d2' },  // blue theme
  sales: { bg: '#e8f5e9', text: '#2e7d32' },     // green theme
  revenue: { bg: '#fff3e0', text: '#f57c00' }    // orange theme
};

const VIEW_MODES = [
  { value: 'productType', label: 'Product Type' },
  { value: 'color', label: 'Color' },
  { value: 'division', label: 'Division' },
  { value: 'category', label: 'Category' },
  { value: 'family', label: 'Family' },
  { value: 'specification', label: 'Specification' }
];

const ProductPerformanceDashboard = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [chartView, setChartView] = useState('monthly');
  const [viewMode, setViewMode] = useState('productType');
  const [metricType, setMetricType] = useState('sales');

  const formatValue = (value, isRevenue) => {
    if (isRevenue) {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        notation: 'compact',
        maximumFractionDigits: 1
      }).format(value);
    }
    return new Intl.NumberFormat('en-US', {
      notation: 'compact',
      maximumFractionDigits: 1
    }).format(value);
  };

  const formatDate = (dateStr) => {
    return dateStr; // Simply return as is since it's already in YYYY-MM format
  };

  const getSourceData = () => {
    if (!data) return {};

    switch (viewMode) {
      case 'productType':
        return data.byProductType || {};
      case 'color':
        return data.byColor || {};
      case 'division':
        return data.byDivision || {};
      case 'category':
        return data.byCategory || {};
      case 'family':
        return data.byFamily || {};
      case 'specification':
        return data.bySpecification || {};
      default:
        return {};
    }
  };

  const transformMonthlyData = () => {
    if (!data) return { monthlyData: [], series: [] };

    const sourceData = getSourceData();
    const months = {};
    const seriesSet = new Set();

    // Handle limits for different views
    let entriesToProcess = Object.entries(sourceData);
    if (viewMode === 'color' || viewMode === 'productType') {
      const limit = VIEW_LIMITS[viewMode];
      entriesToProcess = Object.entries(sourceData)
        .map(([key, value]) => ({
          key,
          value,
          total: metricType === 'sales' ? value.totalSales : value.totalRevenue
        }))
        .sort((a, b) => b.total - a.total)
        .slice(0, limit)
        .map(({ key, value }) => [key, value]);
    }

    entriesToProcess.forEach(([itemKey, itemData]) => {
      if (itemData.monthlyData && itemData.monthlyData.length > 0) {
        seriesSet.add(itemKey);

        itemData.monthlyData.forEach(month => {
          const monthKey = month.month;
          if (!months[monthKey]) {
            months[monthKey] = { month: monthKey };
          }
          months[monthKey][`${itemKey}_${metricType}`] = month[metricType];
        });
      }
    });

    return {
      monthlyData: Object.values(months).sort((a, b) => a.month.localeCompare(b.month)),
      series: Array.from(seriesSet)
    };
  };

  const CustomTooltip = ({ active, payload, label, isMonthly }) => {
    if (!active || !payload) return null;

    return (
      <Paper
        elevation={3}
        sx={{
          p: 2,
          backgroundColor: 'background.paper',
          border: '1px solid rgba(0, 0, 0, 0.12)'
        }}
      >
        <Typography variant="subtitle2" gutterBottom>
          {label} {/* Using the YYYY-MM format directly */}
        </Typography>
        {payload.map((entry, index) => {
          const isRevenue = entry.dataKey.includes('revenue') || entry.dataKey === 'totalRevenue';
          const name = isMonthly ? entry.dataKey.split('_')[0] : entry.name;

          return (
            <Box
              key={index}
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                gap: 2,
                alignItems: 'center',
                my: 0.5
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Box
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: entry.color
                  }}
                />
                <Typography variant="body2">{name}:</Typography>
              </Box>
              <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                {formatValue(entry.value, isRevenue)}
              </Typography>
            </Box>
          );
        })}
      </Paper>
    );
  };

  const renderMonthlyChart = () => {
    const { monthlyData, series } = transformMonthlyData();

    if (!monthlyData.length) return null;

    return (
      <Box sx={{ height: 500, mt: 2 }}>
        {(viewMode === 'color' || viewMode === 'productType') && (
          <Typography
            color="text.secondary"
            sx={{ mb: 2, fontSize: '0.875rem', fontStyle: 'italic' }}
          >
            Note: Displaying top {VIEW_LIMITS[viewMode]} {viewMode === 'color' ? 'colors' : 'product types'} by {metricType}
          </Typography>
        )}
        <ResponsiveContainer>
          <LineChart data={monthlyData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
            <XAxis
              dataKey="month"
              height={50}
              tick={{ fontSize: 12 }}
              tickMargin={10} // Add more margin for the longer date format
              interval={0}   // Show all months
              angle={-45}    // Angle the dates for better readability
              textAnchor="end" // Align the angled text
            />
            <YAxis
              tickFormatter={(value) => formatValue(value, metricType === 'revenue')}
              tick={{ fontSize: 12 }}
              width={80}
            />
            <Tooltip content={(props) => <CustomTooltip {...props} isMonthly={true} />} />
            <Legend
              wrapperStyle={{ paddingTop: 20 }}
              formatter={(value) => <span style={{ fontSize: 12 }}>{value}</span>}
            />
            {series.map((item, index) => (
              <Line
                key={item}
                type="monotone"
                dataKey={`${item}_${metricType}`}
                name={item}
                stroke={CHART_COLORS[index % CHART_COLORS.length]}
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 6 }}
              />
            ))}
          </LineChart>
        </ResponsiveContainer>
      </Box>
    );
  };

  const renderSummaryChart = () => {
    if (!data) return null;

    const sourceData = getSourceData();
    let chartData = Object.entries(sourceData)
      .map(([key, value]) => ({
        name: key,
        totalSales: value.totalSales || 0,
        totalRevenue: value.totalRevenue || 0
      }))
      .sort((a, b) => b[`total${metricType.charAt(0).toUpperCase() + metricType.slice(1)}`] -
        a[`total${metricType.charAt(0).toUpperCase() + metricType.slice(1)}`]);

    // Apply limits for both color and product type views
    if (viewMode === 'color' || viewMode === 'productType') {
      const limit = VIEW_LIMITS[viewMode];
      chartData = chartData.slice(0, limit);
    }

    return (
      <Box>
        {(viewMode === 'color' || viewMode === 'productType') && (
          <Typography
            color="text.secondary"
            sx={{ mb: 2, fontSize: '0.875rem', fontStyle: 'italic' }}
          >
            Note: Displaying top {VIEW_LIMITS[viewMode]} {viewMode === 'color' ? 'colors' : 'product types'} by {metricType}
          </Typography>
        )}
        <Box sx={{ height: 500, mt: 2 }}>
          <ResponsiveContainer>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
              <XAxis
                dataKey="name"
                angle={-45}
                textAnchor="end"
                height={80}
                interval={0}
                tick={{ fontSize: 12 }}
              />
              <YAxis
                tickFormatter={(value) => formatValue(value, metricType === 'revenue')}
                tick={{ fontSize: 12 }}
                width={80}
              />
              <Tooltip content={(props) => <CustomTooltip {...props} isMonthly={false} />} />
              <Bar
                dataKey={`total${metricType.charAt(0).toUpperCase() + metricType.slice(1)}`}
                fill={CHART_COLORS[0]}
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </Box>
      </Box>
    );
  };

  const calculateTotals = () => {
    if (!data) return { products: 0, sales: 0, revenue: 0 };

    const sourceData = getSourceData();
    const totals = Object.values(sourceData).reduce((acc, curr) => {
      acc.sales += curr.totalSales || 0;
      acc.revenue += curr.totalRevenue || 0;
      acc.products += curr.items || 0;
      return acc;
    }, { products: 0, sales: 0, revenue: 0 });

    return totals;
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const functions = getFunctions();
        const getProductPerformanceMetrics = httpsCallable(functions, 'getProductPerformanceMetrics');
        const result = await getProductPerformanceMetrics();
        setData(result.data);
      } catch (error) {
        console.error('Error fetching performance data:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height={400}>
        <CircularProgress />
      </Box>
    );
  }

  const totals = calculateTotals();

  return (
    <ChartExportWrapper title={`Product_Performance_${viewMode}_${chartView}_${metricType}`}>

      <Box sx={{ p: 3 }}>
        {/* Chart Controls */}
        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          gap: 2,
          mb: 3,
          justifyContent: 'space-between'
        }}>
          <Tabs
            value={chartView}
            onChange={(e, newValue) => setChartView(newValue)}
            aria-label="chart view tabs"
          >
            <Tab label="Monthly Trends" value="monthly" />
            <Tab label="Summary" value="summary" />
          </Tabs>

          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Tabs
              value={metricType}
              onChange={(e, newValue) => setMetricType(newValue)}
              aria-label="metric type tabs"
            >
              <Tab label="Sales Volume" value="sales" />
              <Tab label="Revenue" value="revenue" />
            </Tabs>
          </Box>
        </Box>

        {/* View Mode Selector */}
        <Box sx={{ mb: 3 }}>
          <Tabs
            value={viewMode}
            onChange={(e, newValue) => setViewMode(newValue)}
            aria-label="view mode tabs"
            variant="scrollable"
            scrollButtons="auto"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            {VIEW_MODES.map((mode) => (
              <Tab key={mode.value} label={mode.label} value={mode.value} />
            ))}
          </Tabs>
        </Box>

        {/* KPI Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {[
            {
              label: 'Products with Sales',
              value: totals.products.toLocaleString(),
              type: 'products'
            },
            {
              label: 'Total Sales',
              value: totals.sales.toLocaleString(),
              type: 'sales'
            },
            {
              label: 'Total Revenue',
              value: formatValue(totals.revenue, true),
              type: 'revenue'
            }
          ].map((stat) => (
            <Grid item xs={12} md={4} key={stat.type}>
              <KPICard
                title={stat.label}
                value={stat.value}
                bgColor={KPI_COLORS[stat.type].bg}
                textColor={KPI_COLORS[stat.type].text}
              />
            </Grid>
          ))}
        </Grid>

        {/* Chart */}
        <Paper elevation={3} sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            {`${chartView === 'monthly' ? 'Monthly' : 'Summary'} Performance by ${VIEW_MODES.find(mode => mode.value === viewMode)?.label}`}
          </Typography>
          {chartView === 'monthly' ? renderMonthlyChart() : renderSummaryChart()}
        </Paper>
      </Box>
    </ChartExportWrapper>
  );
};

export default ProductPerformanceDashboard;