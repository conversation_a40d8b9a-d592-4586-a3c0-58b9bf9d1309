module.exports = {
  env: {
    es6: true,
    node: true,
  },
  parserOptions: {
    "ecmaVersion": 2020,
  },
  extends: [
    "eslint:recommended",
    "google",
  ],
  rules: {
    "no-restricted-globals": ["error", "name", "length"],
    "prefer-arrow-callback": "error",
    "quotes": ["error", "double", { "allowTemplateLiterals": true }],
    "max-len": ["error", { "code": 180 }],
    "linebreak-style": "off",
    "comma-dangle": "off",
    "object-curly-spacing": "off",
    "indent": "off",
    "quote-props": "off",
    "no-trailing-spaces": "off",
    "spaced-comment": "off",
    "operator-linebreak": "off",
    "arrow-parens": "off",
    "eol-last": "off",
    "no-unused-vars": "warn",
  },
  overrides: [
    {
      files: ["**/*.spec.*"],
      env: {
        mocha: true,
      },
      rules: {},
    },
  ],
  globals: {},
};
