/* eslint-disable new-cap */
/* eslint-disable react/prop-types */
/* eslint-disable require-jsdoc */
/* eslint-disable guard-for-in */

// todo move inventory report to this page
// import logo from './logo.svg';
// import '../App.css';
import {
  // useEffect,
  useEffect,
  useState,
} from 'react';
import React from 'react';
import axios from 'axios';
import {api} from './firebase';
import {Input, Table, Layout, Menu, Typography} from 'antd';
import CustomerTab from '../components/wholesale/CustomerTab';
import OrdersTab from '../components/wholesale/OrdersTab';
import OrderDetailsModal from '../components/wholesale/OrderDetailsModal';
import { ArrowRightOutlined, ArrowLeftOutlined } from '@ant-design/icons';
const searchUrl =
  // eslint-disable-next-line max-len
  'https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1873&deploy=1&compid=6810379&ns-at=AAEJ7tMQ62yb0CjU7hRc7bEjjwQw9nJDo95m1agKif4C71WED_8&key=Hydr04L1fe!&searchId=';


function Wholesale({userObj}) {
  const [selectedTab, setSelectedTab] = useState('orders');
  const [modalItem, setModalItem] = useState('');
  const statusMap = {
    B: 'Pending Fulfillment',
    A: 'Pending Approval',
    E: 'Pending Billing/Partially Fulfilled',
  };
  userObj.page = 'wholesale';

  const menuItems = [
    // {
    //   key: 'customer',
    //   label: 'Customer',
    //   onClick: () => setSelectedTab('customer')
    // },
    {
      key: 'orders',
      label: 'Orders',
      onClick: () => setSelectedTab('orders')
    }
  ];

  const handleMenuClick = (e) => {
    setSelectedTab(e.key);
  };

  const renderContent = () => {
    switch (selectedTab) {
      // case 'customer':
      //   return <CustomerTab />;
      case 'orders':
        return <OrdersTab />;
      default:
        return null;
    }
  };

  return (
    <div style={{ display: 'flex', minHeight: '100vh' }}>
      <Menu
        mode="vertical"
        selectedKeys={[selectedTab]}
        onClick={handleMenuClick}
        items={menuItems}
        style={{ width: 100 }}
      />
      <div style={{ flex: 1, padding: 5 }}>
        {renderContent()}
      </div>
    </div>
  );
}
export default Wholesale;
