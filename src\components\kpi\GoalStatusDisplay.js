import React from 'react';
import { Card, CardContent, Typography, Box, Alert, AlertTitle } from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import RemoveIcon from '@mui/icons-material/Remove';
import { useGoalEvaluation } from '../../hooks/useGoalEvaluation';

const GoalStatusDisplay = ({
  currentValue,
  goalConfig,
  className = '',
  showScore = true,
  size = 'medium',
  showTrend = true,
  trendValue = 0,
  title
}) => {
  const { score, status, message, performanceLevel } = useGoalEvaluation(
    currentValue,
    goalConfig
  );

  const formatNumber = (value) => {
    return typeof value === 'number'
      ? value.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
      : value;
  };

  const getScoreColor = () => {
    switch (performanceLevel) {
      case 'excellent':
        return { bg: '#e8f5e9', text: '#2e7d32', border: '#66bb6a' };
      case 'good':
        return { bg: '#e3f2fd', text: '#1976d2', border: '#42a5f5' };
      case 'fair':
        return { bg: '#fff3e0', text: '#f57c00', border: '#ffb74d' };
      case 'poor':
        return { bg: '#fbe9e7', text: '#d32f2f', border: '#ef5350' };
      default:
        return { bg: '#f5f5f5', text: '#757575', border: '#bdbdbd' };
    }
  };

  const getTrendIcon = () => {
    if (!showTrend) return null;
    if (trendValue === 0) {
      return <RemoveIcon sx={{ fontSize: 16, color: '#757575' }} />;
    }

    // For metrics where lower is better (like DIO, COGS, Transportation costs)
    const isLowerBetter = goalConfig.goalCondition === 'lower' || goalConfig.goalCondition === 'lowerThanMax';

    // A negative trend is good when we want lower values
    // A positive trend is good when we want higher values
    const isImproving = isLowerBetter ? trendValue < 0 : trendValue > 0;

    if (isImproving) {
      return <TrendingUpIcon sx={{ fontSize: 16, color: '#2e7d32' }} />;
    } else {
      return <TrendingDownIcon sx={{ fontSize: 16, color: '#d32f2f' }} />;
    }
  };

  const sizeStyles = {
    small: { p: 1, fontSize: '0.875rem' },
    medium: { p: 2, fontSize: '1rem' },
    large: { p: 3, fontSize: '1.25rem' }
  };

  const getStatusAlert = () => {
    if (status === 'warning') {
      let alertMessage = '';

      if (goalConfig.hasOwnProperty('min') && goalConfig.hasOwnProperty('max')) {
        switch (goalConfig.goalCondition) {
          case 'inRange':
            alertMessage = `Current value (${formatNumber(currentValue)}${goalConfig.unit}) should be between ${formatNumber(goalConfig.min)}${goalConfig.unit} and ${formatNumber(goalConfig.max)}${goalConfig.unit}`;
            break;
          case 'higherThanMin':
            alertMessage = `Current value (${formatNumber(currentValue)}${goalConfig.unit}) is below minimum target of ${formatNumber(goalConfig.min)}${goalConfig.unit}`;
            break;
          case 'lowerThanMax':
            alertMessage = `Current value (${formatNumber(currentValue)}${goalConfig.unit}) exceeds maximum target of ${formatNumber(goalConfig.max)}${goalConfig.unit}`;
            break;
          default:
            alertMessage = `Current value requires attention to meet target`;
        }
      } else {
        // Handle single value goals
        const targetValue = parseFloat(goalConfig.value);
        alertMessage = goalConfig.goalCondition === 'lower'
          ? `Current value (${formatNumber(currentValue)}${goalConfig.unit}) is above the target of ${formatNumber(targetValue)}${goalConfig.unit}`
          : goalConfig.goalCondition === 'higher'
            ? `Current value (${formatNumber(currentValue)}${goalConfig.unit}) is below the target of ${formatNumber(targetValue)}${goalConfig.unit}`
            : `Current value requires attention to meet target`;
      }

      return (
        <Alert severity="warning" sx={{ mt: 2 }}>
          <AlertTitle>Action Needed</AlertTitle>
          {alertMessage}
        </Alert>
      );
    }
    return null;
  };

  const getPerformanceLabel = () => {
    switch (performanceLevel) {
      case 'excellent':
        return 'Excellent';
      case 'good':
        return 'Good';
      case 'fair':
        return 'Fair';
      case 'poor':
        return 'Needs Improvement';
      default:
        return 'No Data';
    }
  };

  const colors = getScoreColor();
  const formattedValue = formatNumber(currentValue);

  return (
    <Card className={className} sx={{ boxShadow: 1 }}>
      {title && (
        <Typography variant="h6" sx={{ px: 2, pt: 2 }}>
          {title}
        </Typography>
      )}
      <CardContent sx={{
        p: sizeStyles[size].p,
        '&:last-child': { pb: sizeStyles[size].p },
        display: 'flex',
        alignItems: 'flex-start',
        gap: 2
      }}>
        {showScore && (
          <Box sx={{
            width: 48,
            height: 48,
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: colors.bg,
            border: `2px solid ${colors.border}`,
            color: colors.text,
            fontWeight: 'bold',
            fontSize: sizeStyles[size].fontSize,
            p: 3,
            minWidth: 48
          }}>
            {Math.round(score)}
          </Box>
        )}

        <Box sx={{ flex: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 600,
                fontSize: sizeStyles[size].fontSize
              }}
            >
              {formattedValue}{goalConfig.unit}
            </Typography>
            {getTrendIcon()}
          </Box>

          <Typography
            variant="body2"
            sx={{
              color: 'text.secondary',
              mt: 0.5,
              fontSize: '0.875rem'
            }}
          >
            {message}
          </Typography>

          {showScore && (
            <Typography
              variant="body2"
              sx={{ mt: 0.5, fontSize: '0.875rem' }}
            >
              Performance:
              <Box
                component="span"
                sx={{
                  fontWeight: 500,
                  textTransform: 'capitalize',
                  ml: 0.5,
                  color: colors.text
                }}
              >
                {getPerformanceLabel()}
              </Box>
            </Typography>
          )}
        </Box>
      </CardContent>

      {getStatusAlert()}
    </Card>
  );
};

export default GoalStatusDisplay;