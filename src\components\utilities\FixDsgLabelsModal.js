import React, { useState } from 'react';
import { Modal, Upload, Typography, Button, message } from 'antd';
const { PDFDocument, rgb } = require('pdf-lib');

const { Title } = Typography;
const FixDsgLabelsModal = ({ show, onClose }) => {
  const [fileList, setFileList] = useState([]);

  const handleFileChange = (info) => {
    setFileList(info.fileList);
  };

  const handleOk = () => {
    fileList.forEach(async (file) => {
      try {
        const reader = new FileReader();
        reader.onload = async (e) => {
          const pdfBytes = new Uint8Array(e.target.result);
          const pdfDoc = await PDFDocument.load(pdfBytes);
          const pages = pdfDoc.getPages();

          pages.forEach(page => {
            const { width, height } = page.getSize();
            page.drawText('Sort Code O', {
              x: width * 0.7,
              y: height * 0.5,
              size: 12,
              color: rgb(0, 0, 0)
            });
          });

          const modifiedPdfBytes = await pdfDoc.save();
          
          // Create download link
          const blob = new Blob([modifiedPdfBytes], { type: 'application/pdf' });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `fixed_${file.name}`;
          link.click();
          window.URL.revokeObjectURL(url);
        };
        reader.readAsArrayBuffer(file.originFileObj);
      } catch (error) {
        console.error('Error processing PDF:', error);
      }
    });
    setFileList([]);
    message.success('PDFs fixed successfully, Check your downloads');
    onClose();
  };

  return (
    <Modal
      title="Fix Dsg Labels"
      open={show}
      onOk={handleOk}
      onCancel={onClose}
    >
      <Title level={3} style={{ marginTop: '0px' }}>Upload PDFs</Title>
      <Upload
        name="labelFile"
        accept=".pdf"
        multiple={true}
        onChange={handleFileChange}
        fileList={fileList}
      >
        <Button>Upload</Button>
      </Upload>
    </Modal>
  );
};

export default FixDsgLabelsModal;