/* eslint-disable guard-for-in */
import React from 'react';
import { Layout, Tabs } from 'antd';


import ManageExternalDemandPlans from './ManageExternalDemandPlans';
import SummaryExternalDemandPlans from './SummaryExternalDemandPlans';
const { TabPane } = Tabs;
// this component is used to display the external demand plans for each forecast node. It should companre the 'demand plan' with the 'external demand plans' and display the difference
// it should also allow the user to upload new external demand plans and delete existing ones
const ExternalDemandPlans = ({ userObj }) => {
  return (
    <Layout>
      <Tabs
        defaultActiveKey="1"
        items={[{
          key: '1', label: 'Manage', children: <ManageExternalDemandPlans userObj={userObj} />
        }, {
          key: '2', label: 'Summary', children: <SummaryExternalDemandPlans />
        }]}
      />

    </Layout>
  );
};

export default ExternalDemandPlans;