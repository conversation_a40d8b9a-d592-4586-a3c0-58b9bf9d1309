/* eslint-disable guard-for-in */
const {onCall} = require("firebase-functions/v2/https");
const functions = require("firebase-functions");
const {makeNSSavedSearchRequest, executeNSSuiteQLQuery} = require("../helpers/netsuite");

// Function to get COGS data
/**
 * Fetches COGS data from NetSuite saved search 103501
 * @return {Promise<Array>} Array of COGS data objects
 * @throws {Error} Throws an error if the request fails
 */
async function getCOGSData() {
  try {
    const parsedData = await makeNSSavedSearchRequest("103501");

    return parsedData
        .filter((row) => row["Month of Date"] !== "Total")
        .map((row) => ({
          month: row["Month of Date"],
          location: row["Location"],
          productType: row["Product Type"],
          upcCode: row["UPC Code"],
          totalCOGS: parseFloat(row["Sum of Amount"]) || 0,
        }));
  } catch (error) {
    console.error("Error fetching COGS data:", error);
    throw error;
  }
}
/**
 *
 * @return {Promise<Array>} Array of inventory data objects
 */
async function getAverageInventoryData() {
  const inventoryQuery = `
    WITH inventory_data AS (
      SELECT 
        i.upccode AS upc_code,
        TO_CHAR(ih.custrecord_ih_date, 'YYYY-MM') AS month,
        ROUND(AVG(ih.custrecord_ih_qty_available), 2) AS avg_quantity_available,
        i.custitem_shopify_sales_price AS item_cost
      FROM 
        customrecord_inventory_history ih
      JOIN 
        item i ON ih.custrecord_ih_item = i.id
      WHERE 
        ih.custrecord_ih_qty_available > 0
      GROUP BY 
        i.upccode, TO_CHAR(ih.custrecord_ih_date, 'YYYY-MM'), i.custitem_shopify_sales_price
    )
    SELECT 
      id.upc_code,
      id.month,
      id.avg_quantity_available,
      id.item_cost,
      ROUND(id.avg_quantity_available * id.item_cost, 2) AS total_avg_inventory_cost,
      i.custitem20 AS life_status,
      i.custitem24 AS launch_date,
      i.custitem25 AS end_date
    FROM 
      inventory_data id
    JOIN 
      item i ON id.upc_code = i.upccode
    ORDER BY 
      id.upc_code, id.month
  `;

  return await executeNSSuiteQLQuery(inventoryQuery);
}

exports.getInventoryTurnoverRatio = onCall(async (data, context) => {
  try {
    const [cogsResult, inventoryResult] = await Promise.all([
      getCOGSData(),
      getAverageInventoryData(),
    ]);

    const today = new Date();
    const lastCompleteMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    const lastCompleteMonthString = lastCompleteMonth.toISOString().substring(0, 7);

    console.log(`Last complete month: ${lastCompleteMonthString}`);

    const earliestInventoryMonth = inventoryResult.items
        .map((item) => item.month)
        .sort()[0];

    console.log(`Earliest inventory month: ${earliestInventoryMonth}`);

    const filteredCogsResult = cogsResult.filter((item) =>
      item.month >= earliestInventoryMonth && item.month <= lastCompleteMonthString,
    );
    const filteredInventoryResult = {
      ...inventoryResult,
      items: inventoryResult.items.filter((item) =>
        item.month >= earliestInventoryMonth && item.month <= lastCompleteMonthString,
      ),
    };

    console.log("Filtered COGS Result length:", filteredCogsResult.length);
    console.log("Filtered Inventory Result length:", filteredInventoryResult.items.length);

    if (!filteredCogsResult || filteredCogsResult.length === 0) {
      throw new functions.https.HttpsError("internal", "Filtered COGS data is empty or has no items");
    }

    if (!filteredInventoryResult || !filteredInventoryResult.items || !Array.isArray(filteredInventoryResult.items)) {
      throw new functions.https.HttpsError("internal", "Invalid filtered inventory data structure");
    }

    const turnoverRatios = {};
    const monthlyTotals = {};
    let totalCOGS = 0;
    let totalAverageInventory = 0;

    console.log("Processing filtered COGS data:");
    filteredCogsResult.forEach((item) => {
      const upc = item.upcCode;
      const month = item.month;
      const cogs = item.totalCOGS;

      if (!turnoverRatios[upc]) {
        turnoverRatios[upc] = {monthlyData: {}};
      }
      if (!turnoverRatios[upc].monthlyData[month]) {
        turnoverRatios[upc].monthlyData[month] = {cogs: 0, averageInventory: 0, turnoverRatio: 0};
      }
      turnoverRatios[upc].monthlyData[month].cogs += cogs;

      if (!monthlyTotals[month]) {
        monthlyTotals[month] = {date: month, cogs: 0, averageInventory: 0, turnoverRatio: 0};
      }
      monthlyTotals[month].cogs += cogs;
      totalCOGS += cogs;

      // console.log(`UPC: ${upc}, Month: ${month}, COGS: ${cogs}, Running Total: ${totalCOGS}`);
    });

    console.log(`Final Total COGS: ${totalCOGS}`);

    console.log("Processing filtered inventory data:");
    filteredInventoryResult.items.forEach((item) => {
      const upc = item.upc_code;
      const month = item.month;
      const totalAvgInventoryCost = parseFloat(item.total_avg_inventory_cost) || 0;

      if (!turnoverRatios[upc]) {
        turnoverRatios[upc] = {monthlyData: {}};
      }
      if (!turnoverRatios[upc].monthlyData[month]) {
        turnoverRatios[upc].monthlyData[month] = {cogs: 0, averageInventory: 0, turnoverRatio: 0};
      }
      turnoverRatios[upc].monthlyData[month].averageInventory = totalAvgInventoryCost;

      if (!monthlyTotals[month]) {
        monthlyTotals[month] = {date: month, cogs: 0, averageInventory: 0, turnoverRatio: 0};
      }
      monthlyTotals[month].averageInventory += totalAvgInventoryCost;
      totalAverageInventory += totalAvgInventoryCost;

      turnoverRatios[upc].lifeStatus = item.life_status;
      turnoverRatios[upc].launchDate = item.launch_date;
      turnoverRatios[upc].endDate = item.end_date;

      // console.log(`UPC: ${upc}, Month: ${month}, Avg Inventory Cost: ${totalAvgInventoryCost}, Running Total: ${totalAverageInventory}`);
    });

    console.log(`Final Total Average Inventory Cost: ${totalAverageInventory}`);

    for (const upc in turnoverRatios) {
      let upcTotalCOGS = 0;
      let upcTotalInventory = 0;
      for (const month in turnoverRatios[upc].monthlyData) {
        const monthData = turnoverRatios[upc].monthlyData[month];
        monthData.turnoverRatio = monthData.averageInventory !== 0 ? monthData.cogs / monthData.averageInventory : 0;
        upcTotalCOGS += monthData.cogs;
        upcTotalInventory += monthData.averageInventory;
      }
      turnoverRatios[upc].overallTurnoverRatio = upcTotalInventory !== 0 ? upcTotalCOGS / upcTotalInventory : 0;
    }

    for (const month in monthlyTotals) {
      monthlyTotals[month].turnoverRatio = monthlyTotals[month].averageInventory !== 0 ?
        monthlyTotals[month].cogs / monthlyTotals[month].averageInventory :
        0;
    }

    const overallTurnoverRatio = totalAverageInventory !== 0 ?
      totalCOGS / totalAverageInventory :
      0;

    const runningData = Object.values(monthlyTotals).sort((a, b) => a.date.localeCompare(b.date));

    console.log("Final calculations:");
    console.log(`Total COGS: ${totalCOGS}`);
    console.log(`Total Average Inventory: ${totalAverageInventory}`);
    console.log(`Overall Turnover Ratio: ${overallTurnoverRatio}`);

    return {
      turnoverRatios,
      runningData,
      overallTurnoverRatio,
      totalCOGS,
      totalAverageInventory,
      lastCompleteMonth: lastCompleteMonthString,
      earliestDataMonth: earliestInventoryMonth,
    };
  } catch (error) {
    console.error("Error calculating Inventory Turnover Ratio:", error);
    throw new functions.https.HttpsError("internal", "Failed to calculate Inventory Turnover Ratio", error.message);
  }
});

exports.getBackorderRate = onCall(
  {
    timeoutSeconds: 300,
  },
  async (data, context) => {
    try {
      console.log("Fetching backorder rate data...");

      // Get backorder data from saved search
      const backorderData = await makeNSSavedSearchRequest("96709");

      if (!backorderData || backorderData.length === 0) {
        console.warn("No data returned from backorder saved search");
        throw new functions.https.HttpsError(
          "not-found",
          "No backorder data available"
        );
      }
    // Get the date range from backorder data
    const backorderDates = backorderData.map((order) => new Date(order["Sales Order Date"]));
    const earliestDate = new Date(Math.min(...backorderDates));
    const latestDate = new Date(Math.max(...backorderDates));

    // Format dates for SQL
    const formattedEarliestDate = earliestDate.toISOString().split("T")[0];
    const formattedLatestDate = latestDate.toISOString().split("T")[0];

    // Modified query to sum quantities instead of counting orders
    const totalOrdersQuery = `
      SELECT 
        TO_CHAR(transaction.trandate, 'YYYY-MM') AS month_year,
        CUSTOMRECORD_PRODUCT_TYPE.name AS product_type,
        SUM(transactionline.quantity) AS total_quantity
      FROM 
        transaction
      JOIN 
        transactionline ON transaction.id = transactionline.transaction
      JOIN 
        item ON transactionline.item = item.id
      LEFT JOIN 
        CUSTOMRECORD_PRODUCT_TYPE ON item.custitem_product_type = CUSTOMRECORD_PRODUCT_TYPE.id
      WHERE 
        transaction.type = 'SalesOrd'
        AND transaction.trandate BETWEEN TO_DATE('${formattedEarliestDate}', 'YYYY-MM-DD') 
        AND TO_DATE('${formattedLatestDate}', 'YYYY-MM-DD')
      GROUP BY 
        TO_CHAR(transaction.trandate, 'YYYY-MM'),
        CUSTOMRECORD_PRODUCT_TYPE.name
      ORDER BY 
        TO_DATE(TO_CHAR(transaction.trandate, 'YYYY-MM'), 'YYYY-MM') ASC
    `;

    console.log("Executing total orders query...");
    const totalOrdersResult = await executeNSSuiteQLQuery(totalOrdersQuery);
    console.log("Total Orders Result:", JSON.stringify(totalOrdersResult));

    // Process the results using processBackorderRateData function
    const processedData = processBackorderRateData(
        backorderData,
        totalOrdersResult.items || [],
    );

    console.log("Backorder rate data processed successfully");
    return processedData;
  } catch (error) {
    console.error("Error calculating Backorder Rate:", error);
    throw new functions.https.HttpsError("internal", "Failed to calculate Backorder Rate", error.message);
  }
});
/**
 * @param {Array} backorderData - Array of backorder data objects
 * @param {Array} totalOrdersData - Array of total order data objects
 * @return {Object} Processed backorder
 * rate data object
 */
function processBackorderRateData(backorderData, totalOrdersData) {
  console.log("Processing backorder rate data...");

  const monthlyData = {};
  let totalBackorderedQty = 0;
  let totalOrderedQty = 0;

  // First, group total order quantities by month and product type
  totalOrdersData.forEach((item) => {
    const monthYear = item.month_year;
    const productType = item.product_type || "Unknown";
    const totalQuantity = Math.abs(parseFloat(item.total_quantity || 0));

    if (!monthlyData[monthYear]) {
      monthlyData[monthYear] = {
        monthYear,
        totalOrders: 0,
        backorders: 0,
        backorderRate: 0,
        products: {},
      };
    }

    if (!monthlyData[monthYear].products[productType]) {
      monthlyData[monthYear].products[productType] = {
        productType,
        totalOrders: 0,
        backorders: 0,
        backorderRate: 0,
      };
    }

    monthlyData[monthYear].totalOrders += totalQuantity;
    monthlyData[monthYear].products[productType].totalOrders = totalQuantity;
    totalOrderedQty += totalQuantity;
  });

  // Process backorder quantity data
  backorderData.forEach((order) => {
    const orderDate = new Date(order["Sales Order Date"]);
    const monthYear = orderDate.toISOString().substring(0, 7); // YYYY-MM format
    const productType = order["Product Type"] || "Unknown";
    const qtyBackOrdered = parseInt(order["Qty Back Ordered"]) || 0;

    if (monthlyData[monthYear]) {
      monthlyData[monthYear].backorders += qtyBackOrdered;
      totalBackorderedQty += qtyBackOrdered;

      if (!monthlyData[monthYear].products[productType]) {
        monthlyData[monthYear].products[productType] = {
          productType,
          totalOrders: 0,
          backorders: 0,
          backorderRate: 0,
        };
      }
      monthlyData[monthYear].products[productType].backorders += qtyBackOrdered;
    } else {
      console.warn(`No matching month found for backorder: ${monthYear}`);
    }
  });

  // Calculate backorder rates based on quantities
  const processedMonthlyData = Object.values(monthlyData)
      .map((month) => {
        const backorderRate = month.totalOrders > 0 ?
        (month.backorders / month.totalOrders) * 100 :
        0;

        // Calculate product-specific rates based on quantities
        const productData = Object.values(month.products)
            .map((product) => ({
              ...product,
              backorderRate: product.totalOrders > 0 ?
            (product.backorders / product.totalOrders) * 100 :
            0,
            }))
            .sort((a, b) => b.backorderRate - a.backorderRate);

        return {
          monthYear: month.monthYear,
          totalOrders: month.totalOrders,
          backorders: month.backorders,
          backorderRate: parseFloat(backorderRate.toFixed(2)),
          products: productData,
        };
      })
      .sort((a, b) => a.monthYear.localeCompare(b.monthYear));

  const overallBackorderRate = totalOrderedQty > 0 ?
    (totalBackorderedQty / totalOrderedQty) * 100 :
    0;

  return {
    overallBackorderRate: parseFloat(overallBackorderRate.toFixed(2)),
    monthlyData: processedMonthlyData,
    totalBackorders: totalBackorderedQty,
    totalOrders: totalOrderedQty,
    lastUpdated: new Date().toISOString(),
  };
}
