import React, { useState, Suspense, lazy } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Container,
  Tab,
  Tabs,
  styled,
  CircularProgress
} from '@mui/material';
import { MultiSelect } from 'react-multi-select-component';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import PerfectOrderRate from '../components/kpi/PerfectOrderRate';
import SupplierDefectRate from '../components/kpi/SupplierDefectRate';
import SupplierQualityScore from '../components/kpi/SupplierQualityScore';


// Lazy load KPI components
const DIOReport = lazy(() => import('../components/kpi/DIOReport'));
const COGSProfitMargin = lazy(() => import('../components/kpi/COGSProfitMargin'));
const InboundTransportationCost = lazy(() => import('../components/kpi/InboundTransportationCost'));
const OutboundTransportationCost = lazy(() => import('../components/kpi/OutboundTransportationCost'));
const OTDReport = lazy(() => import('../components/kpi/OTDReport'));
const ReturnRateReport = lazy(() => import('../components/kpi/ReturnRateReport'));
const InventoryTurnoverRatio = lazy(() => import('../components/kpi/InventoryTurnoverRatio'));
const OrderFulfillmentCycleTime = lazy(() => import('../components/kpi/OrderFulfillmentCycleTime'));
const TotalUnfulfilledOrders = lazy(() => import('../components/kpi/TotalUnfulfilledOrders'));
const BackorderRate = lazy(() => import('../components/kpi/BackorderRate'));
const ProcurementCycleTimeReport = lazy(() => import('../components/kpi/ProcurementCycleTimeReport'));
const SupplierLeadTimeReport = lazy(() => import('../components/kpi/SupplierLeadTimeReport'));
const ProductLifecycleVisualScore = lazy(() => import('../components/kpi/ProductLifecycleVisualScore'));
const ProductPerformanceDashboard = lazy(() => import('../components/kpi/ProductPerformanceDashboard'));
const TimelineHitScore = lazy(() => import('../components/kpi/TimelineHitScore'));
const OrderAccuracyRate = lazy(() => import('../components/kpi/OrderAccuracyRate'));

const LoadingComponent = () => (
  <Box sx={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '400px'
  }}>
    <CircularProgress />
  </Box>
);

const kpiCategories = {
  'Financial KPIs': [
    'DIO',
    'Cost of Goods Sold (COGS) % (Profit Margin)',
    'Inbound Transportation Cost',
    'Outbound Transportation Cost',
  ],
  'Inventory Management KPIs': [
    'Inventory Turnover Ratio',
    'Backorder Rate',
  ],
  'Order Management KPIs': [
    'Order Fulfillment Cycle Time',
    'Return Rate',
    'Total Unfulfilled Orders',
    'Order Accuracy Rate',
    'Perfect Order Rate'
  ],
  'Supplier Management KPIs': [
    'On-Time Delivery (OTD) Score',
    'Supplier Lead Time',
    'Procurement Cycle Time',
    'Supplier Defect Rate',
    'Supplier Quality Score',
  ],
  'Production and Efficiency KPIs': [],
  'Forecasting and Planning KPIs': [
    'Procurement Cycle Time'
  ],
  'Product Development KPIs': [
    'Timelines Hit Score',
    'Performance of Product Types and Colors',
    'Product Lifecycle Visual Score'
  ]
};

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: `1px solid ${theme.palette.divider}`,
  '& .MuiTabs-indicator': {
    backgroundColor: theme.palette.primary.main,
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  textTransform: 'none',
  minWidth: 0,
  padding: '12px 16px',
  marginRight: theme.spacing(4),
  '&.Mui-selected': {
    color: theme.palette.primary.main,
  },
}));

function KPIDashboard() {
  const [selectedCategory, setSelectedCategory] = useState(Object.keys(kpiCategories)[0]);
  const [selectedKpis, setSelectedKpis] = useState([]);

  const handleCategoryChange = (event, newValue) => {
    setSelectedCategory(newValue);
    setSelectedKpis([]);
  };

  const handleKpiChange = (selected) => {
    setSelectedKpis(selected.map(item => item.value));
  };

  const getGridSize = (totalItems) => {
    if (totalItems === 1) return 12;
    if (totalItems === 2) return 6;
    return 4;
  };

  const displayKpis = selectedKpis.length > 0
    ? selectedKpis
    : kpiCategories[selectedCategory];

  const renderKPIContent = (kpi) => {
    switch (kpi) {
      case 'DIO':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <DIOReport />
          </Suspense>
        );
      case 'Cost of Goods Sold (COGS) % (Profit Margin)':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <COGSProfitMargin />
          </Suspense>
        );
      case 'Inbound Transportation Cost':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <InboundTransportationCost />
          </Suspense>
        );
      case 'Outbound Transportation Cost':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <OutboundTransportationCost />
          </Suspense>
        );
      case 'On-Time Delivery (OTD) Score':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <OTDReport />
          </Suspense>
        );
      case 'Return Rate':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <ReturnRateReport />
          </Suspense>
        );
      case 'Inventory Turnover Ratio':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <InventoryTurnoverRatio />
          </Suspense>
        );
      case 'Order Fulfillment Cycle Time':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <OrderFulfillmentCycleTime />
          </Suspense>
        );
      case 'Total Unfulfilled Orders':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <TotalUnfulfilledOrders />
          </Suspense>
        );
      case 'Backorder Rate':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <BackorderRate />
          </Suspense>
        );
      case 'Procurement Cycle Time':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <ProcurementCycleTimeReport />
          </Suspense>
        );
      case 'Supplier Lead Time':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <SupplierLeadTimeReport />
          </Suspense>
        );
      case 'Product Lifecycle Visual Score':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <ProductLifecycleVisualScore />
          </Suspense>
        );
      case 'Performance of Product Types and Colors':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <ProductPerformanceDashboard />
          </Suspense>
        );
      case 'Timelines Hit Score':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <TimelineHitScore />
          </Suspense>
        );
      case 'Order Accuracy Rate':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <OrderAccuracyRate />
          </Suspense>
        );
      case 'Perfect Order Rate':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <PerfectOrderRate />
          </Suspense>
        );
      case 'Supplier Defect Rate':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <SupplierDefectRate />
          </Suspense>
        );
      case 'Supplier Quality Score':
        return (
          <Suspense fallback={<LoadingComponent />}>
            <SupplierQualityScore />
          </Suspense>
        );
      default:
        return (
          <Typography variant="body2" color="text.secondary">
            Data visualization would go here
          </Typography>
        );
    }
  };

  return (
    <Container
      maxWidth={false}
      sx={{
        bgcolor: 'background.default',
        minHeight: '100vh',
        p: 0,
      }}
    >
      <Box sx={{ width: '100%', height: '100vh', display: 'flex', flexDirection: 'column' }}>
        <StyledTabs
          value={selectedCategory}
          onChange={handleCategoryChange}
          variant="scrollable"
          scrollButtons="auto"
          aria-label="KPI category tabs"
        >
          {Object.keys(kpiCategories).map((category) => (
            <StyledTab
              label={category}
              value={category}
              key={category}
            />
          ))}
        </StyledTabs>

        <Box sx={{
          px: 2,
          py: 1,
          bgcolor: 'grey.100',
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}>
          <FilterAltIcon color="primary" />
          <Box sx={{ width: '300px' }}>
            <MultiSelect
              options={kpiCategories[selectedCategory].map(kpi => ({
                label: kpi,
                value: kpi
              }))}
              value={selectedKpis.map(kpi => ({ label: kpi, value: kpi }))}
              onChange={handleKpiChange}
              labelledBy="Filter KPIs"
              overrideStrings={{
                selectSomeItems: 'Filter KPIs...',
                allItemsAreSelected: 'All KPIs selected',
                selectAll: 'Select All',
                search: 'Search',
              }}
            />
          </Box>
        </Box>

        <Box sx={{
          p: 2,
          flexGrow: 1,
          overflow: 'auto',
          bgcolor: 'grey.100'
        }}>
          <Grid container spacing={2}>
            {displayKpis.map((kpi) => (
              <Grid
                item
                xs={12}
                sm={getGridSize(displayKpis.length) === 6 ? 6 : 12}
                md={getGridSize(displayKpis.length)}
                key={kpi}
              >
                <Card sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  boxShadow: (theme) => theme.shadows[2]
                }}>
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Typography
                      variant="h6"
                      gutterBottom
                      sx={{
                        color: 'primary.main',
                        fontSize: { xs: '1rem', sm: '1.25rem' }
                      }}
                    >
                      {kpi}
                    </Typography>
                    {renderKPIContent(kpi)}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Box>
    </Container>
  );
}

export default KPIDashboard;