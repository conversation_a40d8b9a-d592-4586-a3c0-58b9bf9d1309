import React, {useState, useEffect} from 'react';
import {Table, Input, Button, message, Space, Select, DatePicker, Layout} from 'antd';
import {doc, getDoc} from 'firebase/firestore';
import {db, api} from './firebase';
import {Content, Header} from 'antd/es/layout/layout';

const OpsForecast = ({userObj}) => {
  const [lifeStatusOptions, setLifeStatusOptions] = useState([]);
  const [lifeStatuses, setLifeStatuses] = useState({});
  const [productTypeOptions, setProductTypeOptions] = useState([]);
  const [productTypes, setProductTypes] = useState({});
  const [brandOptions, setBrandOptions] = useState([]);
  const [brands, setBrands] = useState({});
  const [locations, setLocations] = useState([[{label: 'HQ', value: 'HQ'}]]);
  const [selectedLocations, setSelectedLocations] = useState([{label: 'HQ', value: 'HQ'}]);
  const [itemSearch, setItemSearch] = useState('');
  const [forecastData, setForecastData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedMonth, setSelectedMonth] = useState(null);
  const [newData, setNewData] = useState([]);
  const [messageApi, contextHolder] = message.useMessage();
  // console.log('contextHolder', contextHolder, setLocations);

  const handleSave = async () => {
    // console.log('saving data', newData);
    const updateQuery = `
    UPDATE \`hj-reporting.sales.forecasts\`
    SET forecast = CASE ${newData.map((x) => `WHEN uniqueKey = '${x.uniqueKey}' THEN ${x.forecast}`).join(' ')} END
    WHERE uniqueKey ${newData.length > 1 ? `IN (${newData.map((x) => `'${x.uniqueKey}'`).join(', ')})` : `= '${newData[0].uniqueKey}'`}
  ;`;
    try {
      // console.log('updateQuery', updateQuery);
      const bigQueryResult = await api.runQueryOnCall({options: {query: updateQuery}});
      // console.log('bigQueryResult', bigQueryResult);
      messageApi.open({
        type: 'success',
        content: 'Data saved successfully!',
        duration: 2,
      });
      // message.success('Data saved successfully!', { duration: 3 });
      setNewData([]);
    } catch (error) {
      message.error('Failed to save data!', error.message);
    }
  };

  const handleInputChange = (data) => {
    // console.log('data', data);
    const {val, uniqueKey, org} = data;
    if (parseFloat(val ? val : 0) === parseFloat(org ? org : 0)) {
      setNewData(newData.filter((x) => x.uniqueKey !== uniqueKey));
      return;
    }
    const currentChange = newData.find((x) => x.uniqueKey === uniqueKey);
    if (currentChange) {
      setNewData(newData.map((x) => x.uniqueKey === uniqueKey ? {...x, forecast: val} : x));
    } else {
      setNewData([...newData, {uniqueKey, forecast: val}]);
    }
  };
  const getForecastDays = () => {
    if (!selectedMonth) return [];
    const daysInMonth = new Date(selectedMonth.year(), selectedMonth.month() + 1, 0).getDate();
    return Array.from({length: daysInMonth}, (_, i) => i + 1);
  };
  const days = getForecastDays();

  // const refreshItems = async () => {
  //   api.getNsInventoryOnCall();
  // };
  useEffect(() => {
    const fetchLists = async () => {
      try {
        const lifeStatusRef = doc(db, 'lists', 'lifeStatuses');
        const productTypeRef = doc(db, 'lists', 'productTypes');
        const brandRef = doc(db, 'lists', 'brands');
        // const locationRef = doc(db, 'lists', 'locations');
        const lifeStatusSnap = await getDoc(lifeStatusRef);
        const productTypeSnap = await getDoc(productTypeRef);
        const brandSnap = await getDoc(brandRef);
        // const locationSnap = await getDoc(locationRef);

        // console.log('lifeStatusSnap', lifeStatusSnap.data());
        if (lifeStatusSnap.exists() && lifeStatusSnap.data() && lifeStatusSnap.data().data) {
          setLifeStatusOptions(lifeStatusSnap.data().data.items.map((x) => ({label: x, value: x})));
        }
        if (productTypeSnap.exists() && productTypeSnap.data() && productTypeSnap.data().data) {
          setProductTypeOptions(productTypeSnap.data().data.items.map((x) => ({label: x, value: x})));
        }
        if (brandSnap.exists() && brandSnap.data() && brandSnap.data().data) {
          setBrandOptions(brandSnap.data().data.items.map((x) => ({label: x, value: x})));
        }

        // if (locationSnap.exists() && locationSnap.data().items) {
        //   console.log('locationSnap', locationSnap.data());
        //   setLocations(locationSnap.data().items.map((x) => ({label: x.name, value: x.id})));
        // }
      } catch (error) {
        console.error('Error', error.message);
      }
    };
    fetchLists();
  }, []);

  useEffect(() => {
    const fetchData = async (itemData) => {
      setLoading(true);
      if (!selectedMonth || !selectedMonth.month) return;
      const q = `SELECT 
                  f.date,
                  f.salesChannel,
                  f.location,
                  f.uniqueKey,
                  i.sku,
                  i.upc,
                  i.productType,
                  i.description,
                  i.lifeStatus, 
                  f.forecast,
                  f.calculatedForecast
                FROM \`hj-reporting.items.variants\` AS i
                LEFT JOIN \`hj-reporting.forecasts.opsForecast\` AS f ON f.sku = i.sku
                WHERE 
                  f.date >= '${selectedMonth.format('YYYY-MM')}-01'
                  AND f.date <= '${selectedMonth.format('YYYY-MM')}-30'
                  ${lifeStatuses.length > 0 ? `AND i.lifeStatus IN (${lifeStatuses.map((x) => `'${x}'`).join(',')})` : ''}
                  ${productTypes.length > 0 ? `AND i.productType IN (${productTypes.map((x) => `'${x}'`).join(',')})` : ''}
                  ${selectedLocations.length > 0 ? `AND f.location IN (${selectedLocations.map((x) => `'${x}'`).join(',')})` : ''}
                  ${brands.length > 0 ? `AND i.brand IN (${brands.map((x) => `'${x}'`).join(',')})` : ''}
                  ${itemSearch ? `AND i.sku LIKE '%${itemSearch}%'` : ''}
                ORDER BY f.date, i.sku
      `;
      // console.log('q', q);
      try {
        const queryData = await api.runQueryOnCall({options: {query: q}});
        if (!queryData || !queryData.length) {
          setForecastData([]);
          setLoading(false);
          return;
        };
        // console.log('queryData', queryData);
        setForecastData(queryData.data);
        // console.log('forecastData', forecastData);
      } catch (error) {
        console.error('Error', error.message);
      } finally {
        setLoading(false);
      }
    };
    // console.log('fetchingData');
    fetchData();
  }, [itemSearch, lifeStatuses, lifeStatusOptions, productTypes, productTypeOptions, selectedMonth, selectedLocations]);

  const data = [];
  const skus = [...new Set(forecastData.map((x) => x.sku))];
  for (const sku of skus) {
    const forecastDataForSku = forecastData.filter((x) => x.sku === sku);
    const item = forecastDataForSku[0];
    const forecastDays = [];
    if (forecastDataForSku && forecastDataForSku.length > 0) {
      for (const day of days) {
        const dt = `${selectedMonth.format('YYYY-MM')}-${day.toString().padStart(2, '0')}`;
        const forecast = forecastDataForSku.find((x) => x.date === dt);
        // if (forecast) {
        //   debugger;
        //   console.log('forecast', forecast);
        // }
        forecastDays.push({
          id: forecast ? forecast.id : null,
          date: dt,
          forecast: forecast ? forecast.forecast : 0,
          salesChannel: forecast ? forecast.salesChannel : null,
          location: forecast ? forecast.location : null,
          uniqueKey: forecast ? forecast.uniqueKey : null,
        });
      }
    }
    const itemRow = {
      upc: item.upc,
      sku: item.sku,
      productType: item.productType,
      desc: item.description,
      lifeStatus: item.lifeStatus,
      forecastDays,
    };
    data.push(itemRow);
  }


  const columns = [
    {
      title: 'UPC',
      dataIndex: 'id',
      key: 'id',

      render: (text, record) => {
        return <>{text}<br />{record.sku}</>;
      },
    },
    {
      title: 'Product',
      dataIndex: 'desc',
      key: 'desc',
      render: (text, record) => {
        return <span>{record.productType}<br />{record.desc}</span>;
      },
    },
    {
      title: 'Life Status',
      dataIndex: 'lifeStatus',
      key: 'lifeStatus',
    },
  ];
  if (data.length > 0) {
    // console.log('days', days);
    for (const d of days) {
      columns.push({
        title: `${selectedMonth.format('YYYY-MM')}-${d.toString().padStart(2, '0')}`,
        key: d,
        dataIndex: d,
        render: (text, record) => {
          // debugger;
          // if (record.forecastDays.length > 0) {
          //   debugger;
          // }
          const fDay = record.forecastDays[d - 1];
          return (
            <>
              <Input
                style={{minWidth: '50px', borderColor: fDay?.uniqueKey && newData.find((x) => x.uniqueKey === fDay.uniqueKey) ? 'red' : null}}
                defaultValue={fDay ? fDay.forecast : 0}
                onChange={(e) => handleInputChange({val: e.target.value, uniqueKey: fDay?.uniqueKey, org: fDay?.forecast})}
              />
              <br />
              <span style={{fontSize: '5px'}}>{fDay?.uniqueKey}</span>
            </>
          );
        },
      });
    }
  }
  // console.log('data', data, columns);
  // debugger;
  return (
    <Layout>
      <Header>
        <Space >
          <Input
            placeholder="Search Items"
            style={{width: 200}}
            allowClear
            value={itemSearch}
            onChange={(e) => setItemSearch(e.target.value.trim())}
          />
          <Select
            placeholder="Brand"
            style={{width: 200}}
            onChange={(e) => {
              // console.log(e);
              setBrands(e);
            }}
            allowClear
            mode='multiple'
            options={brandOptions}
            showSearch
          />
          <Select
            placeholder="Select an Life Status"
            style={{width: 200}}
            onChange={(e) => {
              // console.log(e);
              setLifeStatuses(e);
            }}
            allowClear
            mode='multiple'
            options={lifeStatusOptions}
            showSearch
          />
          <Select
            placeholder="Select a Product Type"
            style={{width: 200}}
            onChange={(e) => setProductTypes(e)}
            allowClear
            options={productTypeOptions}
            showSearch
            mode='multiple'
          />
          <Select
            placeholder="Select a Location"
            style={{width: 200}}
            onChange={(e) => setSelectedLocations(e)}
            allowClear
            options={locations}
            showSearch
            mode='multiple'
          />
          <DatePicker onChange={(e) => setSelectedMonth(e)} picker="month" />
          <Button onClick={refreshItems}>Refresh</Button>
          {newData.length > 0 && <Button type="primary" onClick={handleSave}>Save</Button>}
        </Space>
      </Header>
      <Content>
        <Table
          dataSource={data}
          columns={columns}
          rowKey="sku"
          loading={loading}
          pagination={false}
        />
      </Content>
    </Layout>

  );
};

export default OpsForecast;
