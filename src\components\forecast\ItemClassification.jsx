// ItemClassification.jsx

import React, { useState, useEffect, useCallback } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { Layout, message, Button, Space, Switch, Spin, Tooltip, Row, Col } from 'antd';
import { Content } from 'antd/es/layout/layout';
import { api } from '../../pages/firebase';

import { itemClassificationColors, lifeStatusColors, PRODUCT_SPECIFICATION_COLORS } from '../../constants';
import ImageCell from '../ImageCell';
import { ReloadOutlined, SaveOutlined, UndoOutlined, EyeOutlined } from '@ant-design/icons';
const ItemClassification = () => {
  const [items, setItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [manualOverrides, setManualOverrides] = useState({});
  const [showMismatchOnly, setShowMismatchOnly] = useState(false);
  const [gridApi, setGridApi] = useState(null);
  const [pendingChanges, setPendingChanges] = useState({});

  // Function to calculate stock levels based on classification
  const calculateStockLevels = useCallback((data, classification) => {
    const { total_demand_90_days, total_demand_60_days, total_demand_30_days } = data;
    
    let safetyStock = 0;
    
    switch (classification) {
      case 'A':
        safetyStock = Math.round(total_demand_90_days || 0);
        break;
      case 'B':
        safetyStock = Math.round(total_demand_60_days || 0);
        break;
      case 'C':
        safetyStock = Math.round(total_demand_30_days || 0);
        break;
      default:
        safetyStock = 0;
        break;
    }
    
    const minStock = Math.round(safetyStock * 0.75);
    const maxStock = Math.round(safetyStock * 1.25);
    
    return { safety_stock: safetyStock, min_stock: minStock, max_stock: maxStock };
  }, []);

  // Function to load manual overrides from BigQuery
  const loadManualOverrides = useCallback(async () => {
    try {
      const response = await api.bigQueryRunQueryOnCall({
        options: {
          query: `
            SELECT 
              upc,
              classification,
              safety_stock,
              min_stock,
              max_stock,
              locked,
              modified_at
            FROM 
              \`hj-reporting.forecast.item_classification\`
            WHERE 
              modified_at IS NOT NULL
          `
        }
      });
      
      const overrides = {};
      response.data?.forEach(row => {
        overrides[row.upc] = {
          classification: row.classification,
          safety_stock: row.safety_stock,
          min_stock: row.min_stock,
          max_stock: row.max_stock,
          locked: row.locked,
          modified_at: row.modified_at
        };
      });
      
      setManualOverrides(overrides);
    } catch (error) {
      console.error('Error loading manual overrides:', error);
    }
  }, []);

  // Function to save manual overrides to BigQuery
  const saveManualOverrides = useCallback(async (upc, overrideData) => {
    setSaving(true);
    try {
      await api.bigQueryRunQueryOnCall({
        options: {
          query: `
            MERGE \`hj-reporting.forecast.item_classification\` AS target
            USING (
              SELECT 
                '${upc}' as upc,
                ${overrideData.percent_revenue} as percent_revenue,
                ${overrideData.percent_total_revenue} as percent_total_revenue,
                '${overrideData.classification}' as classification,
                ${overrideData.safety_stock} as safety_stock,
                ${overrideData.min_stock} as min_stock,
                ${overrideData.max_stock} as max_stock,
                CURRENT_TIMESTAMP() as modified_at,
                ${overrideData.locked || false} as locked,
                '${overrideData.calculated_classification}' as calculated_classification
            ) AS source
            ON target.upc = source.upc
            WHEN MATCHED THEN
              UPDATE SET
                percent_revenue = source.percent_revenue,
                percent_total_revenue = source.percent_total_revenue,
                classification = source.classification,
                safety_stock = source.safety_stock,
                min_stock = source.min_stock,
                max_stock = source.max_stock,
                modified_at = source.modified_at,
                locked = source.locked,
                calculated_classification = source.calculated_classification
            WHEN NOT MATCHED THEN
              INSERT (upc, percent_revenue, percent_total_revenue, classification, safety_stock, min_stock, max_stock, modified_at, locked, calculated_classification)
              VALUES (source.upc, source.percent_revenue, source.percent_total_revenue, source.classification, source.safety_stock, source.min_stock, source.max_stock, source.modified_at, source.locked, source.calculated_classification)
          `
        }
      });
      
      message.success('Classification saved successfully');
      
      // Update local state
      setManualOverrides(prev => ({
        ...prev,
        [upc]: {
          ...overrideData,
          modified_at: new Date().toISOString()
        }
      }));
    } catch (error) {
      console.error('Error saving manual override:', error);
      message.error('Failed to save classification changes');
    } finally {
      setSaving(false);
    }
  }, []);

  // Function to load manual overrides and return them (for use in save process)
  const loadManualOverridesAndReturn = useCallback(async () => {
    try {
      const response = await api.bigQueryRunQueryOnCall({
        options: {
          query: `
            SELECT 
              upc,
              classification,
              safety_stock,
              min_stock,
              max_stock,
              locked,
              modified_at
            FROM 
              \`hj-reporting.forecast.item_classification\`
            WHERE 
              modified_at IS NOT NULL
          `
        }
      });
      
      const overrides = {};
      response.data?.forEach(row => {
        overrides[row.upc] = {
          classification: row.classification,
          safety_stock: row.safety_stock,
          min_stock: row.min_stock,
          max_stock: row.max_stock,
          locked: row.locked,
          modified_at: row.modified_at
        };
      });
      
      // Update state as well
      setManualOverrides(overrides);
      
      // Return the overrides for immediate use
      return overrides;
    } catch (error) {
      console.error('Error loading manual overrides:', error);
      return {};
    }
  }, []);

  // Function to recalculate classifications with provided overrides
  const recalculateClassificationsWithOverrides = useCallback(async (overrides) => {
    try {
      // Re-fetch the data to get fresh calculations
      const response = await api.bigQueryRunQueryOnCall({
        options: {
          query: `
            WITH
              items AS (
              SELECT
                producttype,
                color,
                size,
                baseprice,
                launchdate,
                enddate,
                productdivision,
                productcategory,
                productform,
                productspecification,
                lifestatus,
                image,
                upc
              FROM
                \`hj-reporting.items.items_netsuite\`
              WHERE
                upc IS NOT NULL
                AND upc != 'TRUE'
              ),
              demand AS (
              SELECT
                upc,
                forecast_node,
                DATE_TRUNC(date, MONTH) AS demand_month,
                SUM(qty) AS monthly_demand
              FROM
                \`hj-reporting.forecast.demand_plan\`
              WHERE
                date BETWEEN CURRENT_DATE()
                AND DATE_ADD(CURRENT_DATE(), INTERVAL 4 MONTH)
              GROUP BY
                upc,
                forecast_node,
                demand_month
              ),
              forecast_nodes AS (
              SELECT
                code,
                COALESCE(CAST(msrp_discount AS FLOAT64), 0) / 100 AS discount_factor
              FROM
                \`hj-reporting.forecast.forecast_nodes\`
              ),
              pivoted AS (
              SELECT
                i.producttype,
                i.color,
                i.size,
                i.baseprice,
                i.launchdate,
                i.enddate,
                i.productdivision,
                i.productcategory,
                i.productform,
                i.productspecification,
                i.lifestatus,
                i.image,
                d.upc,
                d.forecast_node,
                COALESCE(fn.discount_factor, 1.0) AS discount_factor,
                SUM(CASE
                    WHEN d.demand_month = DATE_TRUNC(CURRENT_DATE(), MONTH) THEN d.monthly_demand
                    ELSE 0
                END) AS month_1_demand,
                SUM(CASE
                    WHEN d.demand_month = DATE_TRUNC(DATE_ADD(CURRENT_DATE(), INTERVAL 1 MONTH), MONTH) THEN d.monthly_demand
                    ELSE 0
                END) AS month_2_demand,
                SUM(CASE
                    WHEN d.demand_month = DATE_TRUNC(DATE_ADD(CURRENT_DATE(), INTERVAL 2 MONTH), MONTH) THEN d.monthly_demand
                    ELSE 0
                END) AS month_3_demand,
                SUM(CASE
                    WHEN d.demand_month = DATE_TRUNC(DATE_ADD(CURRENT_DATE(), INTERVAL 3 MONTH), MONTH) THEN d.monthly_demand
                    ELSE 0
                END) AS month_4_demand
              FROM
                demand d
              JOIN
                items i
              ON
                d.upc = i.upc
              LEFT JOIN
                forecast_nodes fn
              ON
                d.forecast_node = fn.code
              GROUP BY
                d.upc,
                d.forecast_node,
                fn.discount_factor,
                i.producttype,
                i.color,
                i.size,
                i.baseprice,
                i.launchdate,
                i.enddate,
                i.productdivision,
                i.productcategory,
                i.productform,
                i.productspecification,
                i.lifestatus,
                i.image
              ),
              aggregated AS (
              SELECT
                producttype,
                color,
                size,
                baseprice,
                launchdate,
                enddate,
                productdivision,
                productcategory,
                productform,
                productspecification,
                lifestatus,
                upc,
                image,
                SUM(month_2_demand) AS month_2_demand,
                SUM(month_3_demand) AS month_3_demand,
                SUM(month_4_demand) AS month_4_demand,
                SUM(month_2_demand + month_3_demand + month_4_demand) AS total_demand,
                SUM(
                  (month_2_demand + month_3_demand + month_4_demand) * 
                  baseprice * 
                  discount_factor
                ) AS total_forecasted_revenue
              FROM
                pivoted
              GROUP BY
                producttype,
                color,
                size,
                baseprice,
                launchdate,
                enddate,
                productdivision,
                productcategory,
                productform,
                productspecification,
                lifestatus,
                upc,
                image
              ),
              final AS (
              SELECT
                *
              FROM
                aggregated
              ),
              scored AS (
              SELECT
                *,
                ROUND(CAST(100*total_demand AS FLOAT64) / SUM(total_demand) OVER (), 2) AS percent_demand,
                ROUND(CAST(100*total_forecasted_revenue AS FLOAT64) / SUM(total_forecasted_revenue) OVER (), 2) AS percent_revenue
              FROM
                final
              ),
                              ranked AS (
                SELECT
                  *,
                  CASE
                    WHEN total_forecasted_revenue IS NULL OR total_forecasted_revenue = 0 
                         OR lifestatus IN ('Phasing Out', 'Obsolete')
                         OR productform IN ('Limited Edition', 'Exclusive') THEN NULL
                    ELSE
                      CAST(ROW_NUMBER() OVER (
                        PARTITION BY 
                          CASE 
                            WHEN total_forecasted_revenue IS NOT NULL AND total_forecasted_revenue > 0 
                                 AND lifestatus NOT IN ('Phasing Out', 'Obsolete')
                                 AND productform NOT IN ('Limited Edition', 'Exclusive') 
                            THEN 1 
                            ELSE 0 
                          END
                        ORDER BY total_forecasted_revenue DESC
                      ) AS FLOAT64) / 
                      CAST(COUNT(*) OVER (
                        PARTITION BY 
                          CASE 
                            WHEN total_forecasted_revenue IS NOT NULL AND total_forecasted_revenue > 0 
                                 AND lifestatus NOT IN ('Phasing Out', 'Obsolete')
                                 AND productform NOT IN ('Limited Edition', 'Exclusive') 
                            THEN 1 
                            ELSE 0 
                          END
                      ) AS FLOAT64)
                  END AS percentile_rank
                FROM
                  scored
                ),
                classified AS (
                SELECT
                  *,
                  CASE
                    WHEN total_forecasted_revenue IS NULL OR total_forecasted_revenue = 0 OR lifestatus IN ('Phasing Out', 'Obsolete') OR productform IN ('Limited Edition', 'Exclusive') THEN 'No Class'
                    WHEN ROUND(SUM(total_forecasted_revenue) OVER (ORDER BY total_forecasted_revenue DESC) / SUM(total_forecasted_revenue) OVER () * 100, 2) <= 80 THEN 'A'
                    WHEN ROUND(SUM(total_forecasted_revenue) OVER (ORDER BY total_forecasted_revenue DESC) / SUM(total_forecasted_revenue) OVER () * 100, 2) <= 90 THEN 'B'
                    ELSE 'C'
                  END AS classification
                FROM
                  ranked
                )
            SELECT
              image,
              producttype,
              color,
              upc,
              size,
              baseprice,
              launchdate,
              enddate,
              productdivision,
              productcategory,
              productform,
              productspecification,
              lifestatus,
              total_forecasted_revenue,
              month_2_demand,
              month_3_demand,
              month_4_demand,
              percent_revenue,
              ROUND(SUM(percent_revenue) OVER (ORDER BY percent_revenue DESC), 2) AS percent_total_revenue,
              classification,
              CASE
                WHEN total_demand IS NULL OR total_demand = 0 THEN 0
                WHEN classification = 'A' THEN ROUND(total_demand / 3 * 3, 0)
                WHEN classification = 'B' THEN ROUND(total_demand / 3 * 2, 0)
                WHEN classification = 'C' THEN ROUND(total_demand / 3 * 1, 0)
                ELSE 0
              END AS safety_stock,
              CASE
                WHEN total_demand IS NULL OR total_demand = 0 THEN 0
                WHEN classification = 'A' THEN ROUND(total_demand / 3 * 3 * 0.75, 0)
                WHEN classification = 'B' THEN ROUND(total_demand / 3 * 2 * 0.75, 0)
                WHEN classification = 'C' THEN ROUND(total_demand / 3 * 1 * 0.75, 0)
                ELSE 0
              END AS min_stock,
              CASE
                WHEN total_demand IS NULL OR total_demand = 0 THEN 0
                WHEN classification = 'A' THEN ROUND(total_demand / 3 * 3 * 1.25, 0)
                WHEN classification = 'B' THEN ROUND(total_demand / 3 * 2 * 1.25, 0)
                WHEN classification = 'C' THEN ROUND(total_demand / 3 * 1 * 1.25, 0)
                ELSE 0
              END AS max_stock
            FROM
              classified
            ORDER BY
              total_forecasted_revenue DESC
          `
        }
      });
      
      const formattedData = response.data.map((row, i) => {
        const calculatedClassification = row.classification;
        const upc = row.upc;
        const override = overrides[upc];
        
        // If there's a manual override, preserve it; otherwise use calculated values
        if (override) {
          return {
            id: i,
            ...row,
            calculated_classification: calculatedClassification,
            classification: override.classification,
            safety_stock: override.safety_stock,
            min_stock: override.min_stock,
            max_stock: override.max_stock,
            is_manual_override: true,
            modified_at: override.modified_at,
            total_demand: row.month_2_demand + row.month_3_demand + row.month_4_demand,
            launchdate: row.launchdate && row.launchdate.value ? new Date(row.launchdate.value) : null,
            enddate: row.enddate && row.enddate.value ? new Date(row.enddate.value) : null,
          };
        } else {
          return {
            id: i,
            ...row,
            calculated_classification: calculatedClassification,
            is_manual_override: false,
            total_demand: row.month_2_demand + row.month_3_demand + row.month_4_demand,
            launchdate: row.launchdate && row.launchdate.value ? new Date(row.launchdate.value) : null,
            enddate: row.enddate && row.enddate.value ? new Date(row.enddate.value) : null,
          };
        }
      });
      
      setItems(formattedData || []);
    } catch (error) {
      console.error('Error recalculating classifications:', error);
      message.error('Failed to recalculate classifications');
    }
  }, []);

  // Function to save all pending changes to BigQuery
  const saveAllPendingChanges = useCallback(async () => {
    if (Object.keys(pendingChanges).length === 0) {
      message.warning('No pending changes to save');
      return;
    }

    setSaving(true);
    setLoading(true);
    try {
      // Build a single query to save all changes
      const mergeStatements = Object.entries(pendingChanges).map(([upc, overrideData]) => {
        return `
          SELECT 
            '${upc}' as upc,
            ${overrideData.percent_revenue} as percent_revenue,
            ${overrideData.percent_total_revenue} as percent_total_revenue,
            '${overrideData.classification}' as classification,
            ${overrideData.safety_stock} as safety_stock,
            ${overrideData.min_stock} as min_stock,
            ${overrideData.max_stock} as max_stock,
            CURRENT_TIMESTAMP() as modified_at,
            ${overrideData.locked || false} as locked,
            '${overrideData.calculated_classification}' as calculated_classification
        `;
      }).join(' UNION ALL ');

      await api.bigQueryRunQueryOnCall({
        options: {
          query: `
            MERGE \`hj-reporting.forecast.item_classification\` AS target
            USING (
              ${mergeStatements}
            ) AS source
            ON target.upc = source.upc
            WHEN MATCHED THEN
              UPDATE SET
                percent_revenue = source.percent_revenue,
                percent_total_revenue = source.percent_total_revenue,
                classification = source.classification,
                safety_stock = source.safety_stock,
                min_stock = source.min_stock,
                max_stock = source.max_stock,
                modified_at = source.modified_at,
                locked = source.locked,
                calculated_classification = source.calculated_classification
            WHEN NOT MATCHED THEN
              INSERT (upc, percent_revenue, percent_total_revenue, classification, safety_stock, min_stock, max_stock, modified_at, locked, calculated_classification)
              VALUES (source.upc, source.percent_revenue, source.percent_total_revenue, source.classification, source.safety_stock, source.min_stock, source.max_stock, source.modified_at, source.locked, source.calculated_classification)
          `
        }
      });
      
      message.success(`${Object.keys(pendingChanges).length} classification changes saved successfully`);
      
      // Clear pending changes
      setPendingChanges({});
      
      // Fetch fresh data from BigQuery to ensure consistency
      const freshOverrides = await loadManualOverridesAndReturn();
      await recalculateClassificationsWithOverrides(freshOverrides);
    } catch (error) {
      console.error('Error saving pending changes:', error);
      message.error('Failed to save classification changes');
    } finally {
      setSaving(false);
      setLoading(false);
    }
  }, [pendingChanges]);

  // Function to reset pending changes
  const resetPendingChanges = useCallback(() => {
    if (Object.keys(pendingChanges).length === 0) {
      message.warning('No pending changes to reset');
      return;
    }

    // Clear pending changes
    setPendingChanges({});
    
    // Update items to remove pending status and revert to saved/calculated values
    const updatedItems = items.map(item => {
      if (pendingChanges[item.upc]) {
        // If this item had pending changes, revert to saved override or calculated values
        const savedOverride = manualOverrides[item.upc];
        if (savedOverride) {
          // Revert to saved override
          return {
            ...item,
            classification: savedOverride.classification,
            safety_stock: savedOverride.safety_stock,
            min_stock: savedOverride.min_stock,
            max_stock: savedOverride.max_stock,
            is_manual_override: true,
            has_pending_changes: false
          };
        } else {
          // Revert to calculated values
          return {
            ...item,
            classification: item.calculated_classification,
            safety_stock: item.calculated_classification === 'A' ? Math.round(item.total_demand / 3 * 3) : 
                          item.calculated_classification === 'B' ? Math.round(item.total_demand / 3 * 2) : 
                          item.calculated_classification === 'C' ? Math.round(item.total_demand / 3 * 1) : 0,
            min_stock: item.calculated_classification === 'A' ? Math.round(item.total_demand / 3 * 3 * 0.75) : 
                       item.calculated_classification === 'B' ? Math.round(item.total_demand / 3 * 2 * 0.75) : 
                       item.calculated_classification === 'C' ? Math.round(item.total_demand / 3 * 1 * 0.75) : 0,
            max_stock: item.calculated_classification === 'A' ? Math.round(item.total_demand / 3 * 3 * 1.25) : 
                       item.calculated_classification === 'B' ? Math.round(item.total_demand / 3 * 2 * 1.25) : 
                       item.calculated_classification === 'C' ? Math.round(item.total_demand / 3 * 1 * 1.25) : 0,
            is_manual_override: false,
            has_pending_changes: false
          };
        }
      }
      return {
        ...item,
        has_pending_changes: false
      };
    });
    
    setItems(updatedItems);
    message.success(`Reset ${Object.keys(pendingChanges).length} pending changes`);
  }, [pendingChanges, items, manualOverrides]);

  // Function to recalculate all classifications
  const recalculateClassifications = useCallback(async () => {
    setLoading(true);
    try {
      // Use the Cloud Function to recalculate item classifications
      const response = await api.recalculateItemClassificationOnCall({});
      
      if (response.success) {
        message.success(`Classifications recalculated successfully for ${response.count} items`);
        
        // Reload the data to show the updated classifications
        const freshOverrides = await loadManualOverridesAndReturn();
        await recalculateClassificationsWithOverrides(freshOverrides);
      } else {
        throw new Error(response.message || 'Failed to recalculate classifications');
      }
    } catch (error) {
      console.error('Error recalculating classifications:', error);
      message.error('Failed to recalculate classifications: ' + (error.message || error));
    } finally {
      setLoading(false);
    }
  }, [loadManualOverridesAndReturn, recalculateClassificationsWithOverrides]);

  useEffect(() => {
    const fetchItems = async () => {
      setLoading(true);
      try {
        const response = await api.bigQueryRunQueryOnCall({
          options: {
            query: `
              WITH
                items AS (
                SELECT
                  producttype,
                  color,
                  size,
                  baseprice,
                  launchdate,
                  enddate,
                  productdivision,
                  productcategory,
                  productform,
                  productspecification,
                  lifestatus,
                  image,
                  upc
                FROM
                  \`hj-reporting.items.items_netsuite\`
                WHERE
                  upc IS NOT NULL
                  AND upc != 'TRUE'
                ),
                demand AS (
                SELECT
                  upc,
                  forecast_node,
                  date,
                  qty
                FROM
                  \`hj-reporting.forecast.demand_plan\`
                WHERE
                  date BETWEEN CURRENT_DATE() AND DATE_ADD(CURRENT_DATE(), INTERVAL 90 DAY)
                ),
                forecast_nodes AS (
                SELECT
                  code,
                  COALESCE(CAST(msrp_discount AS FLOAT64), 0) / 100 AS discount_factor
                FROM
                  \`hj-reporting.forecast.forecast_nodes\`
                ),
                aggregated_demand AS (
                  SELECT
                    d.upc,
                    i.producttype, i.color, i.size, i.baseprice, i.launchdate, i.enddate,
                    i.productdivision, i.productcategory, i.productform, i.productspecification,
                    i.lifestatus, i.image,
                    SUM(d.qty) AS total_demand_90_days,
                    SUM(IF(d.date <= DATE_ADD(CURRENT_DATE(), INTERVAL 60 DAY), d.qty, 0)) AS total_demand_60_days,
                    SUM(IF(d.date <= DATE_ADD(CURRENT_DATE(), INTERVAL 30 DAY), d.qty, 0)) AS total_demand_30_days,
                    SUM(d.qty * i.baseprice * COALESCE(fn.discount_factor, 1.0)) AS total_forecasted_revenue
                  FROM demand d
                  JOIN items i ON d.upc = i.upc
                  LEFT JOIN forecast_nodes fn ON d.forecast_node = fn.code
                  GROUP BY
                    d.upc, i.producttype, i.color, i.size, i.baseprice, i.launchdate, i.enddate,
                    i.productdivision, i.productcategory, i.productform, i.productspecification,
                    i.lifestatus, i.image
                ),
                final AS (
                  SELECT * FROM aggregated_demand
                ),
                scored AS (
                SELECT
                  *,
                  ROUND(CAST(100*total_demand_90_days AS FLOAT64) / SUM(total_demand_90_days) OVER (), 2) AS percent_demand,
                  ROUND(CAST(100*total_forecasted_revenue AS FLOAT64) / SUM(total_forecasted_revenue) OVER (), 2) AS percent_revenue
                FROM
                  final
                ),
                ranked AS (
                SELECT
                  *,
                  CASE
                    WHEN total_forecasted_revenue IS NULL OR total_forecasted_revenue = 0 
                         OR lifestatus IN ('Phasing Out', 'Obsolete')
                         OR productform IN ('Limited Edition', 'Exclusive') THEN NULL
                    ELSE
                      CAST(ROW_NUMBER() OVER (
                        PARTITION BY 
                          CASE 
                            WHEN total_forecasted_revenue IS NOT NULL AND total_forecasted_revenue > 0 
                                 AND lifestatus NOT IN ('Phasing Out', 'Obsolete')
                                 AND productform NOT IN ('Limited Edition', 'Exclusive') 
                            THEN 1 
                            ELSE 0 
                          END
                        ORDER BY total_forecasted_revenue DESC
                      ) AS FLOAT64) / 
                      CAST(COUNT(*) OVER (
                        PARTITION BY 
                          CASE 
                            WHEN total_forecasted_revenue IS NOT NULL AND total_forecasted_revenue > 0 
                                 AND lifestatus NOT IN ('Phasing Out', 'Obsolete')
                                 AND productform NOT IN ('Limited Edition', 'Exclusive') 
                            THEN 1 
                            ELSE 0 
                          END
                      ) AS FLOAT64)
                  END AS percentile_rank
                FROM
                  scored
                ),
                classified AS (
                SELECT
                  *,
                  CASE
                    WHEN total_forecasted_revenue IS NULL OR total_forecasted_revenue = 0 OR lifestatus IN ('Phasing Out', 'Obsolete') OR productform IN ('Limited Edition', 'Exclusive') THEN 'No Class'
                    WHEN ROUND(SUM(total_forecasted_revenue) OVER (ORDER BY total_forecasted_revenue DESC) / SUM(total_forecasted_revenue) OVER () * 100, 2) <= 80 THEN 'A'
                    WHEN ROUND(SUM(total_forecasted_revenue) OVER (ORDER BY total_forecasted_revenue DESC) / SUM(total_forecasted_revenue) OVER () * 100, 2) <= 95 THEN 'B'
                    ELSE 'C'
                  END AS classification
                FROM
                  ranked
                )
              SELECT
                image,
                producttype,
                color,
                upc,
                size,
                baseprice,
                launchdate,
                enddate,
                productdivision,
                productcategory,
                productform,
                productspecification,
                lifestatus,
                total_forecasted_revenue,
                percent_revenue,
                ROUND(SUM(percent_revenue) OVER (ORDER BY percent_revenue DESC), 2) AS percent_total_revenue,
                classification,
                total_demand_90_days,
                total_demand_60_days,
                total_demand_30_days,
                CASE
                  WHEN classification = 'A' THEN ROUND(total_demand_90_days, 0)
                  WHEN classification = 'B' THEN ROUND(total_demand_60_days, 0)
                  WHEN classification = 'C' THEN ROUND(total_demand_30_days, 0)
                  ELSE 0
                END AS safety_stock,
                CASE
                  WHEN classification = 'A' THEN ROUND(total_demand_90_days * 0.75, 0)
                  WHEN classification = 'B' THEN ROUND(total_demand_60_days * 0.75, 0)
                  WHEN classification = 'C' THEN ROUND(total_demand_30_days * 0.75, 0)
                  ELSE 0
                END AS min_stock,
                CASE
                  WHEN classification = 'A' THEN ROUND(total_demand_90_days * 1.25, 0)
                  WHEN classification = 'B' THEN ROUND(total_demand_60_days * 1.25, 0)
                  WHEN classification = 'C' THEN ROUND(total_demand_30_days * 1.25, 0)
                  ELSE 0
                END AS max_stock
              FROM
                classified
              ORDER BY
                total_forecasted_revenue DESC
            `
          }
        });
        console.log('BigQuery response:', response);
        console.log('Sample data rows:', response.data?.slice(0, 3));
        const formattedData = response.data.map((row, i) => {
          const calculatedClassification = row.classification;
          return {
            id: i,
            ...row,
            calculated_classification: calculatedClassification,
            total_demand: row.month_2_demand + row.month_3_demand + row.month_4_demand,
            launchdate: row.launchdate && row.launchdate.value ? new Date(row.launchdate.value) : null,
            enddate: row.enddate && row.enddate.value ? new Date(row.enddate.value) : null,
          };
        });
        const sortedData = (formattedData || []).slice().sort((a, b) => b.total_forecasted_revenue - a.total_forecasted_revenue);
        setItems(sortedData);
      } catch (error) {
        console.error('Error fetching items:', error?.message);
        message.error('Error fetching items' + error?.message);
      }
      setLoading(false);
    };
    fetchItems();
  }, []);

  // Load manual overrides when component mounts
  useEffect(() => {
    loadManualOverrides();
  }, [loadManualOverrides]);

  // Apply manual overrides to items when overrides or items change
  useEffect(() => {
    if (items.length > 0) {
      const updatedItems = items.map(item => {
        const override = manualOverrides[item.upc];
        if (override) {
          return {
            ...item,
            classification: override.classification,
            safety_stock: override.safety_stock,
            min_stock: override.min_stock,
            max_stock: override.max_stock,
            is_manual_override: true,
            modified_at: override.modified_at
          };
        }
        return {
          ...item,
          is_manual_override: false
        };
      });
      setItems(updatedItems);
    }
  }, [manualOverrides]);

  // Filter items based on showMismatchOnly
  const filteredItems = React.useMemo(() => {
    if (!showMismatchOnly) return items;
    return items.filter(item => 
      item.classification !== item.calculated_classification
    );
  }, [items, showMismatchOnly]);

  // Handle cell value changes
  const onCellValueChanged = useCallback((params) => {
    if (params.colDef.field === 'classification') {
      const { data, newValue, oldValue } = params;
      
      // Track changes whenever the classification value changes
      if (newValue !== oldValue) {
        // Calculate new stock levels
        const stockLevels = calculateStockLevels(data, newValue);
        
        // Update the row data
        const updatedData = {
          ...data,
          classification: newValue,
          safety_stock: stockLevels.safety_stock,
          min_stock: stockLevels.min_stock,
          max_stock: stockLevels.max_stock,
          is_manual_override: true,
          has_pending_changes: true
        };
        
        // Update the grid
        params.node.setData(updatedData);
        
        // Update items state to keep it in sync
        setItems(prev => prev.map(item => 
          item.upc === data.upc ? updatedData : item
        ));
        
        // Track pending changes for any classification change
        setPendingChanges(prev => ({
          ...prev,
          [data.upc]: {
            percent_revenue: data.percent_revenue,
            percent_total_revenue: data.percent_total_revenue,
            classification: newValue,
            safety_stock: stockLevels.safety_stock,
            min_stock: stockLevels.min_stock,
            max_stock: stockLevels.max_stock,
            calculated_classification: data.calculated_classification,
            locked: false
          }
        }));
      }
    }
  }, [calculateStockLevels]);

  const columns = [
    {
      headerName: 'Image', field: 'image', cellRenderer: (params) =>
        <ImageCell imageUrl={params.value} altText="Item" />
    },
    { headerName: 'Division', field: 'productdivision' },
    { headerName: 'Product Category', field: 'productcategory' },
    { headerName: 'Product Form', field: 'productform' },
    { headerName: 'Product Specification', field: 'productspecification', cellStyle: (params) => {
      if (typeof params.value === 'string' && params.value.toLowerCase().includes('seasonal')) {
        return { color: PRODUCT_SPECIFICATION_COLORS['seasonal'] };
      }
      if (params.value.toLowerCase().includes('exclusive')) {
        return { color: PRODUCT_SPECIFICATION_COLORS['exclusive'] };
      }
      if (params.value.toLowerCase().includes('limited')) {
        return { color: PRODUCT_SPECIFICATION_COLORS['limited'] };
      }
      if (params.value.toLowerCase().includes('core')) {
        return { color: PRODUCT_SPECIFICATION_COLORS['core'] }; 
      }
      return { color: '#000' };
    } },
    { headerName: 'Product Type', field: 'producttype' },
    { headerName: 'Life Status', field: 'lifestatus', cellStyle: (params) => {
      return { color: lifeStatusColors[params.value] || '#000'};
    } },
    { headerName: 'Color', field: 'color' },
    { headerName: 'Size', field: 'size' },
    { headerName: 'UPC', field: 'upc' },
    { headerName: 'Base Price', field: 'baseprice', valueFormatter: (params) => params.value ? params.value.toLocaleString('en-US', { style: 'currency', currency: 'USD' }) : '' },
    { headerName: 'Launch Date', field: 'launchdate' },
    { headerName: 'End Date', field: 'enddate' },
    {
      headerName: 'Classification',
      field: 'classification',
      editable: true,
      cellEditor: 'agSelectCellEditor',
      cellEditorParams: {
        values: ['A', 'B', 'C', 'No Class']
      },
      valueSetter: (params) => {
        // Validate pasted values
        const validValues = ['A', 'B', 'C', 'No Class', ''];
        const newValue = params.newValue;
        
        if (validValues.includes(newValue)) {
          params.data[params.colDef.field] = newValue;
          return true;
        }
        return false;
      },
      cellStyle: (params) => {
        const classification = params.value;
        const isManualOverride = params.data?.is_manual_override;
        const hasPendingChanges = params.data?.has_pending_changes;
        const isMismatch = params.data?.calculated_classification !== params.value;
        const showManualBorder = isManualOverride && isMismatch;
        
        if (!classification || classification === '') {
          return {
            backgroundColor: 'transparent',
            color: '#000',
            fontWeight: 'normal',
            border: hasPendingChanges ? '2px solid #faad14' : (showManualBorder ? '2px solid #1890ff' : 'none')
          };
        }
        return {
          backgroundColor: itemClassificationColors[classification] || 'transparent',
          color: classification === 'No Class' ? '#000' : '#fff',
          fontWeight: 'bold',
          border: hasPendingChanges ? '2px solid #faad14' : (showManualBorder ? '2px solid #1890ff' : 'none')
        };
      },
      valueFormatter: (params) => params.value || '',
            cellRenderer: (params) => {
        const classification = params.value || '';
        const hasPendingChanges = params.data?.has_pending_changes;
        
        return (
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <span>{classification}</span>
            {hasPendingChanges && <span style={{ color: '#faad14', fontSize: '10px' }}>*</span>}
          </div>
        );
      }
     },
     {
       headerName: 'Calculated Classification',
       field: 'calculated_classification',
       editable: false,
       cellStyle: (params) => {
         const classification = params.value;
         if (!classification || classification === '') {
           return {
             backgroundColor: 'transparent',
             color: '#000',
             fontWeight: 'normal',
             opacity: 0.6
           };
         }
         return {
           backgroundColor: itemClassificationColors[classification] || 'transparent',
           color: classification === 'No Class' ? '#000' : '#fff',
           fontWeight: 'bold',
           opacity: 0.6
         };
       },
       valueFormatter: (params) => params.value || ''
     },
     { headerName: 'Total Forecasted Revenue', field: 'total_forecasted_revenue', valueFormatter: (params) => params.value ? params.value.toLocaleString('en-US', { style: 'currency', currency: 'USD' }) : '' },
    { headerName: 'Percent Revenue', field: 'percent_revenue', valueFormatter: (params) => params.value !== null && params.value !== undefined ? params.value.toFixed(2) + '%' : '' },
    { headerName: 'Percent Total Revenue', field: 'percent_total_revenue', valueFormatter: (params) => params.value !== null && params.value !== undefined ? params.value.toFixed(2) + '%' : '' },
    { 
      headerName: 'Safety Stock', 
      field: 'safety_stock', 
      valueFormatter: (params) => params.value ? params.value.toLocaleString('en-US') : '0',
      cellStyle: (params) => {
        const isManualOverride = params.data?.is_manual_override;
        const hasPendingChanges = params.data?.has_pending_changes;
        const isMismatch = params.data?.calculated_classification !== params.data?.classification;
        const showManualStyle = isManualOverride && isMismatch;
        return {
          backgroundColor: hasPendingChanges ? '#fff7e6' : (showManualStyle ? '#e6f7ff' : 'transparent'),
          border: hasPendingChanges ? '1px solid #faad14' : (showManualStyle ? '1px solid #1890ff' : 'none')
        };
      }
    },
    { 
      headerName: 'Min Stock', 
      field: 'min_stock', 
      valueFormatter: (params) => params.value ? params.value.toLocaleString('en-US') : '0',
      cellStyle: (params) => {
        const isManualOverride = params.data?.is_manual_override;
        const hasPendingChanges = params.data?.has_pending_changes;
        const isMismatch = params.data?.calculated_classification !== params.data?.classification;
        const showManualStyle = isManualOverride && isMismatch;
        return {
          backgroundColor: hasPendingChanges ? '#fff7e6' : (showManualStyle ? '#e6f7ff' : 'transparent'),
          border: hasPendingChanges ? '1px solid #faad14' : (showManualStyle ? '1px solid #1890ff' : 'none')
        };
      }
    },
    { 
      headerName: 'Max Stock', 
      field: 'max_stock', 
      valueFormatter: (params) => params.value ? params.value.toLocaleString('en-US') : '0',
      cellStyle: (params) => {
        const isManualOverride = params.data?.is_manual_override;
        const hasPendingChanges = params.data?.has_pending_changes;
        const isMismatch = params.data?.calculated_classification !== params.data?.classification;
        const showManualStyle = isManualOverride && isMismatch;
        return {
          backgroundColor: hasPendingChanges ? '#fff7e6' : (showManualStyle ? '#e6f7ff' : 'transparent'),
          border: hasPendingChanges ? '1px solid #faad14' : (showManualStyle ? '1px solid #1890ff' : 'none')
        };
      }
    },
  ];

  return <Layout>
    <Content style={{ height: '90vh', width: '100%' }}>
      <div style={{ padding: '16px 0' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Tooltip title="Recalculate Classifications">
              <Button
                icon={<ReloadOutlined />}
                onClick={recalculateClassifications}
                loading={loading}
                disabled={loading || saving}
              />
            </Tooltip>
            <Tooltip title="Save Changes">
              <Button
                icon={<SaveOutlined />}
                onClick={saveAllPendingChanges}
                loading={loading || saving}
                disabled={Object.keys(pendingChanges).length === 0 || loading}
              />
            </Tooltip>
            <Tooltip title="Reset Changes">
              <Button
                icon={<UndoOutlined />}
                onClick={resetPendingChanges}
                disabled={Object.keys(pendingChanges).length === 0 || loading || saving}
              />
            </Tooltip>
            <Tooltip title="Show Mismatched Only">
              <Button
                icon={<EyeOutlined />}
                onClick={() => setShowMismatchOnly(!showMismatchOnly)}
                type={showMismatchOnly ? 'primary' : 'default'}
                disabled={loading || saving}
              />
            </Tooltip>
          </Col>
        </Row>
      </div>
      <AgGridReact
        loading={loading || saving}
        defaultColDef={{
          resizable: true,
          sortable: true,
          filter: true,
        }}
        enableRangeSelection={true}
        enableClipboard={true}
        suppressCopySingleCellRanges={true}
        processCellFromClipboard={(params) => {
          // Handle pasted values for classification column
          if (params.column.getColId() === 'classification') {
            const validValues = ['A', 'B', 'C', 'No Class'];
            const pastedValue = params.value;
            
            // Return valid values, or null for invalid values
            if (validValues.includes(pastedValue)) {
              return pastedValue;
            }
            return null;
          }
          return params.value;
        }}
        rowData={filteredItems}
        columnDefs={columns}
        autoSizeStrategy={{
          type: 'fitCellContents',
        }}
        onCellValueChanged={onCellValueChanged}
        onGridReady={(params) => setGridApi(params.api)}
      />
    </Content>
  </Layout>;
};

export default ItemClassification;