rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // This rule allows anyone with your Firestore database reference to view, edit,
    // and delete all data in your Firestore database. It is useful for getting
    // started, but it is configured to expire after 30 days because it
    // leaves your app open to attackers. At that time, all client
    // requests to your Firestore database will be denied.
    //
    // Make sure to write security rules for your app before that time, or else
    // all client requests to your Firestore database will be denied until you Update
    // your rules
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
    match /lists/{l} {
      allow read, write: if request.auth != null;
    }
    match /historicalSalesChartViews/{l} {
      allow read: if resource.data.createdBy == request.auth.uid || resource.data.isPublic == true;
      allow write: if request.auth != null;
    }
    match /historicalSalesTableViews/{t} {
      allow read: if resource.data.createdBy == request.auth.uid || resource.data.isPublic == true;
      allow write: if request.auth != null;
    }
    match /itemNodeMatrixViews/{i} {
      allow read: if resource.data.createdBy == request.auth.uid || resource.data.isPublic == true;
      allow write: if request.auth != null;
    }
    match /users/{u}{
      allow read, write: if request.auth != null;
    }
    match /pimViews/{l} {
      allow read, write: if request.auth != null;
    }
  }
}