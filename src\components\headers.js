/* eslint-disable react/prop-types */
/* eslint-disable require-jsdoc */
import React, { useEffect, useState } from 'react';
import '../App.css';
import { logout } from '../pages/firebase';
import { useNavigate } from 'react-router-dom';
import { auth } from '../pages/firebase';
import { useAuthState } from 'react-firebase-hooks/auth';
import { Menu, Typography, Space, Divider } from 'antd';
import { 
  ShoppingCartOutlined, 
  BarChartOutlined, 
  AppstoreOutlined, 
  LineChartOutlined, 
  SettingOutlined, 
  LogoutOutlined, 
  ShopOutlined, 
  BookOutlined, 
  RiseOutlined, 
  ToolOutlined, 
  DashboardOutlined, 
  FileTextOutlined, 
  TruckOutlined, 
  UploadOutlined, 
  DatabaseOutlined,
  ExceptionOutlined,
  AlertOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { AutoGraphOutlined } from '@mui/icons-material';

const { Title } = Typography;

export default function Header({ userObj }) {
  // const [userEmail, setUserEmail] = useState('');
  // console.log('header userObj', userObj);
  const [user, authLoading] = useAuthState(auth);
  const [current, setCurrent] = useState(window.location.pathname.split('/').pop());

  // Safety check - don't render if userObj is not properly loaded
  if (!userObj || !userObj.userPermissions || !Array.isArray(userObj.userPermissions)) {
    return (
      <div style={{
        padding: '16px',
        textAlign: 'center',
        backgroundColor: '#f0f0f0'
      }}>
        Loading navigation...
      </div>
    );
  }
  // console.log('userObj', userObj);

  const userPermissions = userObj.userPermissions.filter((x) => x && x.hasAccess).map((p) => p.technicalName);

  // Additional safety check for userUrls
  if (!userPermissions || !Array.isArray(userPermissions)) {
    return (
      <div style={{
        padding: '16px',
        textAlign: 'center',
        backgroundColor: '#f0f0f0'
      }}>
        Loading navigation...
      </div>
    );
  }

  const navigate = useNavigate();
  useEffect(() => {
    if (authLoading) {
      // maybe trigger a loading screen
      return;
    }
    if (!user) navigate('/login');
  }, [user, authLoading]);

  // Organize menu items by category for better UX
  const menuItems = [];

  // Core Reports Section
  menuItems.push({
    label: 'Inventory', key: '',
    icon: <BookOutlined />,
    onClick: () => navigate('/'),
    category: 'reports'
  });

  // if (userPermissions.includes('read:dashboard')) {
  //   menuItems.push({
  //     label: 'Dashboard', key: 'dashboard',
  //     icon: <DashboardOutlined />,
  //     onClick: () => navigate('/dashboard'),
  //     category: 'reports'
  //   });
  // }

  // if (userPermissions.includes('read:items')) {
  //   menuItems.push({
  //     label: 'Items', key: 'items',
  //     icon: <ShoppingCartOutlined />,
  //     onClick: () => navigate('/items'),
  //     category: 'management'
  //   });
  // }

  // if (userPermissions.includes('read:wholesale')) {
  //   menuItems.push({
  //     label: 'Wholesale', key: 'wholesale',
  //     icon: <ShopOutlined />,
  //     onClick: () => navigate('/wholesale'),
  //     category: 'management'
  //   });
  // }
  // if (userUrls.includes('/forecasts/operations') || userUrls.includes('/forecasts/marketing')) {
  //   const children = [];
  //   if (userUrls.includes('/forecasts/operations')) {
  //     children.push({
  //       label: 'Operations', key: 'operations',
  //       icon: <BarChartOutlined />,
  //       key: 'opsForecast',
  //       onClick: () => navigate('/forecasts/operations'),
  //     });
  //   }
  //   if (userUrls.includes('/forecasts/marketing')) {
  //     children.push({
  //       label: 'Marketing', key: 'marketing',
  //       icon: <LineChartOutlined />,
  //       key: 'marketingForecast',
  //       onClick: () => navigate('/forecasts/marketing'),
  //     });
  //   }
  //   items.push({
  //     label: 'Forecasts', key: 'forecasts',
  //     icon: <RiseOutlined />,
  //     children,
  //   });
  // }
  // if (userUrls.some(x => x.includes('/pim'))) {
  //   const children = [];
  //   if (userUrls.includes('/pim/products')) {
  //     children.push({
  //       label: 'Products', key: 'products',
  //       icon: <BookOutlined />,
  //       onClick: () => navigate('/pim/products'),
  //     });
  //   }
  //   if (userUrls.includes('/pim/variants')) {
  //     children.push({
  //       label: 'Variants', key: 'variants',
  //       icon: <BookOutlined />,
  //       onClick: () => navigate('/pim/variants'),
  //     });
  //   }
  //   items.push({
  //     label: 'PIM', key: 'pim',
  //     icon: <BookOutlined />,
  //     children,
  //   });
  // }
  // Analytics & Forecasting Section
  if (userPermissions.includes('read:forecastBeta')) {
    menuItems.push({
      label: 'Forecasting', key: 'forecastBeta',
      icon: <AutoGraphOutlined />,
      onClick: () => navigate('/forecastBeta'),
      category: 'analytics'
    });
  }

  if (userPermissions.includes('read:historicalSales')) {
    menuItems.push({
      label: 'Sales History', key: 'historicalSales',
      icon: <HistoryOutlined />,
      onClick: () => navigate('/historicalSales'),
      category: 'analytics'
    });
  }

  if (userPermissions.includes('read:orderAllocation')) {
    menuItems.push({
      label: 'Order Allocation', key: 'order-allocation',
      icon: <FileTextOutlined />,
      onClick: () => navigate('/order-allocation'),
      category: 'operations'
    });
  }

  // Exception Reports Section
  if (userPermissions.includes('read:shippingExceptions')) {
    menuItems.push({
      label: 'Shipping Exceptions', key: 'shippingExceptions',
      icon: <AlertOutlined />,
      onClick: () => navigate('/shippingExceptions'),
      category: 'exceptions'
    });
  }

  if (userPermissions.includes('read:inventoryExceptions')) {
    menuItems.push({
      label: 'Inventory Exceptions', key: 'inventoryExceptions',
      icon: <ExceptionOutlined />,
      onClick: () => navigate('/inventoryExceptions'),
      category: 'exceptions'
    });
  }

  // Data Management Section
  if (userPermissions.includes('read:uploadData')) {
    menuItems.push({
      label: 'Upload Data', key: 'upload',
      icon: <UploadOutlined />,
      onClick: () => navigate('/uploadData'),
      category: 'data'
    });
  }

  if (userPermissions.includes('read:data')) {
    menuItems.push({
      label: 'Data Manager', key: 'data',
      icon: <DatabaseOutlined />,
      onClick: () => navigate('/data'),
      category: 'data'
    });
  }

  if (userPermissions.includes('read:utilities')) {
    menuItems.push({
      label: 'Utilities', key: 'utilities',
      icon: <ToolOutlined />,
      onClick: () => navigate('/utilities'),
      category: 'admin'
    });
  }

  if (userPermissions.includes('read:settings')) {
    menuItems.push({
      label: 'Settings', key: 'settings',
      icon: <SettingOutlined />,
      onClick: () => navigate('/settings'),
      category: 'admin'
    });
  }

  // Always show logout
  menuItems.push({
    label: 'Logout', key: 'logout',
    icon: <LogoutOutlined />,
    onClick: () => logout(),
    category: 'admin'
  });

  // Check if in development/test mode
  const isTestMode = window.location.hostname === 'localhost';

  return (
    <div className={`modern-header ${isTestMode ? 'test-mode' : ''}`}>
      <div className="header-brand">
        <Title level={4} style={{
          margin: 0,
          color: isTestMode ? '#fff' : '#1976d2',
          fontWeight: 600
        }}>
          HJ Reporting {isTestMode && '(TEST)'}
        </Title>
      </div>
      
      <Menu
        selectedKeys={[current]}
        mode="horizontal"
        className="modern-menu"
        style={{ 
          flex: 1,
          border: 'none',
          backgroundColor: 'transparent'
        }}
      >
        {menuItems.map(item => {
          // Handle logout specially
          if (item.key === 'logout') {
            return (
              <Menu.Item 
                key={item.key} 
                icon={item.icon}
                className="logout-item"
                onClick={item.onClick}
                style={{ marginLeft: 'auto' }}
              >
                {item.label}
              </Menu.Item>
            );
          }
          
          return (
            <Menu.Item 
              key={item.key} 
              icon={item.icon}
              onClick={() => {
                item.onClick();
                setCurrent(item.key);
              }}
              className="menu-item"
            >
              {item.label}
            </Menu.Item>
          );
        })}
      </Menu>
    </div>
    // <ul className="navbar-nav bg-gradient-dark sidebar sidebar-dark accordion toggled" id="accordionSidebar">
    //   <a className="sidebar-brand d-flex align-items-center justify-content-center" href="#">
    //     <div className="sidebar-brand-icon">
    //       <i className="fas fa-chart-simple"></i>
    //     </div>
    //     <div className="sidebar-brand-text mx-3">HJ Reporting</div>
    //   </a>
    //   <hr className="sidebar-divider my-0" />
    //   <li className={(userObj.page == 'inventory_report') ? 'nav-item active' : 'nav-item'}>
    //     <a className="nav-link" href="/">
    //       <i className="fas fa-fw fa-table"></i>
    //       <span>Inventory Report</span>
    //     </a>
    //   </li>
    //   <li className={(userObj.page == 'items') ? 'nav-item active' : 'nav-item'}>
    //     <a className="nav-link" href="/items">
    //       <i className="fas fa-fw fa-bottle-water"></i>
    //       <span>Items</span>
    //     </a>
    //   </li>
    //   {userObj && userObj.permissions && userObj.permissions.wholesale && (
    //     <li className={(userObj.page == 'wholesale') ? 'nav-item active' : 'nav-item'}>
    //       <a className="nav-link" href="/wholesale">
    //         <i className="fas fa-fw fa-arrow-right-arrow-left"></i>
    //         <span>Wholesale</span>
    //       </a>
    //     </li>
    //   )}
    //   <hr className="sidebar-divider d-none d-md-block" />
    //   <li className="nav-item">
    //     <a className="nav-link" href="#" onClick={() => logout()}>
    //       <i className="fas fa-fw fa-right-from-bracket"></i>
    //       <span>Logout</span>
    //     </a>
    //   </li>
    // </ul>
  );
}
