import React, { useState, useEffect } from 'react';
import { Box, Typography, Grid, Paper, CircularProgress, Alert } from '@mui/material';
import {
    ComposedChart, Line, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import { useFormatAxisTick } from '../../hooks/useFormatAxisTick';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import ChartExportWrapper from './ChartExportWrapper';

const BackorderRate = () => {
    const [backorderData, setBackorderData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);

    // Custom formatter for quantities
    const formatQuantity = (value) => {
        return value.toLocaleString(undefined, { maximumFractionDigits: 0 });
    };

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const functions = getFunctions();
            const getBackorderRate = httpsCallable(functions, 'getBackorderRate');
            const getKPIGoalsForReport = httpsCallable(functions, 'getKPIGoalsForReport');

            const [backorderResult, goalsResult] = await Promise.all([
                getBackorderRate(),
                getKPIGoalsForReport({ reportName: 'backorder' })
            ]);

            setBackorderData(backorderResult.data);

            if (goalsResult.data.success) {
                setKpiGoals(goalsResult.data.data);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            setError('Failed to fetch data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    const calculateBackorderTrend = () => {
        if (!backorderData?.monthlyData || backorderData.monthlyData.length < 2) return 0;
        const lastTwo = backorderData.monthlyData.slice(-2);
        return lastTwo[1].backorderRate - lastTwo[0].backorderRate;
    };

    const calculatePerformanceStatus = () => {
        if (!backorderData || !kpiGoals?.['Backorder Rate']) return 'neutral';
        const currentRate = backorderData.overallBackorderRate;
        const goalValue = parseFloat(kpiGoals['Backorder Rate'].value);
        return currentRate <= goalValue ? 'success' : 'warning';
    };

    // Calculate domains including the goal values
    const calculateDomains = (data) => {
        if (!data) return { rateDomain: [0, 100], unitsDomain: [0, 1000] };

        const rateValues = data.map(item => item.backorderRate);
        const unitValues = data.map(item => Math.max(item.totalOrders, item.backorders));

        // Include goal values in rate domain calculation
        const goalValue = kpiGoals?.['Backorder Rate']?.value;
        const maxRate = Math.max(...rateValues, goalValue ? parseFloat(goalValue) : 0);

        return {
            rateDomain: [0, Math.ceil(maxRate * 1.2)], // Add 20% padding for visibility
            unitsDomain: [0, Math.ceil(Math.max(...unitValues) * 1.1)]
        };
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                    <Typography variant="body2">{`Month: ${label}`}</Typography>
                    <Typography variant="body2" color="#ff9800">
                        {`Backorder Rate: ${data.backorderRate.toFixed(2)}%`}
                    </Typography>
                    <Typography variant="body2" color="#66bb6a">
                        {`Total Units Ordered: ${formatQuantity(data.totalOrders)} units`}
                    </Typography>
                    <Typography variant="body2" color="#f44336">
                        {`Backordered Units: ${formatQuantity(data.backorders)} units`}
                    </Typography>
                    {data.products && data.products.length > 0 && (
                        <Box sx={{ mt: 1 }}>
                            <Typography variant="body2" fontWeight="bold">Top Product Types:</Typography>
                            {data.products.slice(0, 3).map((product, index) => (
                                <Typography key={index} variant="body2" color="#666">
                                    <Box component="span" sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                        <span>{product.productType}:</span>
                                        <span>
                                            {`${formatQuantity(product.backorders)}/${formatQuantity(product.totalOrders)} units (${product.backorderRate.toFixed(2)}%)`}
                                        </span>
                                    </Box>
                                </Typography>
                            ))}
                        </Box>
                    )}
                </Paper>
            );
        }
        return null;
    };

    // Use the KPI reference lines hook
    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: kpiGoals?.['Backorder Rate'],
        yAxisId: "left",
        styles: {
            stroke: "#ff9800",
            strokeDasharray: "3 3",
            label: {
                fill: "#ff9800",
                fontSize: 12,
                position: "right"
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Min",
            max: "Max"
        }
    });

    if (isLoading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Alert severity="error" sx={{ mt: 2 }}>
                {error}
            </Alert>
        );
    }

    if (!backorderData || !kpiGoals) return null;

    const { rateDomain, unitsDomain } = calculateDomains(backorderData.monthlyData);
    const backorderConfig = kpiGoals['Backorder Rate'];
    const performanceStatus = calculatePerformanceStatus();

    return (
        <ChartExportWrapper title="Backorder_Rate">
            <Box sx={{ p: { xs: 2, sm: 3 }, mb: 3 }}>
                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <ResponsiveContainer width="100%" height={400}>
                        <ComposedChart
                            data={backorderData.monthlyData}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="monthYear" />
                            <YAxis
                                yAxisId="left"
                                domain={rateDomain}
                                label={{
                                    value: 'Backorder Rate (%)',
                                    angle: -90,
                                    position: 'insideLeft',
                                    offset: -10,
                                    style: { textAnchor: 'middle' }
                                }}
                            />
                            <YAxis
                                yAxisId="right"
                                orientation="right"
                                domain={unitsDomain}
                                label={{
                                    value: 'Units',
                                    angle: 90,
                                    position: 'outside',
                                    offset: 25,
                                    style: { textAnchor: 'middle' }
                                }}
                                tickFormatter={formatQuantity}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            {goalReferenceLines}
                            <Bar yAxisId="right" dataKey="totalOrders" fill="#66bb6a" name="Total Units Ordered" />
                            <Bar yAxisId="right" dataKey="backorders" fill="#f44336" name="Backordered Units" />
                            <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="backorderRate"
                                stroke="#ff9800"
                                strokeWidth={3}
                                dot={{ r: 4, fill: "#ff9800" }}
                                activeDot={{ r: 8 }}
                                name="Backorder Rate (%)"
                            />
                        </ComposedChart>
                    </ResponsiveContainer>

                    <Grid container spacing={2} sx={{ mt: 2 }}>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Overall Backorder Rate"
                                value={`${backorderData.overallBackorderRate.toFixed(2)}%`}
                                bgColor="#fff8e1"
                                textColor="#ff9800"
                                status={performanceStatus}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Total Backordered Units"
                                value={`${formatQuantity(backorderData.totalBackorders)} units`}
                                bgColor="#fff0f0"
                                textColor="#f44336"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Total Units Ordered"
                                value={`${formatQuantity(backorderData.totalOrders)} units`}
                                bgColor="#e8f5e9"
                                textColor="#4caf50"
                            />
                        </Grid>
                    </Grid>
                </Paper>

                <GoalStatusDisplay
                    currentValue={backorderData.overallBackorderRate}
                    goalConfig={backorderConfig}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculateBackorderTrend()}
                    size="medium"
                    title="Backorder Rate Performance"
                />
            </Box>
        </ChartExportWrapper>
    );
};

export default BackorderRate;