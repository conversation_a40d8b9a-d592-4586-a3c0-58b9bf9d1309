import {Checkbox, Input, message, Modal, Radio, Select} from 'antd';
import React, {useState, useEffect} from 'react';
import dayjs from 'dayjs';

const AutoPopulateModal = ({visible, setVisible, selectedMonth, api, fetchOpsForecast}) => {
  const populateMethods = [{
    'value': 'lastMonth',
    'label': 'Last Month',
    'startDate': dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
    'endDate': dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
    'days': 30,
  }, {
    'value': 'lastYearSameMonth',
    'label': 'Last Year Same Month',
    'startDate': dayjs(selectedMonth).subtract(1, 'year').startOf('month').format('YYYY-MM-DD'),
    'endDate': dayjs(selectedMonth).subtract(1, 'year').endOf('month').format('YYYY-MM-DD'),
    'days': 30,
  }, {
    'value': 'last90Days',
    'label': 'Last 90 Days',
    'startDate': dayjs().subtract(90, 'days').format('YYYY-MM-DD'),
    'endDate': dayjs().format('YYYY-MM-DD'),
    'days': 90,
  }];
  const [selectedMethod, setSelectedMethod] = useState(populateMethods[0]);
  const [includeNewProducts, setIncludeNewProducts] = useState(true);
  const [excludeOutOfStock, setExcludeOutOfStock] = useState(true);
  const [growthNumber, setGrowthNumber] = useState(null);
  const [revenueGoal, setRevenueGoal] = useState(null);
  const [hitRevenueGoal, setHitRevenueGoal] = useState(false);
  // const [priorMonthlyRevenue, setPriorMonthlyRevenue] = useState(0);
  useEffect(() => {
    setSelectedMethod(populateMethods[0]);
  }, [selectedMonth]);

  // useEffect(() => {
  //   const historicalRevQuery = `SELECT
  //   SUM(revenue) as revenue
  //   FROM \`hj-reporting.sales.dailySalesByChannel\`
  //   WHERE soDate BETWEEN '${selectedMethod.startDate}' AND '${selectedMethod.endDate}'`;
  //   api.runQueryOnCall({options: {query: historicalRevQuery}}).then((data) => {
  //     debugger;
  //     setPriorMonthlyRevenue(data[0].revenue/(selectedMethod.days/30));
  //   });
  // }, [selectedMethod, includeNewProducts, excludeOutOfStock, growthNumber, revenueGoal, hitRevenueGoal]);
  return (
    <Modal
      title="Auto Populate"
      open={visible}
      onOk={async () => {
        setVisible(false);
        message.loading('Generating Forecast', 0);
        const test = await api.generateForecastDataOnCall({
          selectedMethod,
          selectedMonth,
          options: {includeNewProducts, excludeOutOfStock, growthNumber, revenueGoal, hitRevenueGoal}});
        message.destroy();
        if (test) message.success('Forecast Generated', {duration: 2});
        else message.error('Error Generating Forecast', {duration: 2});
        message.destroy();
        await fetchOpsForecast();
      }}
      onCancel={() => setVisible(false)}
    >
      <Select
        options={populateMethods}
        value={selectedMethod}
        onChange={(value) => setSelectedMethod(populateMethods.find((method) => method.value === value))}
      />
      {/* <span>{priorMonthlyRevenue}</span> */}
      <Checkbox
        checked={includeNewProducts}
        onChange={() => setIncludeNewProducts(!includeNewProducts)}
      >Include New Products</Checkbox>
      <Checkbox
        checked={excludeOutOfStock}
        onChange={() => setExcludeOutOfStock(!excludeOutOfStock)}
      >Exclude out of Stock</Checkbox>
      <Radio.Group value={hitRevenueGoal} onChange={(e) => {
        setHitRevenueGoal(e.target.value);
        if (e.target.value) setGrowthNumber(null);
        else setRevenueGoal(null);
      }}>
        <Radio value={true}>Hit Revenue Goal</Radio>
        <Radio value={false}>Set Growth Number</Radio>
      </Radio.Group>
      {hitRevenueGoal ? (
        <>
          <Input
            value={revenueGoal}
            id='revenueGoal'
            placeholder='Revenue Goal'
            onChange={(e) => {
              // setTotalRevenue(parseFloat(e.target.value));
              setRevenueGoal(e.target.value);
            }}
          />
        </>
      ):(
        <>
          <Input
            placeholder='Growth Number'
            value={growthNumber}
            id='growthNumber'
            onChange={(e) => setGrowthNumber(e.target.value)}/>
        </>
      )}
    </Modal>
  );
};
export default AutoPopulateModal;

