import React, { useState, useMemo, useRef, useEffect } from 'react';
import { Button, Modal, InputNumber, Row, Col, Tooltip, Spin } from 'antd';
import { AgGridReact } from 'ag-grid-react';
import { api } from '../../pages/firebase';
import { locObj, lifeStatusColors } from '../../constants';
import { themeBalham } from 'ag-grid-community';

const months = (() => {
  const arr = [];
  const now = new Date();
  for (let i = 0; i < 12; i++) {
    const d = new Date(now.getFullYear(), now.getMonth() + i, 1);
    arr.push(`${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`);
  }
  console.log('Months', arr);
  return arr;
})();

// Custom aggFunc for inventory projection
const inventoryAggFunc = params => {
  // params.values: array of values for this column in the group
  // params.rowNode: the group node
  // params.column: the column
  // params.api: the grid api
  // params.data: the row data (undefined for group rows)
  // params.column.getColId() gives the month string
  const month = params.column.getColId();
  const allRows = params.rowNode.allLeafChildren || [];
  const monthIdx = months.indexOf(month);
  const priorMonth = monthIdx > 0 ? months[monthIdx - 1] : null;
  let sum = 0;
  for (const node of allRows) {
    console.log('node', node);
    const data = node.data;
    if (!data) continue;
    let inQty = 0;
    let outQty = 0;
    if (data.children && Array.isArray(data.children)) {
      const inChild = data.children.find(child => child.name === 'In');
      const outChild = data.children.find(child => child.name === 'Out');
      if (inChild && inChild[month] !== undefined) inQty = inChild[month];
      if (outChild && outChild[month] !== undefined) outQty = outChild[month];
    }
    if (monthIdx === 0) {
      // For the first month, use onHand as starting point, add inbound, subtract outbound
      sum += (data.onHand || 0) + inQty - outQty;
    } else {
      // For other months, use prior month as starting point
      let startQty = data[priorMonth] || 0;
      sum += startQty + inQty - outQty;
    }
  }
  return sum;
};

// Custom aggFunc for summing Out row children
const outSumAggFunc = params => {
  // params.values: array of values for this column in the group
  // For the Out row, just sum the values of its direct children
  return params.values.reduce((sum, val) => sum + (val || 0), 0);
};

const SupplyPlan = () => {
  return (
    <div>
      <h1>Supply Plan</h1>
      Coming Soon
    </div>
  );

  const [rowData, setRowData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedItem, setSelectedItem] = useState(null);
  const [showDetail, setShowDetail] = useState(false);
  const [simulationQty, setSimulationQty] = useState(0);
  const gridRef = useRef();

  // Fetch demand and inbound data from BigQuery
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const itemQuery = `
        SELECT
          i.producttype,
          i.color,
          i.size,
          i.lifestatus,
          i.launchdate,
          i.enddate,
          i.productspecification,
          i.productcategory,
          i.productform,
          i.upc,
          inv.on_hand,
          inv.available
        FROM \`hj-reporting.items.items_netsuite\` AS i
        LEFT JOIN \`hj-reporting.inventory.live_inventory\` AS inv ON i.upc = inv.upc
        WHERE inv.location IN (${Object.keys(locObj).map(loc => `'${locObj[loc]}'`).join(',')})
      `;
      // 1. Fetch demand data
      const demandQuery = `
        SELECT
          FORMAT_DATE('%Y-%m', forecast.date) AS month,
          forecast.forecast_node,
          forecast.upc,
          SUM(forecast.qty) AS forecast_qty
        FROM \
          \`hj-reporting.forecast.demand_plan\` AS forecast
        WHERE forecast.date BETWEEN DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH) AND DATE_ADD(CURRENT_DATE(), INTERVAL 12 MONTH)
        GROUP BY month, forecast.upc, forecast.forecast_node
      `;
      const inboundQuery = `
        SELECT
          upc,
          FORMAT_DATE('%Y-%m', expected_delivery_date) AS month,
          SUM(items_quantity_expected) AS inbound_qty
        FROM \
          \`hj-reporting.transactions.inbounds_netsuite\`
        WHERE expected_delivery_date BETWEEN DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH) AND DATE_ADD(CURRENT_DATE(), INTERVAL 12 MONTH)
        GROUP BY upc, month
        ORDER BY upc, month
      `;
      let demandData = [];
      let inboundData = [];
      let itemData = [];
      try {
        const [demandRes, inboundRes, itemRes] = await Promise.all([
          api.bigQueryRunQueryOnCall({ options: { query: demandQuery } }),
          api.bigQueryRunQueryOnCall({ options: { query: inboundQuery } }),
          api.bigQueryRunQueryOnCall({ options: { query: itemQuery } })
        ]);
        demandData = demandRes.data || [];
        inboundData = inboundRes.data || [];
        itemData = itemRes.data || [];
      } catch (err) {
        console.error('Error fetching data', err.message, err.stack);
        setLoading(false);
        return;
      }
      // 2. Merge by upc and month
      const merged = {};
      for (const item of itemData) {
        merged[item.upc] = {
          lifeStatus: item.lifestatus,
          name: `${item.producttype} ${item.color || ''} ${item.size || ''}`.trim(),
          upc: item.upc,
          launchDate: item.launchdate?.value,
          endDate: item.enddate?.value,
          onHand: item.on_hand,
          available: item.available,
          children: [
            {
              name: 'Starting',
              ...Object.fromEntries(months.map(month => ([month, 0]))),
            },

            {
              name: 'In',
              ...Object.fromEntries(months.map(month => ([month, 0]))),
            },
            {
              name: 'Out',
              ...Object.fromEntries(months.map(month => ([month, 0]))),
            },
            {
              name: 'Ending',
              ...Object.fromEntries(months.map(month => ([month, 0]))),
            },
          ],
        };
      }
      // console.log('merged', merged);
      // Populate the 'In' child row with inboundData for each month and item
      for (const row of inboundData) {
        const upc = row.upc;
        const month = row.month?.value || row.month;
        const qty = row.inbound_qty || 0;
        // console.log('upc', upc, month, qty);
        if (merged[upc]) {
          // Find the 'In' child row
          const inChild = merged[upc].children.find(child => child.name === 'In');
          if (inChild && months.includes(month)) {
            inChild[month] = qty;
            // console.log('inChild', inChild[month]);
          }
        }
      }
      // Build Out children grouped by forecast_node
      const outMap = {};
      for (const row of demandData) {
        const upc = row.upc;
        const month = row.month?.value || row.month;
        const qty = row.forecast_qty || 0;
        const forecastNode = row.forecast_node || 'Unknown';
        if (!merged[upc]) continue;
        if (!outMap[upc]) outMap[upc] = {};
        if (!outMap[upc][forecastNode]) outMap[upc][forecastNode] = { name: forecastNode, ...Object.fromEntries(months.map(m => [m, 0])) };
        outMap[upc][forecastNode][month] = qty;
      }
      // Now set the Out child as a parent with children for each forecast_node
      Object.values(merged).forEach(item => {
        // Remove old Out child
        item.children = item.children.filter(child => child.name !== 'Out');
        // Build Out parent
        const outParent = { name: 'Out', children: [] };
        const outNodes = outMap[item.upc] || {};
        for (const nodeName in outNodes) {
          if (Object.prototype.hasOwnProperty.call(outNodes, nodeName)) {
            outParent.children.push(outNodes[nodeName]);
          }
        }
        // For parent Out row, sum all children for each month
        months.forEach(month => {
          outParent[month] = outParent.children.reduce((sum, child) => sum + (child[month] || 0), 0);
        });
        // Filter out forecast_node children that sum to 0 across all months
        outParent.children = outParent.children.filter(child => months.some(month => (child[month] || 0) !== 0));
        // Remove Ending row and re-add it after Out
        const endingIdx = item.children.findIndex(child => child.name === 'Ending');
        let endingRow = null;
        if (endingIdx !== -1) {
          endingRow = item.children.splice(endingIdx, 1)[0];
        }
        // Add Out row
        item.children.push(outParent);
        // Add Ending row after Out
        if (endingRow) {
          item.children.push(endingRow);
        }
      });

      // Calculate Starting and Ending inventory for each month
      Object.values(merged).forEach(item => {
        let prevEnding = item.onHand || 0;
        months.forEach((month, idx) => {
          // Find in and out for this month
          const inChild = item.children.find(child => child.name === 'In');
          const outChild = item.children.find(child => child.name === 'Out');
          const inQty = inChild ? inChild[month] || 0 : 0;
          const outQty = outChild ? outChild[month] || 0 : 0;
          // Starting inventory
          const starting = idx === 0 ? (item.onHand || 0) : (item[months[idx - 1]] || 0);
          // Ending inventory
          const ending = starting + inQty - outQty;
          // Set on main row
          item[month] = ending;
          // Optionally, also set on children for reference
          const startingChild = item.children.find(child => child.name === 'Starting');
          const endingChild = item.children.find(child => child.name === 'Ending');
          if (startingChild) startingChild[month] = starting;
          if (endingChild) endingChild[month] = ending;
          prevEnding = ending;
        });
      });
      setRowData(Object.values(merged));
      setLoading(false);
    };
    fetchData();
  }, []);

  // Columns for Ag-Grid
  const columnDefs = useMemo(() => [
    {
      minWidth: 120,
      headerName: 'Product', field: 'name', filter: 'agTextColumnFilter', cellRenderer: params => {
        const color = lifeStatusColors[params.value] || '#222';
        // Blue for Starting row
        if (params.data && params.data.name === 'Starting') {
          return <span style={{ color: 'blue' }}>{params.value}</span>;
        }
        // Green for In row
        if (params.data && params.data.name === 'In') {
          return <span style={{ color: 'green' }}>{params.value}</span>;
        }
        // Red for Out row
        if (params.data && params.data.name === 'Out') {
          return <span style={{ color: 'red' }}>{params.value}</span>;
        }
        // Italicize Out children (forecast_node rows)
        if (params.data && params.data.name && params.node.parent && params.node.parent.data && params.node.parent.data.name === 'Out') {
          return <span style={{ fontStyle: 'italic', color }}>{params.value}</span>;
        }
        // Bold Ending rows
        if (params.data && params.data.name === 'Ending') {
          return <span style={{ fontWeight: 'bold', color }}>{params.value}</span>;
        }
        return <span style={{ color }}>{params.value}</span>;
      }
    },
    {
      headerName: 'Life Status',
      field: 'lifeStatus',
      filter: 'agTextColumnFilter',
      hide: true,
      cellRenderer: params => {
        const color = lifeStatusColors[params.value] || '#222';
        return <span style={{ color }}>{params.value}</span>;
      }
    },
    { headerName: 'Product Specification', field: 'productspecification', filter: 'agTextColumnFilter', hide: true },
    { headerName: 'Product Category', field: 'productcategory', filter: 'agTextColumnFilter', hide: true },
    { headerName: 'Product Form', field: 'productform', filter: 'agTextColumnFilter', hide: true },
    { headerName: 'Launch Date', field: 'launchDate', filter: 'agTextColumnFilter', hide: true },
    { headerName: 'End Date', field: 'endDate', filter: 'agTextColumnFilter', hide: true },
    { headerName: 'UPC', field: 'upc', filter: 'agTextColumnFilter', hide: true },
    { headerName: 'OH', field: 'onHand', filter: 'agNumberColumnFilter' },
    { headerName: 'Avail', field: 'available', filter: 'agNumberColumnFilter' },
    ...months.map(month => ({
      headerName: `${month}`,
      field: `${month}`,
      filter: 'agNumberColumnFilter',
      cellRenderer: params => {
        // Blue for Starting row
        if (params.data && params.data.name === 'Starting') {
          return <span style={{ color: 'blue' }}>{params.value}</span>;
        }
        // Green for In row
        if (params.data && params.data.name === 'In') {
          return <span style={{ color: 'green' }}>{params.value}</span>;
        }
        // Red for Out row
        if (params.data && params.data.name === 'Out') {
          return <span style={{ color: 'red' }}>{params.value}</span>;
        }
        // Italicize Out children (forecast_node rows) numbers
        if (params.data && params.data.name && params.node.parent && params.node.parent.data && params.node.parent.data.name === 'Out') {
          return <span style={{ fontStyle: 'italic' }}>{params.value}</span>;
        }
        // Bold Ending rows
        if (params.data && params.data.name === 'Ending') {
          return <span style={{ fontWeight: 'bold' }}>{params.value}</span>;
        }
        return params.value;
      },
      aggFunc: params => {
        // If this is the Out row, use outSumAggFunc, else use inventoryAggFunc
        if (params && params.rowNode && params.rowNode.data && params.rowNode.data.name === 'Out') {
          return outSumAggFunc(params);
        }
        return inventoryAggFunc(params);
      },
    })),
  ], []);

  // Show detail modal
  const handleShowDetail = (item) => {
    setSelectedItem(item);
    setShowDetail(true);
    setSimulationQty(0);
  };

  // Simulate placing an order and update predicted inventory
  const handleSimulateOrder = () => {
    if (!selectedItem) return;
    // Simple simulation: add qty to all future months
    const updated = rowData.map(item => {
      if (item.id === selectedItem.id) {
        const newPredicted = { ...item.predicted };
        months.forEach(month => {
          newPredicted[month] = (newPredicted[month] || 0) + simulationQty;
        });
        return { ...item, predicted: newPredicted };
      }
      return item;
    });
    setRowData(updated);
    setShowDetail(false);
  };

  // Ag-Grid defaultColDef for enterprise features
  const defaultColDef = useMemo(() => ({
    flex: 1,
    minWidth: 120,
    sortable: true,
    resizable: true,
    filter: true,
    enableRowGroup: true,
    // enablePivot: true,
    enableValue: true,
  }), []);

  // Register custom aggFuncs
  const aggFuncs = useMemo(() => ({
    inventoryAggFunc,
  }), []);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <div
        style={{ height: '600px', width: '100%' }}
      >
        {loading ? (
          <Spin size="large" style={{ marginTop: 100 }} />
        ) : (
          <AgGridReact
            ref={gridRef}
            rowData={rowData}
            columnDefs={columnDefs}
            defaultColDef={defaultColDef}
            autoGroupColumnDef={{
              headerName: 'UPC',
              field: 'upc',
              filter: 'agTextColumnFilter',
              hide: true,
              cellRendererParams: {
                suppressCount: true,
              },
            }}
            rowSelection="multiple"
            // animateRows
            // pivotPanelShow="always"
            treeDataChildrenField={"children"}
            treeData={true}
            // groupDefaultExpanded={-1}
            autoSizeStrategy={{
              type: 'fitCellContents',
            }}
            sideBar={{
              toolPanels: [
                'columns',
                'filters',
              ],
              // defaultToolPanel: 'columns',
            }}
            enableCharts
            // suppressAggFuncInHeader={false}
            aggFuncs={aggFuncs}
          />
        )}
      </div>
      {/* Detail Modal */}
      <Modal
        open={showDetail}
        title={selectedItem ? `${selectedItem.name} (${selectedItem.id}) Details` : ''}
        onCancel={() => setShowDetail(false)}
        footer={null}
        width={700}
      >
        {selectedItem && (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <h4>Forecasted Sales</h4>
                <ul>
                  {months.map((m, idx) => (
                    <li key={m}>{m}: {selectedItem.forecastedSales[m]}</li>
                  ))}
                </ul>
                <h4>Inbounds</h4>
                <ul>
                  {months.map((m, idx) => (
                    <li key={m}>{m}: {selectedItem.inbounds[m]}</li>
                  ))}
                </ul>
              </Col>
              <Col span={12}>
                <h4>Stock Out Days</h4>
                <ul>
                  {months.map((m, idx) => (
                    <li key={m}>{m}: {selectedItem.stockOutDays[m]}</li>
                  ))}
                </ul>
                <h4>Simulate Order</h4>
                <InputNumber
                  min={0}
                  value={simulationQty}
                  onChange={setSimulationQty}
                  style={{ width: '100%' }}
                  placeholder="Enter qty to order"
                />
                <Tooltip title="Add this qty to all future months for this item">
                  <Button
                    type="primary"
                    style={{ marginTop: 8 }}
                    onClick={handleSimulateOrder}
                    disabled={simulationQty <= 0}
                  >
                    Simulate Order
                  </Button>
                </Tooltip>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default SupplyPlan;