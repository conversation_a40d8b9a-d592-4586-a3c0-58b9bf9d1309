import React, { useState } from 'react';
import { Upload, message, Image, Button, Popconfirm, Input } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { storage } from '../../pages/firebase';
import axios from 'axios';
// import { isValidUrl } from '../../utils/helpers';
// import { getBase64, generateRandomImageName } from '../../utils/helpers';

const generateRandomImageName = (length = 20) => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let randomString = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    randomString += characters[randomIndex];
  }
  return `IMG_${randomString}`;
};

const FileUpload = ({ directory, fieldId, imageURL, multi }) => {
  const [uploadLoading, setUploadLoading] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview);
    setPreviewOpen(true);
  };

  const handleUploadChange = (info) => {
    console.log('upload', info);
    if (info.file.status === 'removed') {
      if (multi) {
        let images = (imageURL && imageURL.length > 0) ? imageURL : [];
        handleChange(fieldId, images.filter(image => image !== info.file.url));
      } else {
        handleChange(fieldId, null);
      }
    } else {
      setUploadLoading(true);

      const fileUpload = info.file;
      uploadFileToFirebase(fileUpload);
    }
  };

  const uploadFileToFirebase = (fileUpload) => {
    const imageName = generateRandomImageName();
    const storageRef = ref(storage, `${directory}/${imageName}`);
    const uploadTask = uploadBytesResumable(storageRef, fileUpload);

    uploadTask.on(
      'state_changed',
      (snapshot) => {
        console.log(snapshot);
      },
      (error) => {
        console.error('Upload failed', error);
      },
      () => {
        // Get the uploaded image URL
        getDownloadURL(uploadTask.snapshot.ref).then((downloadURL) => {
          console.log(downloadURL);
          if (multi) {
            let images = (imageURL && imageURL.length > 0) ? imageURL : [];
            if (images.indexOf(downloadURL) === -1)
              images.push(downloadURL);

            handleChange(fieldId, images);
          } else {
            handleChange(fieldId, downloadURL);
          }

          if (itemImages.indexOf(downloadURL) === -1) {
            setItemImages(images => [...images, downloadURL]);
          }
          setUploadLoading(false);
        });
      }
    );
  };

  const handleUploadImageURL = async (imageURL) => {
    if (!isValidUrl(imageURL)) {
      message.error('Invalid URL.');
      return;
    }

    setUploadImageURL(imageURL);

    const response = await axios.get(imageURL, { responseType: 'blob' });
    const fileBlob = response.data;
    uploadFileToFirebase(fileBlob);
    console.log(fileBlob);
  };

  return (
    <>
      <Upload
        action="/"
        listType="picture-card"
        fileList={(multi) ? ((imageURL) ? imageURL.map((url, uid) => ({ uid, url })) : []) : ((imageURL) ? [{ uid: 0, url: imageURL }] : [])}
        beforeUpload={() => false}
        multiple={(multi) ? true : false}
        onPreview={handlePreview}
        onChange={handleUploadChange}
      >
        <PlusOutlined /> {(multi) ? 'Images' : 'Image'}
      </Upload>
      {previewImage && (
        <Image
          wrapperStyle={{
            display: 'none',
          }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewImage(''),
          }}
          src={previewImage}
        />
      )}
      <Popconfirm
        title="Enter the Image URL:"
        description={(<Input placeholder="URL" style={{ width: '250px' }} rules={[{ type: 'url', warningOnly: true }]} onPressEnter={(e) => {
          handleUploadImageURL(e.target.value);
        }} />)}
        icon=""
        okText="Cancel"
        okType="text"
        showCancel={false}
      >
        <Button type='link' style={{ 'padding': '0px', marginBottom: '-10px' }}>Upload from URL</Button>
      </Popconfirm>
    </>
  );
};
export default FileUpload;