import React, { useState, useEffect, useRef } from 'react';
import { Select, Button, Modal, Input, Switch, Space, Tooltip, Popconfirm, message, Divider } from 'antd';
import { SaveOutlined, EditOutlined, DeleteOutlined, EyeOutlined, EyeInvisibleOutlined, FolderOutlined, FolderOpenOutlined, UserOutlined, GlobalOutlined, DownOutlined, RightOutlined } from '@ant-design/icons';
import { collection, onSnapshot, addDoc, setDoc, deleteDoc, doc, query, where, or, getDoc } from 'firebase/firestore';
import { db } from '../pages/firebase';
import { useUser } from '../contexts/UserContext';

/**
 * Reusable SavedViews component for managing grid views
 * 
 * @param {Object} props
 * @param {Object} props.gridRef - Reference to the AG Grid
 * @param {string} props.collectionName - Firestore collection name (e.g., 'orderAllocationViews')
 * @param {string} props.userPreferenceKey - Key for storing user's last used view (e.g., 'lastOrderAllocationView')
 * @param {Function} props.onViewApply - Optional callback function when a view is applied (for custom behavior)
 * @param {Object} props.customGridStateCapture - Optional custom function to capture grid state
 * @param {string} props.selectWidth - Width of the select dropdown (default: '250px')
 * @param {boolean} props.showSaveButton - Whether to show the save button (default: true)
 * @param {string} props.saveButtonText - Text for save button (default: 'Save View')
 * @param {Object} props.additionalGridState - Additional state to include in grid state
 * @param {boolean} props.dataReady - Signal from parent that data is loaded and grid is ready for view application
 * @return {JSX.Element} SavedViews component
 */
const SavedViews = ({
  gridRef,
  collectionName,
  userPreferenceKey,
  onViewApply = null,
  customGridStateCapture,
  selectWidth = '250px',
  showSaveButton = true,
  saveButtonText = 'Save View',
  additionalGridState = {}, // For custom state like groupBy, columnDefs, etc.
  dataReady = false // New prop to signal when parent is ready
}) => {
  const { userData } = useUser();
  
  // State management
  const [savedViews, setSavedViews] = useState([]);
  const [selectedViewId, setSelectedViewId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  
  // Save modal state
  const [saveViewModalVisible, setSaveViewModalVisible] = useState(false);
  const [newViewName, setNewViewName] = useState('');
  const [newViewIsPublic, setNewViewIsPublic] = useState(false);
  
  // Edit modal state
  const [editViewModalVisible, setEditViewModalVisible] = useState(false);
  const [editingView, setEditingView] = useState(null);
  const [editViewName, setEditViewName] = useState('');
  const [editViewIsPublic, setEditViewIsPublic] = useState(false);

  // Track initial view application to prevent auto-reapplication
  const initialViewAppliedRef = useRef(false);

  // Control dropdown state during delete confirmation
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [deletingViewId, setDeletingViewId] = useState(null);

  // Category collapse state
  const [categoriesCollapsed, setCategoriesCollapsed] = useState({
    private: false,
    public: false
  });

  // User cache for displaying creator names
  const [userCache, setUserCache] = useState({});

  // Refs for input focus management
  const saveViewInputRef = useRef(null);
  const editViewInputRef = useRef(null);

  // Fetch saved views on component mount and user change
  useEffect(() => {
    if (!userData?.id || !collectionName) {
      setSavedViews([]);
      return;
    }

    const q = query(
      collection(db, collectionName),
      or(
        where('createdBy', '==', userData.id),
        where('isPublic', '==', true)
      )
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const views = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      setSavedViews(views);
    });

    return unsubscribe;
  }, [userData?.id, collectionName]);

  // Load user information for public views to display creator names
  useEffect(() => {
    const publicViews = savedViews.filter(view => view.createdBy !== userData?.id && view.isPublic);
    const uniqueUserIds = [...new Set(publicViews.map(view => view.createdBy))];

    // Only fetch users we don't already have cached
    const uncachedUserIds = uniqueUserIds.filter(userId => !userCache[userId]);

    if (uncachedUserIds.length === 0) return;

    // Fetch user data for uncached users
    const fetchUsers = async () => {
      const newUserData = {};

      for (const userId of uncachedUserIds) {
        try {
          const userDoc = await getDoc(doc(db, 'users', userId));
          if (userDoc.exists()) {
            const userData = userDoc.data();
            newUserData[userId] = {
              email: userData.email,
              displayName: userData.displayName || userData.email?.split('@')[0] || 'Unknown User'
            };
          } else {
            newUserData[userId] = {
              email: 'Unknown',
              displayName: 'Unknown User'
            };
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          newUserData[userId] = {
            email: 'Unknown',
            displayName: 'Unknown User'
          };
        }
      }

      setUserCache(prev => ({ ...prev, ...newUserData }));
    };

    fetchUsers();
  }, [savedViews, userData?.id, userCache]);

  // Load user's last used view and apply it when grid is ready with data
  useEffect(() => {
    if (!userData?.id || !userPreferenceKey || savedViews.length === 0) return;
    
    const lastViewId = userData[userPreferenceKey];
    console.log('SavedViews: Loading view:', lastViewId);

    if (lastViewId && savedViews.some(view => view.id === lastViewId)) {
      setSelectedViewId(lastViewId);

      // Only apply if we haven't applied the initial view yet
      if (!initialViewAppliedRef.current) {
        console.log('SavedViews: Setting up view application');
        setIsLoading(true);

        // Much more efficient approach using direct event-driven pattern
        const applyViewWhenReady = () => {
          const api = gridRef?.current?.api;

          if (!api) {
            // Grid not ready yet, set up a quick check
            const quickCheck = setInterval(() => {
              if (gridRef?.current?.api) {
                clearInterval(quickCheck);
                applyViewWhenReady();
              }
            }, 50); // Check every 50ms instead of long delays

            // Cleanup after 3 seconds max
            setTimeout(() => clearInterval(quickCheck), 3000);
            return;
          }

          // Grid is ready, now check for data
          const checkForDataAndApply = () => {
            const rowCount = api.getDisplayedRowCount();

            if (rowCount > 0) {
              // Data is ready, apply immediately
              applyViewToGrid(lastViewId)
                .then(() => {
                  if (onViewApply) {
                    onViewApply(lastViewId);
                  }
                  initialViewAppliedRef.current = true;
                  setIsLoading(false);
                  console.log('SavedViews: Applied view successfully');
                })
                .catch(error => {
                  console.error('Error applying saved view:', error);
                  setIsLoading(false);
                });
            } else {
              // No data yet, listen for the data update event
              const onDataUpdate = () => {
                api.removeEventListener('rowDataUpdated', onDataUpdate);
                // Small delay to ensure data is settled
                setTimeout(() => {
                  applyViewToGrid(lastViewId)
                    .then(() => {
                      if (onViewApply) {
                        onViewApply(lastViewId);
                      }
                      initialViewAppliedRef.current = true;
                      setIsLoading(false);
                      console.log('SavedViews: Applied view after data load');
                    })
                    .catch(error => {
                      console.error('Error applying saved view:', error);
                      setIsLoading(false);
                    });
                }, 100);
              };

              api.addEventListener('rowDataUpdated', onDataUpdate);

              // Fallback timeout
              setTimeout(() => {
                api.removeEventListener('rowDataUpdated', onDataUpdate);
                if (!initialViewAppliedRef.current) {
                  console.log('SavedViews: Fallback timeout, trying to apply anyway');
                  checkForDataAndApply();
                }
              }, 5000);
            }
          };

          checkForDataAndApply();
        };

        applyViewWhenReady();
      }
    }
  }, [userData, userPreferenceKey, savedViews]);

  // More efficient approach: Apply view when parent signals data is ready
  useEffect(() => {
    if (!dataReady || !userData?.id || !userPreferenceKey || savedViews.length === 0) return;
    if (initialViewAppliedRef.current) return;

    const lastViewId = userData[userPreferenceKey];
    if (lastViewId && savedViews.some(view => view.id === lastViewId) && gridRef?.current?.api) {
      console.log('SavedViews: Data ready signal received, applying view immediately');
      setIsLoading(true);

      // Apply immediately since parent says data is ready
      applyViewToGrid(lastViewId)
        .then(() => {
          if (onViewApply) {
            onViewApply(lastViewId);
          }
          initialViewAppliedRef.current = true;
          setIsLoading(false);
          console.log('SavedViews: Applied view via dataReady signal');
        })
        .catch(error => {
          console.error('Error applying saved view via dataReady:', error);
          setIsLoading(false);
        });
    }
  }, [dataReady, userData, userPreferenceKey, savedViews]);

  // Focus and select text in save view modal when it opens
  useEffect(() => {
    if (saveViewModalVisible && saveViewInputRef.current) {
      // Small delay to ensure modal is fully rendered
      setTimeout(() => {
        if (saveViewInputRef.current) {
          saveViewInputRef.current.focus();
          saveViewInputRef.current.select();
        }
      }, 100);
    }
  }, [saveViewModalVisible]);

  // Focus and select text in edit view modal when it opens
  useEffect(() => {
    if (editViewModalVisible && editViewInputRef.current) {
      // Small delay to ensure modal is fully rendered
      setTimeout(() => {
        if (editViewInputRef.current) {
          editViewInputRef.current.focus();
          editViewInputRef.current.select();
        }
      }, 100);
    }
  }, [editViewModalVisible]);


  // Capture grid state
  const captureGridState = () => {
    if (!gridRef?.current?.api) {
      throw new Error('Grid not ready for saving');
    }

    const api = gridRef.current.api;
    
    if (customGridStateCapture) {
      return customGridStateCapture(api);
    }

    // Default grid state capture
    try {
      return {
        fullState: api.getState(),
        columnState: api.getColumnState ? api.getColumnState() : [],
        columnGroupState: api.getColumnGroupState ? api.getColumnGroupState() : [],
        filterModel: api.getFilterModel ? api.getFilterModel() : {},
        sortModel: api.getSortModel ? api.getSortModel() : [],
        rowGroupCols: api.getRowGroupColumns ? api.getRowGroupColumns().map(c => c.getColId()) : [],
        pivotCols: api.getPivotColumns ? api.getPivotColumns().map(c => c.getColId()) : [],
        valueCols: api.getValueColumns ? api.getValueColumns().map(c => c.getColId()) : [],
        pivotMode: api.isPivotMode ? api.isPivotMode() : false,
        ...additionalGridState
      };
    } catch (error) {
      console.warn('Some grid state could not be captured:', error);
      return { fullState: api.getState(), ...additionalGridState };
    }
  };

  // Handle save view
  const handleSaveView = () => {
    if (!userData?.id) {
      message.error('You must be logged in to save views');
      return;
    }

    setNewViewName('');
    setNewViewIsPublic(false);
    setSaveViewModalVisible(true);
  };

  // Handle save view submit
  const handleSaveViewSubmit = async () => {
    if (!newViewName.trim()) {
      message.warning('Please enter a view name');
      return;
    }

    const sanitizedName = newViewName.trim().substring(0, 100);
    
    // Check for unique name
    const existingView = savedViews.find(view => 
      view.name.toLowerCase() === sanitizedName.toLowerCase() && 
      view.createdBy === userData?.id
    );
    if (existingView) {
      message.error('A view with this name already exists. Please choose a different name.');
      return;
    }

    // Immediate visual feedback
    setIsLoading(true);
    message.loading({ content: 'Saving view...', key: 'saving', duration: 0 });

    try {
      const gridState = captureGridState();
      
      const newDoc = await addDoc(collection(db, collectionName), {
        name: sanitizedName,
        gridState: JSON.parse(JSON.stringify(gridState)),
        createdBy: userData?.id,
        isPublic: newViewIsPublic,
        createdByEmail: userData?.email,
        timestamp: Date.now()
      });

      // Update user's preferred view if userPreferenceKey is provided
      if (userPreferenceKey) {
        await setDoc(doc(db, 'users', userData.id), {
          [userPreferenceKey]: newDoc.id,
        }, { merge: true });
      }

      setSelectedViewId(newDoc.id);
      setSaveViewModalVisible(false);

      // Success feedback
      message.destroy('saving');
      message.success('View saved successfully');
    } catch (error) {
      console.error('Error saving view:', error);
      message.destroy('saving');
      message.error('Failed to save view');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle edit view
  const handleEditView = (view) => {
    setEditingView(view);
    setEditViewName(view.name);
    setEditViewIsPublic(view.isPublic || false);
    setEditViewModalVisible(true);
  };

  // Handle edit view submit
  const handleEditViewSubmit = async () => {
    if (!editViewName.trim()) {
      message.warning('Please enter a view name');
      return;
    }
    
    const sanitizedName = editViewName.trim().substring(0, 100);
    
    // Check for unique name (excluding current view)
    const existingView = savedViews.find(view => 
      view.name.toLowerCase() === sanitizedName.toLowerCase() && 
      view.createdBy === userData?.id &&
      view.id !== editingView.id
    );
    if (existingView) {
      message.error('A view with this name already exists. Please choose a different name.');
      return;
    }
    
    // Immediate visual feedback
    setIsLoading(true);
    message.loading({ content: 'Updating view...', key: 'updating', duration: 0 });

    try {
      await setDoc(doc(db, collectionName, editingView.id), {
        name: sanitizedName,
        isPublic: editViewIsPublic,
        timestamp: Date.now()
      }, { merge: true });

      setEditViewModalVisible(false);

      // Success feedback
      message.destroy('updating');
      message.success('View updated successfully');
    } catch (error) {
      console.error('Error updating view:', error);
      message.destroy('updating');
      message.error('Failed to update view');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete view
  const handleDeleteView = async (viewId) => {
    try {
      await deleteDoc(doc(db, collectionName, viewId));
      if (selectedViewId === viewId) {
        setSelectedViewId(null);
      }
      message.success('View deleted successfully');
    } catch (error) {
      console.error('Error deleting view:', error);
      message.error('Failed to delete view');
    }
  };

  // Internal view application logic
  const applyViewToGrid = async (viewId) => {
    if (!gridRef?.current?.api) {
      return;
    }

    const api = gridRef.current.api;
    setIsLoading(true);

    console.log('SavedViews: Applying view:', viewId);

    try {
      // Fetch the view data from Firestore
      const viewDoc = await getDoc(doc(db, collectionName, viewId));
      if (!viewDoc.exists()) {
        api.setState({});
        return;
      }

      const selectedView = viewDoc.data();
      if (!selectedView || !selectedView.gridState) {
        api.setState({});
        return;
      }

      const gridState = selectedView.gridState;

      try {
        // Try to apply full state first (newer saves)
        if (gridState.fullState) {
          api.setState(gridState.fullState);

          // If filters weren't applied by fullState, try applying them individually as fallback
          if (gridState.filterModel && Object.keys(gridState.filterModel).length > 0) {
            const currentFilters = api.getFilterModel() || {};
            const hasExpectedFilters = Object.keys(gridState.filterModel).every(key =>
              currentFilters[key] && JSON.stringify(currentFilters[key]) === JSON.stringify(gridState.filterModel[key])
            );

            if (!hasExpectedFilters) {
              api.setFilterModel(gridState.filterModel);
            }
          }
        } else {
          // Apply individual state components for older saves

          // Apply column state first
          if (gridState.columnState) {
            api.applyColumnState({
              state: gridState.columnState,
              applyOrder: true,
              applySize: true,
              applyVisible: true,
              applySort: false // Apply sorting separately
            });
          }

          // Apply column group state
          if (gridState.columnGroupState) {
            api.setColumnGroupState(gridState.columnGroupState);
          }

          // Apply sorting
          if (gridState.sortModel) {
            api.setSortModel(gridState.sortModel);
          }

          // Apply row grouping
          if (gridState.rowGroupCols && gridState.rowGroupCols.length > 0) {
            api.setRowGroupColumns(gridState.rowGroupCols);
          }

          // Apply pivot columns
          if (gridState.pivotCols && gridState.pivotCols.length > 0) {
            api.setPivotColumns(gridState.pivotCols);
          }

          // Apply value columns
          if (gridState.valueCols && gridState.valueCols.length > 0) {
            api.setValueColumns(gridState.valueCols);
          }

          // Apply pivot mode
          if (gridState.pivotMode !== undefined) {
            api.setPivotMode(gridState.pivotMode);
          }

          // Apply filters LAST
          if (gridState.filterModel) {
            api.setFilterModel(gridState.filterModel);
          }
        }
      } catch (error) {
        console.error('Error applying view state:', error);
        message.error('Failed to apply saved view');
        api.setState({});
      }
    } catch (error) {
      console.error('Error fetching view from Firestore:', error);
      message.error('Failed to load saved view');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle view selection
  const handleViewChange = async (viewId) => {
    setSelectedViewId(viewId);

    if (viewId) {
      // Apply view using internal logic
      await applyViewToGrid(viewId);

      // Call optional callback for custom behavior
      if (onViewApply) {
        onViewApply(viewId);
      }
    } else {
      // Clear view - reset to default state
      if (gridRef?.current?.api) {
        gridRef.current.api.setState({});
      }
    }
    
    // Update user preference
    if (userPreferenceKey && userData?.id) {
      try {
        await setDoc(doc(db, 'users', userData.id), {
          [userPreferenceKey]: viewId,
        }, { merge: true });
      } catch (error) {
        console.error('Error saving user preference:', error);
      }
    }
  };

  // Handle toggle view visibility
  const handleToggleViewVisibility = async (viewId) => {
    const view = savedViews.find(v => v.id === viewId);
    if (!view) return;

    try {
      await setDoc(doc(db, collectionName, viewId), {
        isPublic: !view.isPublic,
        timestamp: Date.now()
      }, { merge: true });
      
      message.success(`View is now ${!view.isPublic ? 'public' : 'private'}`);
    } catch (error) {
      console.error('Error updating view visibility:', error);
      message.error('Failed to update view visibility');
    }
  };


  // Helper function to get creator display name
  const getCreatorDisplayName = (view) => {
    if (view.createdBy === userData?.id) {
      return null; // Don't show creator for own views
    }

    // Try to get from cache first
    const cachedUser = userCache[view.createdBy];
    if (cachedUser) {
      return cachedUser.displayName;
    }

    // Fall back to createdByEmail if available
    if (view.createdByEmail) {
      return view.createdByEmail.split('@')[0];
    }

    return 'Unknown';
  };

  // Helper function to organize views into categories
  const organizeViewsIntoCategories = () => {
    const myViews = savedViews.filter(v => v.createdBy === userData?.id);
    const publicViews = savedViews.filter(v => v.createdBy !== userData?.id && v.isPublic);

    const options = [];

    // My Views (Private) Category
    if (myViews.length > 0) {
      // Category header
      options.push({
        label: (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: 8,
              padding: '6px 8px',
              fontWeight: 600,
              color: '#1890ff',
              cursor: 'pointer',
              borderBottom: '1px solid #f0f0f0',
              marginBottom: 4,
              borderRadius: '4px',
              transition: 'background-color 0.2s ease',
              background: 'rgba(24, 144, 255, 0.05)'
            }}
            onClick={(e) => {
              e.stopPropagation();
              setCategoriesCollapsed(prev => ({
                ...prev,
                private: !prev.private
              }));
            }}
            onMouseEnter={(e) => {
              e.target.style.background = 'rgba(24, 144, 255, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.target.style.background = 'rgba(24, 144, 255, 0.05)';
            }}
          >
            {categoriesCollapsed.private ? <RightOutlined style={{ fontSize: 10 }} /> : <DownOutlined style={{ fontSize: 10 }} />}
            {categoriesCollapsed.private ? <FolderOutlined style={{ fontSize: 12 }} /> : <FolderOpenOutlined style={{ fontSize: 12 }} />}
            <UserOutlined style={{ fontSize: 12 }} />
            <span>My Views ({myViews.length})</span>
          </div>
        ),
        value: '__category_private__',
        disabled: true
      });

      // Add private views if not collapsed
      if (!categoriesCollapsed.private) {
        myViews.forEach(view => {
          options.push({
            label: view.name,
            value: view.id,
            view: view
          });
        });
      }
    }

    // Public Views Category
    if (publicViews.length > 0) {
      // Add divider if we have both categories and at least one is expanded (showing content)
      if (myViews.length > 0 && (!categoriesCollapsed.private || !categoriesCollapsed.public)) {
        options.push({
          label: <Divider style={{ margin: '4px 0' }} />,
          value: '__divider__',
          disabled: true
        });
      }

      // Category header
      options.push({
        label: (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: 8,
              padding: '6px 8px',
              fontWeight: 600,
              color: '#52c41a',
              cursor: 'pointer',
              borderBottom: '1px solid #f0f0f0',
              marginBottom: 4,
              borderRadius: '4px',
              transition: 'background-color 0.2s ease',
              background: 'rgba(82, 196, 26, 0.05)'
            }}
            onClick={(e) => {
              e.stopPropagation();
              setCategoriesCollapsed(prev => ({
                ...prev,
                public: !prev.public
              }));
            }}
            onMouseEnter={(e) => {
              e.target.style.background = 'rgba(82, 196, 26, 0.1)';
            }}
            onMouseLeave={(e) => {
              e.target.style.background = 'rgba(82, 196, 26, 0.05)';
            }}
          >
            {categoriesCollapsed.public ? <RightOutlined style={{ fontSize: 10 }} /> : <DownOutlined style={{ fontSize: 10 }} />}
            {categoriesCollapsed.public ? <FolderOutlined style={{ fontSize: 12 }} /> : <FolderOpenOutlined style={{ fontSize: 12 }} />}
            <GlobalOutlined style={{ fontSize: 12 }} />
            <span>Public Views ({publicViews.length})</span>
          </div>
        ),
        value: '__category_public__',
        disabled: true
      });

      // Add public views if not collapsed
      if (!categoriesCollapsed.public) {
        publicViews.forEach(view => {
          const creatorName = getCreatorDisplayName(view);
          options.push({
            label: (
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                <span>{view.name}</span>
                {creatorName && (
                  <span style={{
                    fontSize: '11px',
                    color: '#8c8c8c',
                    fontStyle: 'italic',
                    marginLeft: '8px',
                    flexShrink: 0
                  }}>
                    by {creatorName}
                  </span>
                )}
              </div>
            ),
            value: view.id,
            view: view
          });
        });
      }
    }

    return options;
  };

  return (
    <Space>
      <Tooltip title={
        isLoading ? "Loading saved view..." : ""
      }>
        <Select
          style={{
            minWidth: selectWidth,
            ...(isLoading ? {
              borderColor: '#1890ff',
              boxShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)'
            } : {})
          }}
          placeholder={isLoading ? "Loading saved view..." : "Select Saved View"}
          value={selectedViewId}
          onChange={(value) => {
            // Ignore category clicks
            if (value && value.startsWith('__')) return;
            handleViewChange(value);
          }}
          options={organizeViewsIntoCategories()}
          allowClear
          loading={isLoading}
          disabled={isLoading}
          open={dropdownOpen}
          onDropdownVisibleChange={(open) => {
            // Keep dropdown open if we're in the middle of deleting
            if (!open && deletingViewId) return;
            setDropdownOpen(open);
          }}
          onClear={() => setSelectedViewId(null)}
        optionRender={(option) => {
          // Handle category headers and dividers
          if (option.value && option.value.startsWith('__')) {
            return option.label;
          }

          // Handle regular view options
          const savedView = option.view || savedViews.find(v => v.id === option.value);
          if (!savedView) return <span>{option.label}</span>;
          
          return (
            <Space style={{ display: 'flex', justifyContent: 'space-between', gap: 4, width: '100%' }}>
              <span style={{ flex: 1 }}>{option.label}</span>
              <Space>
                {savedView.createdBy === userData?.id && (
                  <Tooltip title="Edit view">
                    <EditOutlined 
                      style={{ color: '#1890ff', cursor: 'pointer', fontSize: 12 }} 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditView(savedView);
                      }}
                    />
                  </Tooltip>
                )}
                {savedView.createdBy === userData?.id && (
                  <Popconfirm 
                    title="Are you sure you want to delete this view?" 
                    onConfirm={(e) => {
                      e.stopPropagation();
                      handleDeleteView(savedView.id);
                      setDeletingViewId(null);
                      setDropdownOpen(false);
                    }}
                    onCancel={(e) => {
                      e.stopPropagation();
                      setDeletingViewId(null);
                    }}
                    onOpenChange={(open) => {
                      if (open) {
                        setDeletingViewId(savedView.id);
                      } else if (!open && deletingViewId === savedView.id) {
                        setDeletingViewId(null);
                      }
                    }}
                  >
                    <DeleteOutlined
                      style={{ color: 'red', cursor: 'pointer', fontSize: 12 }}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    />
                  </Popconfirm>
                )}
                {savedView.createdBy === userData?.id && (
                  <Tooltip title={savedView.isPublic ? 'Public View' : 'Private View'}>
                    {savedView.isPublic ? (
                      <EyeOutlined
                        style={{ 
                          color: '#52c41a',
                          cursor: 'pointer',
                          fontSize: 12
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleViewVisibility(savedView.id);
                        }}
                      />
                    ) : (
                      <EyeInvisibleOutlined
                        style={{ 
                            color: '#8c8c8c',
                            cursor: 'pointer',
                            fontSize: 12
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleViewVisibility(savedView.id);
                        }}
                      />
                    )}
                  </Tooltip>
                )}
              </Space>
            </Space>
          );
        }}
      />
      </Tooltip>
      
      {showSaveButton && (
        <Tooltip title="Create a custom view with your current settings">
          <Button
            onClick={handleSaveView}
            icon={<SaveOutlined />}
            type="primary"
          >
            {saveButtonText}
          </Button>
        </Tooltip>
      )}

      {/* Save View Modal */}
      <Modal
        title="Save View"
        open={saveViewModalVisible}
        onOk={handleSaveViewSubmit}
        onCancel={() => setSaveViewModalVisible(false)}
        okText="Save"
        cancelText="Cancel"
        confirmLoading={isLoading}
        okButtonProps={{ disabled: !newViewName.trim() }}
      >
        <div style={{ marginBottom: 16 }}>
          <label style={{ fontWeight: 500, marginBottom: 8, display: 'block' }}>View Name:</label>
          <Input
            ref={saveViewInputRef}
            value={newViewName}
            onChange={(e) => setNewViewName(e.target.value)}
            onPressEnter={handleSaveViewSubmit}
            placeholder="Enter a name for this view"
            maxLength={100}
            autoFocus
          />
        </div>
        <div style={{ marginBottom: 16 }}>
          <label style={{ fontWeight: 500, marginBottom: 8, display: 'block' }}>Visibility:</label>
          <Switch
            checked={newViewIsPublic}
            onChange={setNewViewIsPublic}
            checkedChildren="Public"
            unCheckedChildren="Private"
          />
          <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
            {newViewIsPublic
              ? 'Public views can be seen by all users but only deleted by you'
              : 'Private views are only visible to you'
            }
          </div>
        </div>
      </Modal>

      {/* Edit View Modal */}
      <Modal
        title="Edit View"
        open={editViewModalVisible}
        onOk={handleEditViewSubmit}
        onCancel={() => setEditViewModalVisible(false)}
        okText="Update"
        cancelText="Cancel"
        confirmLoading={isLoading}
        okButtonProps={{ disabled: !editViewName.trim() }}
      >
        <div style={{ marginBottom: 16 }}>
          <label style={{ fontWeight: 500, marginBottom: 8, display: 'block' }}>View Name:</label>
          <Input
            ref={editViewInputRef}
            value={editViewName}
            onChange={(e) => setEditViewName(e.target.value)}
            onPressEnter={handleEditViewSubmit}
            placeholder="Enter a name for this view"
            maxLength={100}
            autoFocus
          />
        </div>
        <div style={{ marginBottom: 16 }}>
          <label style={{ fontWeight: 500, marginBottom: 8, display: 'block' }}>Visibility:</label>
          <Switch
            checked={editViewIsPublic}
            onChange={setEditViewIsPublic}
            checkedChildren="Public"
            unCheckedChildren="Private"
          />
          <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
            {editViewIsPublic
              ? 'Public views can be seen by all users but only deleted by you'
              : 'Private views are only visible to you'
            }
          </div>
        </div>
      </Modal>
    </Space>
  );
};

export default SavedViews;
