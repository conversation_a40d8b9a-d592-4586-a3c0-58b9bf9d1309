// hooks/useFormatAxisTick.js

import { useCallback } from 'react';

export const useFormatAxisTick = (options = {}) => {
    const {
        millionAbbr = 'M',
        thousandAbbr = 'K',
        currency = '$',
        decimalPlaces = 1
    } = options;

    const formatAxisTick = useCallback((value) => {
        const isNegative = value < 0;
        const absValue = Math.abs(value);
        let formattedValue;

        if (absValue >= 1000000) {
            formattedValue = `${currency}${(absValue / 1000000).toFixed(decimalPlaces)}${millionAbbr}`;
        } else if (absValue >= 1000) {
            formattedValue = `${currency}${(absValue / 1000).toFixed(decimalPlaces)}${thousandAbbr}`;
        } else {
            formattedValue = `${currency}${absValue.toFixed(decimalPlaces)}`;
        }

        return isNegative ? `-${formattedValue}` : formattedValue;
    }, [millionAbbr, thousandAbbr, currency, decimalPlaces]);

    return formatAxisTick;
};