/* eslint-disable guard-for-in */
import React, { useRef, useState, useEffect } from 'react';
import { Row, Col } from 'antd';
import { AgGridReact } from 'ag-grid-react';
import { api } from '../../pages/firebase';
import { message } from 'antd';
import { buildForecastNode } from '../../constants';
import { themeBalham } from 'ag-grid-community';

const SummaryExternalDemandPlans = () => {
  const summaryGridApi = useRef(null);
  const [loading, setLoading] = useState(false);
  const [rows, setRows] = useState([]);
  const fetchExternalPlans = async () => {
    setLoading(true);
    try {
      const response = await api.bigQueryRunQueryOnCall({
        options: {
          query: `
            SELECT
              e.node_code,
              e.region,
              e.division,
              e.class,
              e.channel,
              e.customer,
              i.lifestatus,
              i.productspecification,
              i.producttype,
              i.color,
              i.size,
              e.upc,
              e.date,
              e.qty
            FROM
              \`hj-reporting.forecast.external_demand_plans\` e
            left join \`hj-reporting.items.items_netsuite\` i on e.upc = i.upc
            WHERE
              date between CURRENT_DATE() and CURRENT_DATE() + INTERVAL 12 MONTH
            ORDER BY
              date
        `
        }
      });
      const formattedData = response.data.map((row, i) => {
        const date = new Date(row.date.value);
        return {
          id: i,
          ...row,
          node_code: buildForecastNode(row),
          date: new Date(row.date.value),
          month: date.toLocaleString('default', { month: 'long' }),
          year: date.getFullYear(),
          quarter: `Q${Math.floor((date.getMonth() / 3) + 1)}`
        };
      });
      if (localStorage.getItem('externalDemandChanges')) {
        setPendingChanges(JSON.parse(localStorage.getItem('externalDemandChanges')));
        const changes = JSON.parse(localStorage.getItem('externalDemandChanges'));
        for (let rowId in changes) {
          const formattedRow = formattedData.find(r => r.id === parseInt(rowId));
          if (formattedRow) {
            formattedRow['pendingChanges'] = true;
            for (let key in changes[rowId]) {
              if (Object.prototype.hasOwnProperty.call(changes[rowId], key)) {
                formattedRow[key] = changes[rowId][key].new;
              }
            }
          }
        }
      }
      setRows(formattedData || []);
    } catch (error) {
      message.error('Failed to load external demand plan from BigQuery' + error.message);
      setRows([]);
    }
    setLoading(false);
  };

  useEffect(() => {
    // fetchNodeList();
    fetchExternalPlans();
  }, []);


  const summaryColumns = [
    {
      headerName: 'Division',
      field: 'division',
      enableRowGroup: true,
      hide: true,
      filter: true,
    },
    {
      headerName: 'Class',
      field: 'class',
      rowGroup: true,
      hide: true,
      filter: true,
    },
    {
      headerName: 'Channel',
      field: 'channel',
      rowGroup: true,
      hide: true,
      filter: true,
    },
    {
      headerName: 'Customer',
      field: 'customer',
      rowGroup: true,
      hide: true,
      filter: true,
    },
    {
      headerName: 'Product',
      field: 'producttype',
      rowGroup: true,
      hide: true,
      filter: true,
    },
    {
      headerName: 'UPC',
      field: 'upc',
      rowGroup: true,
      filter: true,
      hide: true,

      valueGetter: params => {
        // console.log(params);
        const { color, size, upc } = params.data;
        return `${color || ''} ${size || ''} (${upc || ''})`;
      },
      valueFormatter: params => {
        console.log(params);
        if (!params.value) return '';
        if (!params.data) return params.value;
        const { color, size, upc } = params.data;
        return `${color || ''} ${size || ''} (${upc || ''})`;
      }
    },
    {
      headerName: 'Year',
      field: 'year',
      pivot: true,
      filter: true,
    },
    {
      headerName: 'Quarter',
      field: 'quarter',
      pivot: true,
      filter: true,
    },
    {
      headerName: 'Month',
      field: 'month',
      pivot: true,
      filter: true,
    },
    {
      headerName: 'Date',
      field: 'date',
      pivot: true,
      filter: true,
    },
    {
      headerName: 'Qty',
      field: 'qty',
      aggFunc: 'sum',
    }
  ];
  return <>
    <Row>
      <Col span={24} style={{ height: '100vh', width: '100%' }}>
        <AgGridReact
          ref={summaryGridApi}
          getChildCount={false}
          rowData={rows}
          loading={loading}
          columnDefs={summaryColumns}
          defaultColDef={{
            flex: 1,
            minWidth: 130,
            enableValue: true,
            enableRowGroup: true,
            enablePivot: true,
          }}
          autoGroupColumnDef={{
            minWidth: 200,
            pinned: "left",
          }}
          autoSizeStrategy={{
            type: 'fitCellContents',
          }}
          pivotMode={true}
          sideBar={{
            toolPanels: [
              {
                id: 'columns',
                labelDefault: 'Columns',
                labelKey: 'columns',
                iconKey: 'columns',
                toolPanel: 'agColumnsToolPanel',
              },
              {
                id: 'filters',
                labelDefault: 'Filters',
                labelKey: 'filters',
                iconKey: 'filter',
                toolPanel: 'agFiltersToolPanel',
              }
            ]
          }}
          pivotPanelShow={"always"}
          theme={themeBalham}
          />
      </Col>
    </Row>
  </>;
};
export default SummaryExternalDemandPlans;
