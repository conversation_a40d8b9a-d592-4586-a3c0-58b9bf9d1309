import React, { useState, useEffect } from 'react';
import { Box, Typography, Grid, Paper, CircularProgress, Alert, LinearProgress } from '@mui/material';
import {
    ComposedChart, Line, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import { useFormatAxisTick } from '../../hooks/useFormatAxisTick';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import ChartExportWrapper from './ChartExportWrapper';

const SupplierDefectRate = () => {
    const [defectData, setDefectData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const formatAxisTick = useFormatAxisTick();

    const formatNumber = (value) => {
        if (!value && value !== 0) return 'N/A';
        return new Intl.NumberFormat('en-US').format(value);
    };

    // New function to handle defect rate formatting with minimum value
    const formatDefectRate = (rate, totalDefects = 0) => {
        if (!rate && rate !== 0) return '0.00';
        if (totalDefects > 0 && parseFloat(rate) === 0) return '0.01';
        return parseFloat(rate).toFixed(2);
    };

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const functions = getFunctions();
            const getSupplierDefectRate = httpsCallable(functions, 'getSupplierDefectRate');
            const getKPIGoalsForReport = httpsCallable(functions, 'getKPIGoalsForReport');

            const [defectResult, goalsResult] = await Promise.all([
                getSupplierDefectRate(),
                getKPIGoalsForReport({ reportName: 'supplier-defect' })
            ]);

            // Transform data and ensure minimum defect rate if defects exist
            const transformedData = {
                ...defectResult.data,
                overallDefectRate: formatDefectRate(defectResult.data.overallDefectRate, defectResult.data.summary.totalDefects),
                monthlyData: defectResult.data.monthlyData.map(month => ({
                    ...month,
                    totalPOs: month.totalOrders,
                    nonDefectOrders: month.totalOrders - month.defectCount,
                    defectRate: formatDefectRate(month.defectRate, month.defectCount)
                }))
            };

            setDefectData(transformedData);

            if (goalsResult.data.success) {
                setKpiGoals(goalsResult.data.data);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            setError('Failed to fetch data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    const calculateDefectTrend = () => {
        if (!defectData?.monthlyData || defectData.monthlyData.length < 2) return 0;
        const lastTwo = defectData.monthlyData.slice(-2);
        return parseFloat(lastTwo[1].defectRate) - parseFloat(lastTwo[0].defectRate);
    };

    const calculateDomains = () => {
        if (!defectData?.monthlyData) return { rateDomain: [0, 2], posDomain: [0, 10] };

        const maxPOs = Math.max(...defectData.monthlyData.map(item => item.totalPOs || 0));
        const maxDefects = Math.max(...defectData.monthlyData.map(item => item.defectCount || 0));
        const maxRate = Math.max(...defectData.monthlyData.map(item => parseFloat(item.defectRate) || 0));
        const targetRate = kpiGoals?.['Supplier Defect Rate']?.value || 1;

        return {
            rateDomain: [0, Math.max(2, maxRate * 1.2, targetRate * 1.2)],
            posDomain: [0, Math.max(maxPOs, maxDefects) * 1.1]
        };
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                    <Typography variant="body2">{`Month: ${label}`}</Typography>
                    <Typography variant="body2" color="text.secondary">
                        {`Total POs: ${formatNumber(data.totalPOs)}`}
                    </Typography>
                    <Typography variant="body2" color="text.primary">
                        {`Non-Defect Deliveries: ${formatNumber(data.nonDefectOrders)}`}
                    </Typography>
                    <Typography variant="body2" color="error">
                        {`Defects: ${formatNumber(data.defectCount)}`}
                    </Typography>
                    <Typography variant="body2" color="primary">
                        {`Defect Rate: ${data.defectRate}%`}
                    </Typography>
                </Paper>
            );
        }
        return null;
    };

    const { rateDomain, posDomain } = calculateDomains();
    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: kpiGoals?.['Supplier Defect Rate'],
        yAxisId: "left",
        styles: {
            stroke: "#ff0000",
            strokeDasharray: "3 3",
            label: {
                fill: "#ff0000",
                fontSize: 12
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Min",
            max: "Max"
        }
    });

    if (isLoading) return <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}><CircularProgress /></Box>;
    if (error) return <Alert severity="error" sx={{ m: 2 }}>{error}</Alert>;
    if (!defectData || !kpiGoals) return null;

    return (
        <ChartExportWrapper title="Supplier_Defect_Rate">
            <Box sx={{ p: { xs: 2, sm: 3 }, mb: 3 }}>
                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <ResponsiveContainer width="100%" height={400}>
                        <ComposedChart
                            data={defectData.monthlyData}
                            margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="monthYear" />
                            <YAxis
                                yAxisId="left"
                                domain={rateDomain}
                                tickFormatter={value => `${value}`}
                                label={{ value: 'Defect Rate (%)', angle: -90, position: 'insideLeft', offset: 0 }}
                            />
                            <YAxis
                                yAxisId="right"
                                orientation="right"
                                domain={posDomain}
                                tickFormatter={formatNumber}
                                label={{ value: 'Number of POs', angle: 90, position: 'insideRight', offset: 0 }}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            {goalReferenceLines}
                            <Bar yAxisId="right" dataKey="totalPOs" name="Total POs" fill="#9e9e9e" />
                            <Bar yAxisId="right" dataKey="defectCount" name="Defects" fill="#ff9800" />
                            <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="defectRate"
                                stroke="#f44336"
                                name="Defect Rate (%)"
                                strokeWidth={2}
                                dot
                            />
                        </ComposedChart>
                    </ResponsiveContainer>

                    <Grid container spacing={3} sx={{ mt: 2 }}>
                        <Grid item xs={12} sm={4}>
                            <KPICard
                                title="Overall Defect Rate"
                                value={`${defectData.overallDefectRate}%`}
                                bgColor="#ffebee"
                                textColor="#f44336"
                            />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                            <KPICard
                                title="Non-Defective Deliveries"
                                value={formatNumber(defectData.summary.nonDefectOrders)}
                                bgColor="#e8f5e9"
                                textColor="#4caf50"
                            />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                            <KPICard
                                title="Total Defects"
                                value={formatNumber(defectData.summary.totalDefects)}
                                bgColor="#fff3e0"
                                textColor="#ff9800"
                            />
                        </Grid>
                    </Grid>
                </Paper>

                <GoalStatusDisplay
                    currentValue={parseFloat(defectData.overallDefectRate)}
                    goalConfig={{
                        ...kpiGoals['Supplier Defect Rate'],
                        goalCondition: 'lower'
                    }}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculateDefectTrend()}
                    title="Supplier Defect Rate Performance"
                    size="medium"
                    isInverseGoal={true}
                />

                {defectData.defects?.types?.length > 0 && (
                    <Paper elevation={3} sx={{ p: 3, mt: 3 }}>
                        <Typography variant="h6" gutterBottom>
                            Top Defect Types
                        </Typography>
                        {defectData.defects.types.map((defect, index) => (
                            <Box key={defect.type} sx={{ mb: 2 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                    <Typography>
                                        {`${index + 1}. ${defect.type}`}
                                    </Typography>
                                    <Typography color="text.secondary">
                                        {`${defect.count} ${defect.count === 1 ? 'occurrence' : 'occurrences'}`}
                                    </Typography>
                                </Box>
                                <LinearProgress
                                    variant="determinate"
                                    value={(defect.count / Math.max(...defectData.defects.types.map(d => d.count))) * 100}
                                    sx={{
                                        height: 8,
                                        borderRadius: 4,
                                        backgroundColor: '#f5f5f5',
                                        '& .MuiLinearProgress-bar': {
                                            backgroundColor: index === 0 ? '#f44336' :
                                                index === 1 ? '#ff9800' :
                                                    index === 2 ? '#ffc107' : '#9e9e9e'
                                        }
                                    }}
                                />
                            </Box>
                        ))}
                    </Paper>
                )}
            </Box>
        </ChartExportWrapper>
    );
};

export default SupplierDefectRate;