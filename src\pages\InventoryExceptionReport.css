/* Set font size for all rows in the inventory exception table */
.inventory-exception-table .ant-table-tbody>tr.inventory-exception-row>td {
  font-size: 12px;
}

/* Ensure single-line for product, life status, and color columns (fallback) */
.inventory-exception-table td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Style for negative quantities - bold red font */
.negative-qty {
  color: #ff0000 !important;
  font-weight: bold !important;
}

/* Add vertical grid lines to all table cells except the last one */
.inventory-exception-table .ant-table-tbody > tr > td:not(:last-child) {
  border-right: 1px solid #e0e0e0;
}

/* Always override striping for in/out rows */
.inventory-exception-table .ant-table-tbody > tr.in-row > td {
  background: #fff !important;
  color: #006400 !important;
  font-style: normal;
  font-size: 10px;
  min-height: 8px;
  height: 8px;
  padding-top: 0px;
  padding-bottom: 0px;
}
.inventory-exception-table .ant-table-tbody > tr.out-row > td {
  background: #fff !important;
  color: #a8071a !important;
  font-style: normal;
  font-size: 10px;
  min-height: 8px;
  height: 8px;
  padding-top: 0px;
  padding-bottom: 0px;
}

/* Out row: confirmed orders (dark red, bold, underline) */
.out-row-confirmed {
  color: #a8071a !important;
  font-weight: bold !important;
  text-decoration: underline !important;
}

/* Out row: pending orders (dark orange, bold, underline) */
.out-row-pending {
  color: #d97a00 !important;
  font-weight: bold !important;
  text-decoration: underline !important;
}

/* Out row: default (just dark red, not bold, no underline) */
.out-row-default {
  color: #a8071a !important;
  font-weight: normal !important;
  text-decoration: none !important;
}

/* Only apply striping to main rows, not in/out rows */
.inventory-exception-table .ant-table-tbody > tr.inventory-exception-row:not(.in-row):not(.out-row):nth-child(even) > td {
  background: #f7f7f7 !important;
}

/* Product type row styling - light blue background */
.inventory-exception-product-type {
  background-color: #0088c7 !important;
  font-weight: bold !important;
}

/* AG Grid specific product type row styling */
.ag-row.inventory-exception-product-type {
  background-color: #e6f7ff !important;
}

.ag-row.inventory-exception-product-type .ag-cell {
  background-color: #e6f7ff !important;
}

.oos-cell {
  background: #a8071a !important;
  color: #fff !important;
  font-weight: 600;
  border-radius: 2px;
  padding: 0 4px;
  display: inline-block;
}

.launch-cell {
  color: #1890ff !important;
  font-weight: bold;
}

.end-cell {
  /* color: #550000 !important; */
  font-weight: bold;
}



/* Ensure the grid container takes full available space */
.ag-grid-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* Style for the main container to fill viewport */
.inventory-exception-full-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Ensure tabs don't take up too much space */
.ant-tabs {
  flex-shrink: 0;
}

/* Style for the grid wrapper to fill remaining space */
.grid-wrapper {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

/* Custom styles for the new columns */
.needs-order-yes {
  color: #a8071a !important;
  font-weight: bold !important;
}

.out-of-stock-days {
  color: #a8071a !important;
  font-weight: bold !important;
}

/* Ensure the card header doesn't take too much space */
.ant-card-head {
  min-height: auto;
  padding: 0 16px;
}

.ant-card-body {
  padding: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .inventory-exception-full-page {
    height: 100vh;
  }
  
  .grid-wrapper {
    margin: 0 8px 8px 8px;
  }
}
.inventory-exception-in{
  background-color: #befdd6 !important;
}
.inventory-exception-out{
  background-color: #ffcbcb !important;
}
.inventory-exception-node{
  background-color: #ffeeee !important;
}