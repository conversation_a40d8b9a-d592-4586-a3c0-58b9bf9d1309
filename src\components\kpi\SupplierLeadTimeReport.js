import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Grid,
    Paper,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    CircularProgress,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow
} from '@mui/material';
import {
    ComposedChart,
    Line,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import { useFormatAxisTick } from '../../hooks/useFormatAxisTick';
import ChartExportWrapper from './ChartExportWrapper';

const SupplierLeadTimeReport = () => {
    const [leadTimeData, setLeadTimeData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedSupplier, setSelectedSupplier] = useState('');
    const [allSuppliers, setAllSuppliers] = useState([]);
    const formatAxisTick = useFormatAxisTick({ currency: '', decimalPlaces: 0 }); // No currency symbol for order counts

    const CHART_COLORS = {
        leadTime: '#ff6b6b',  // Changed from '#82ca9d' to a coral red
        orderCount: '#4dabf5',  // Changed from '#8884d8' to a light blue
        lightGreen: '#fff0f0',  // Changed from '#e8f5e9' to light red
        lightPurple: '#e3f2fd',  // Changed from '#f3f1ff' to light blue
        goalLine: '#ffd43b'  // Changed from '#ff9800' to golden yellow
    };

    const functions = getFunctions();
    const getSupplierLeadTime = httpsCallable(functions, 'getSupplierLeadTime');

    const daysToWeeks = (days) => {
        return days / 7;
    };

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setIsLoading(true);
        try {
            const [leadTimeResult, goalsResult] = await Promise.all([
                getSupplierLeadTime(),
                httpsCallable(functions, 'getKPIGoalsForReport')({ reportName: 'supplier-lead-time' })
            ]);

            if (leadTimeResult.data.result && leadTimeResult.data.result.monthlyData) {
                const currentDate = new Date();
                const filteredData = {
                    ...leadTimeResult.data.result,
                    monthlyData: leadTimeResult.data.result.monthlyData
                        .filter(month => new Date(month.month) <= currentDate)
                        .sort((a, b) => new Date(a.month) - new Date(b.month))
                };
                setLeadTimeData(filteredData);

                // Filter out any undefined or null names when creating the suppliers list
                const supplierNames = filteredData.monthlyData
                    .flatMap(month => month.suppliers
                        .filter(s => s && s.name)
                        .map(s => s.name));

                setAllSuppliers(['All Suppliers', ...new Set(supplierNames)]);
            }

            if (goalsResult.data.success) {
                setKpiGoals(goalsResult.data.data);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleSupplierChange = (event) => {
        setSelectedSupplier(event.target.value);
    };

    const formatNumber = (num) => {
        if (num === undefined || num === null) return '0';
        return parseFloat(num).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    };

    const getFilteredChartData = () => {
        if (!leadTimeData?.monthlyData) return [];

        return leadTimeData.monthlyData
            .filter(monthData => monthData.averageLeadTime && parseFloat(monthData.averageLeadTime) > 0)
            .map(monthData => {
                if (selectedSupplier === '' || selectedSupplier === 'All Suppliers') {
                    return {
                        month: monthData.month,
                        averageLeadTime: parseFloat(monthData.averageLeadTime) || 0,
                        averageLeadTimeWeeks: daysToWeeks(parseFloat(monthData.averageLeadTime) || 0),
                        orderCount: parseInt(monthData.orderCount) || 0,
                    };
                } else {
                    const supplierData = monthData.suppliers.find(s => s.name === selectedSupplier) || {
                        averageLeadTime: '0',
                        orderCount: 0,
                    };
                    return {
                        month: monthData.month,
                        averageLeadTime: parseFloat(supplierData.averageLeadTime) || 0,
                        averageLeadTimeWeeks: daysToWeeks(parseFloat(supplierData.averageLeadTime) || 0),
                        orderCount: parseInt(supplierData.orderCount) || 0,
                    };
                }
            });
    };
    const getFilteredSupplierPerformance = () => {
        if (!leadTimeData?.supplierPerformance) return [];

        return leadTimeData.supplierPerformance
            .filter(supplier => selectedSupplier === '' || selectedSupplier === 'All Suppliers' || supplier.name === selectedSupplier)
            .map(supplier => ({
                name: supplier.name,
                averageLeadTime: supplier.averageLeadTime === 'NaN' ? 'N/A' : `${parseFloat(supplier.averageLeadTime).toFixed(1)} days (${daysToWeeks(parseFloat(supplier.averageLeadTime)).toFixed(1)} weeks)`,
                orderCount: parseInt(supplier.orderCount) || 0
            }));
    };

    const calculateOverallMetrics = () => {
        if (!leadTimeData) return {
            averageLeadTime: 0,
            averageLeadTimeWeeks: 0,
            totalOrders: 0
        };

        if (selectedSupplier && selectedSupplier !== 'All Suppliers') {
            const supplierData = leadTimeData.supplierPerformance?.find(s => s.name === selectedSupplier);
            if (supplierData) {
                const days = parseFloat(supplierData.averageLeadTime) || 0;
                return {
                    averageLeadTime: days,
                    averageLeadTimeWeeks: daysToWeeks(days),
                    totalOrders: parseInt(supplierData.orderCount) || 0
                };
            }
        }

        return {
            averageLeadTime: parseFloat(leadTimeData.overallAverageLeadTime) || 0,
            averageLeadTimeWeeks: daysToWeeks(parseFloat(leadTimeData.overallAverageLeadTime) || 0),
            totalOrders: parseInt(leadTimeData.totalOrders) || 0
        };
    };

    const calculateTrend = () => {
        if (!leadTimeData?.monthlyData || leadTimeData.monthlyData.length < 2) return 0;
        const relevantData = getFilteredChartData().filter(data => data.averageLeadTime > 0);
        if (relevantData.length < 2) return 0;
        const lastTwo = relevantData.slice(-2);
        return daysToWeeks(lastTwo[1].averageLeadTime - lastTwo[0].averageLeadTime);
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.95)', maxWidth: 250 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>{`Month: ${label}`}</Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Typography variant="body2" color="primary">
                            Average Lead Time: {`${data.averageLeadTime.toFixed(1)} days (${data.averageLeadTimeWeeks.toFixed(1)} weeks)`}
                        </Typography>
                        <Typography variant="body2">
                            Order Count: {formatNumber(data.orderCount)}
                        </Typography>
                    </Box>
                </Paper>
            );
        }
        return null;
    };

    const calculateDomains = (data) => {
        if (!data?.length) return { leadTimeDomain: [0, 100], orderCountDomain: [0, 100] };

        const leadTimeValues = data.map(item => item.averageLeadTime || 0);
        const orderCountValues = data.map(item => item.orderCount || 0);

        const goalMin = kpiGoals?.['Supplier Lead Time']?.min;
        const goalMax = kpiGoals?.['Supplier Lead Time']?.max;

        const goalMinDays = goalMin ? parseFloat(goalMin) * 7 : null;
        const goalMaxDays = goalMax ? parseFloat(goalMax) * 7 : null;

        const allLeadTimeValues = [
            ...leadTimeValues,
            goalMinDays,
            goalMaxDays
        ].filter(Boolean);

        return {
            leadTimeDomain: [0, Math.ceil(Math.max(...allLeadTimeValues) * 1.1)],
            orderCountDomain: [0, Math.ceil(Math.max(...orderCountValues) * 1.1)]
        };
    };

    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: {
            ...kpiGoals?.['Supplier Lead Time'],
            min: kpiGoals?.['Supplier Lead Time']?.min * 7,
            max: kpiGoals?.['Supplier Lead Time']?.max * 7,
            value: kpiGoals?.['Supplier Lead Time']?.value * 7
        },
        yAxisId: "left",
        styles: {
            stroke: CHART_COLORS.goalLine,
            strokeDasharray: "3 3",
            label: {
                fill: CHART_COLORS.goalLine,
                fontSize: 12,
                position: "right"
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Target Min",
            max: "Target Max"
        }
    });

    if (isLoading) return <CircularProgress />;
    if (!leadTimeData || !kpiGoals) return null;

    const filteredChartData = getFilteredChartData();
    const { leadTimeDomain, orderCountDomain } = calculateDomains(filteredChartData);
    const { averageLeadTime, averageLeadTimeWeeks, totalOrders } = calculateOverallMetrics();
    const leadTimeConfig = kpiGoals['Supplier Lead Time'];

    return (
        <ChartExportWrapper title={`Supplier_Lead_Time${selectedSupplier ? `_${selectedSupplier}` : ''}`}>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <FormControl sx={{ minWidth: 200 }}>
                    <InputLabel id="supplier-select-label">Filter by Supplier</InputLabel>
                    <Select
                        labelId="supplier-select-label"
                        id="supplier-select"
                        value={selectedSupplier}
                        onChange={handleSupplierChange}
                        label="Filter by Supplier"
                    >
                        {allSuppliers.map((supplier) => (
                            <MenuItem key={supplier} value={supplier}>
                                {supplier}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            </Box>

            <ResponsiveContainer width="100%" height={400}>
                <ComposedChart data={filteredChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis
                        yAxisId="left"
                        orientation="left"
                        stroke={CHART_COLORS.leadTime}
                        domain={leadTimeDomain}
                        label={{ value: 'Lead Time (Days)', angle: -90, position: 'insideLeft' }}
                    />
                    <YAxis
                        yAxisId="right"
                        orientation="right"
                        stroke={CHART_COLORS.orderCount}
                        domain={orderCountDomain}
                        label={{ value: 'Order Count', angle: 90, position: 'insideRight' }}
                        tickFormatter={formatAxisTick}  // Add this line
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    {goalReferenceLines}
                    <Bar
                        yAxisId="right"
                        dataKey="orderCount"
                        name="Order Count"
                        fill={CHART_COLORS.orderCount}
                    />
                    <Line
                        yAxisId="left"
                        type="monotone"
                        dataKey="averageLeadTime"
                        name="Lead Time (Days)"
                        stroke={CHART_COLORS.leadTime}
                        strokeWidth={2}
                        dot={{ r: 4, fill: CHART_COLORS.leadTime }}
                        activeDot={{ r: 8 }}
                    />
                </ComposedChart>
            </ResponsiveContainer>


            <Box sx={{ mt: 4 }}>
                <Typography variant="h6" gutterBottom>Performance Summary</Typography>
                <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={4}>
                        <KPICard
                            title="Average Lead Time (Days)"
                            value={`${averageLeadTime.toFixed(1)} days`}
                            bgColor="#fff0f0"  // Light red background
                            textColor="#ff6b6b"  // Coral red text
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                        <KPICard
                            title="Average Lead Time (Weeks)"
                            value={`${averageLeadTimeWeeks.toFixed(1)} weeks`}
                            bgColor="#fdf6e3"  // Warm yellow background
                            textColor="#f59f00"  // Orange text
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                        <KPICard
                            title="Total Orders"
                            value={formatNumber(totalOrders)}
                            bgColor={CHART_COLORS.lightPurple}  // Light blue background
                            textColor={CHART_COLORS.orderCount}  // Blue text
                        />
                    </Grid>
                </Grid>
            </Box>

            <Box sx={{ mt: 4 }}>
                <GoalStatusDisplay
                    currentValue={averageLeadTimeWeeks}
                    goalConfig={leadTimeConfig}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculateTrend()}
                    size="medium"
                    title={selectedSupplier && selectedSupplier !== 'All Suppliers'
                        ? `${selectedSupplier} Lead Time Performance`
                        : "Overall Supplier Lead Time Performance"
                    }
                />
            </Box>

            <Box sx={{ mt: 4 }}>
                <Typography variant="h6" gutterBottom>Supplier Performance</Typography>
                <TableContainer component={Paper}>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell>Supplier</TableCell>
                                <TableCell align="right">Average Lead Time</TableCell>
                                <TableCell align="right">Total Orders</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {getFilteredSupplierPerformance().map((supplier) => (
                                <TableRow key={supplier.name}>
                                    <TableCell component="th" scope="row">
                                        {supplier.name}
                                    </TableCell>
                                    <TableCell align="right">{supplier.averageLeadTime}</TableCell>
                                    <TableCell align="right">{formatNumber(supplier.orderCount)}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            </Box>
        </ChartExportWrapper>
    );
};

export default SupplierLeadTimeReport;