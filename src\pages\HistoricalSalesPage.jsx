import React from 'react';
import { useUser } from '../contexts/UserContext';
import HistoricalSalesContent from '../components/forecast/HistoricalSalesContent';

const HistoricalSalesPage = () => {
  const { userData } = useUser();

  // Check if user has permission to access historical sales
  if (!userData || !userData.userPermissions) {
    return (
      <div style={{ 
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <div>Loading user permissions...</div>
      </div>
    );
  }

  const hasHistoricalSalesPermission = userData.userPermissions.some(
    permission => permission.technicalName === 'read:historicalSales' && permission.hasAccess
  );

  if (!hasHistoricalSalesPermission) {
    return (
      <div style={{ 
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <div style={{ textAlign: 'center' }}>
          <h2>Access Denied</h2>
          <p>You do not have permission to access the Historical Sales page.</p>
          <p>Please contact your administrator if you believe this is an error.</p>
        </div>
      </div>
    );
  }

  return <HistoricalSalesContent initialSelection={{ upcs: [], forecastNodes: [] }} />;
};

export default HistoricalSalesPage;
