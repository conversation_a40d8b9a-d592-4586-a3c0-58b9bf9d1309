/* eslint-disable guard-for-in */
import React, { useCallback, useEffect, useState, lazy, Suspense, memo } from 'react';
import { db, api } from './firebase';
import Layout, { Content } from 'antd/es/layout/layout';
import Title from 'antd/es/typography/Title';
import Text from 'antd/es/typography/Text';
import { Button, DatePicker, Input, message, Select, Space, Table } from 'antd';
import dayjs from 'dayjs';
const OpsForecastDetailModal = lazy(() => import('../components/OpsForecasDetailModal'));
const AutoPopulateModal = lazy(() => import('../components/OpsForecastAutoPopulateModal'));
const OpsForecastAddModal = lazy(() => import('../components/OpsForecastAddChannelModal'));
import { PlusOutlined, DeleteOutlined, LockFilled, UnlockFilled } from '@ant-design/icons';
import { getDocs, collection, addDoc } from 'firebase/firestore';
import { debounce, set } from 'lodash';


const dateFormat = 'YYYY-MM';
// auto populate anyhting with a launch date
// if phasing out and no inventory or inbound remove from forecast
// update historical when month changes
// update historical by channel
// autopopulate duplicating data
// add notes to variants
// update column headers to be clearer and italics

const OpsForecast = ({userObj}) => {
  // states
  const [loadingForecast, setLoadingForecast] = useState(true);
  const [loadingHistorical, setLoadingHistorical] = useState(true);
  const [selectedMonth, setSelectedMonth] = useState(dayjs(new Date().toISOString().slice(0, 7), dateFormat));
  const [forecastData, setForecastData] = useState({});
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState(false);
  const [addObject, setAddObject] = useState({});
  const [selectedBrand, setSelectedBrand] = useState('');
  const [selectedProductTypes, setSelectedProductTypes] = useState([]);
  const [selectedLifeStatuses, setSelectedLifeStatuses] = useState([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);
  const [expandedChannelKeys, setExpandedChannelKeys] = useState([]);
  const [showAutoPopulateModal, setShowAutoPopulateModal] = useState(false);
  const [selectedSpecifications, setSelectedSpecifications] = useState([]);
  const [historicalData, setHistoricalData] = useState({});
  const [showAddChannelModal, setShowAddChannelModal] = useState(false);
  const [allLocked, setAllLocked] = useState(false);
  // const [brands, setBrands] = useState([]);  
  // calculated values
  const monthStr = `${selectedMonth.$y}-${String(selectedMonth.$M + 1).padStart(2, '0')}`;
  // console.log('monthStr', monthStr, selectedMonth);
  const [lists, setLists] = useState({});


  const fetchOpsForecast = async () => {
    // console.log('monthStr', monthStr);
    setLoadingForecast(true);
    const forecastQuery = `SELECT
      i.sku,
      i.upc,
      i.netsuiteId,
      p.brand,
      p.name AS productName,
      i.name,
      i.lifeStatus,
      i.productSpecification,
      CASE 
        WHEN opsForecast.salesChannel = 'Wholesale' THEN ROUND(i.basePrice / 2, 2)
        ELSE i.basePrice
      END as basePrice,
      opsForecast.month,
      opsForecast.salesChannel,
      opsForecast.uniqueKey,
      opsForecast.forecast,
      opsForecast.locked
      FROM \`hj-reporting.forecasts.opsForecast\` AS opsForecast
      LEFT JOIN \`hj-reporting.items.variants\` AS i ON i.upc = opsForecast.upc
      LEFT JOIN \`hj-reporting.items.products\` AS p ON p.id = i.productId
      WHERE opsForecast.month = '${monthStr}'
    ;`;
    const { data } = await api.runQueryOnCall({ options: { query: forecastQuery } });
    // console.log('data', data);
    if (typeof data === 'undefined' || typeof data === 'string') {
      alert('Error fetching data: '+data);
    }
    const revTotal = data.reduce((acc, x) => acc += x.forecast * x.basePrice, 0);
    const fdata = data.reduce((acc, x) => {
      if (!acc[x.productType]) {
        acc[x.productType] = {
          productType: x.productType,
          brand: x.brand,
          revenue: 0,
          forecast: 0,
          percent: 0,
          variants: {},
        };
      };
      if (!acc[x.productType].variants[x.upc]) {
        acc[x.productType].variants[x.upc] = {
          upc: x.upc,
          description: x.description,
          productType: x.productType,
          lifeStatus: x.lifeStatus,
          productSpecification: x.productSpecification,
          revenue: 0,
          forecast: 0,
          percent: 0,
          channels: {},
        };
      }
      if (!acc[x.productType].variants[x.upc].channels[x.salesChannel]) {
        acc[x.productType].variants[x.upc].channels[x.salesChannel] = {
          ...x,
          revenue: x.forecast * x.basePrice,
          percent: 0,
        };
      }
      acc[x.productType].forecast += x.forecast;
      acc[x.productType]['revenue'] += x.forecast * x.basePrice;
      acc[x.productType].percent += (x.forecast * x.basePrice / revTotal) * 100;
      acc[x.productType].variants[x.upc].forecast += x.forecast;
      acc[x.productType].variants[x.upc].revenue += x.forecast * x.basePrice;
      acc[x.productType].variants[x.upc].percent += (x.forecast * x.basePrice / revTotal) * 100;
      acc[x.productType].variants[x.upc].channels[x.salesChannel].percent = (x.forecast * x.basePrice / revTotal) * 100;
      return acc;
    }, {});
    setForecastData(fdata);
    // setBrands([...new Set(data.map((x) => x.brand))]);
    setLoadingForecast(false);
  };
  const saveChanges = async ({ lock }) => {
    message.loading('Saving Forecast', 0);
    try {
      await api.runQueryOnCall({ options: { query: `DELETE FROM \`hj-reporting.forecasts.opsForecast\` WHERE month = "${monthStr}"` } });
      const forecastDataArr = [];
      for (const productType in forecastData) {
        for (const upc in forecastData[productType].variants) {
          for (const salesChannel in forecastData[productType].variants[upc].channels) {
            const channel = forecastData[productType].variants[upc].channels[salesChannel];
            forecastDataArr.push({
              upc: channel.upc,
              month: monthStr,
              salesChannel: channel.salesChannel,
              forecast: channel.forecast,
              uniqueKey: channel.uniqueKey,
              locked: lock
            });
          }
        }
      }
      await api.upsertOnCall({ datasetId: 'forecasts', table: 'opsForecast', key: 'uniqueKey', rows: forecastDataArr });
      message.destroy();
      fetchOpsForecast();
      message.success('Forecast Saved', { duration: 2 });
    } catch (error) {
      message.error('Error Saving Forecast', { duration: 2 });
      console.error('error', error);
    } finally {
      message.destroy();
    }
  };
  const saveForecast = async () => {
    let dataToSave = { forecastData, month: monthStr, asOfDate: new Date(), savedBy: userObj.email };
    // remove all undefined values
    dataToSave = JSON.parse(JSON.stringify(dataToSave));
    // console.log('saveForecast', dataToSave);
    addDoc(collection(db, 'savedForecasts'), dataToSave);
  };
  const lockRows = async ({ rowIds, locked }) => {
    // message.loading('Locking Rows', 0);
    try {
      setForecastData((prev) => {
        const newForecastData = { ...prev };
        for (const productType in newForecastData) {
          for (const upc in newForecastData[productType].variants) {
            for (const salesChannel in newForecastData[productType].variants[upc].channels) {
              const channel = newForecastData[productType].variants[upc].channels[salesChannel];
              if (rowIds.includes(channel.uniqueKey)) {
                channel.locked = locked;
              }
            }
          }
        }
        return newForecastData;
      });
      await api.upsertOnCall({ datasetId: 'forecasts', table: 'opsForecast', key: 'uniqueKey', rows: rowIds.map((x) => ({ uniqueKey: x, locked: locked })) });
      // message.destroy();
    } catch (error) {
      message.error('Error Locking Rows', { duration: 2 });
      console.error('error', error);
    } finally {
      message.destroy();
    }
  };
  const fetchHistoricalData = async () => {
    setLoadingHistorical(true);
    let whereClause = '';
    const baseQuery = `SELECT
      i.product as productType,
      s.upc as upc,
      CASE
        WHEN s.salesChannel = 'Wholesale' THEN ROUND(i.basePrice / 2, 2)
        ELSE i.basePrice
      END as basePrice,      
      SUM(s.quantity) as qty,
      CASE
        WHEN s.salesChannel = 'Wholesale' THEN 'Wholesale'
        WHEN s.salesChannel = 'Shopify US' THEN 'Shopify'
        WHEN s.salesChannel = 'Shopify' THEN 'Shopify'
        WHEN s.salesChannel = 'HydroJug US' THEN 'Shopify'
        WHEN s.salesChannel = 'Wholesale Inbound' THEN 'Wholesale'
        WHEN s.salesChannel = 'Wholesale Outbound' THEN 'Wholesale'
        WHEN s.salesChannel = 'TikTok Shops' THEN 'TikTok'
        WHEN s.salesChannel = 'Amazon' THEN 'Amazon'
        WHEN s.salesChannel = '- None -' THEN 'Wholesale'
        WHEN s.salesChannel = 'Shopify CA' THEN 'Shopify'
        WHEN s.salesChannel = 'Shopify UK' THEN 'Shopify'
        WHEN s.salesChannel = 'Shopify AU' THEN 'Shopify'
        ELSE s.salesChannel
      END as salesChannel,
      FROM \`hj-reporting.sales.dailySalesByChannel\` 
      AS s
      LEFT JOIN \`hj-reporting.items.variants\` AS i ON s.upc = i.upc
      {whereClause}
      GROUP BY i.product, upc, salesChannel,basePrice`;
    const lastMonthDt = dayjs().subtract(1, 'month');
    const startDate = lastMonthDt.startOf('month').format('YYYY-MM-DD');
    const endDate = lastMonthDt.endOf('month').format('YYYY-MM-DD');
    whereClause = `WHERE s.soDate >= '${startDate}' AND s.soDate <= '${endDate}'`;
    const lastMonthQuery = baseQuery.replace('{whereClause}', whereClause);
    const lastYearSameMonthDt = dayjs().subtract(1, 'year');
    // eslint-disable-next-line max-len
    whereClause = `WHERE s.soDate >= '${lastYearSameMonthDt.startOf('month').format('YYYY-MM-DD')}' AND s.soDate <= '${lastYearSameMonthDt.endOf('month').format('YYYY-MM-DD')}'`;
    const lastYearSameMonthQuery = baseQuery.replace('{whereClause}', whereClause);
    const lastNinetyDaysDt = dayjs().subtract(90, 'days');
    whereClause = `WHERE s.soDate >= '${lastNinetyDaysDt.format('YYYY-MM-DD')}' AND s.soDate <= '${dayjs().format('YYYY-MM-DD')}'`;
    const lastNinetyDaysQuery = baseQuery.replace('{whereClause}', whereClause);
    const queries = [lastMonthQuery, lastYearSameMonthQuery, lastNinetyDaysQuery];
    const queryProms = [];
    for (const q of queries) {
      queryProms.push(api.runQueryOnCall({ options: { query: q } }));
    }
    const results = await Promise.all(queryProms);
    const keys = ['lastMonth', 'lastYearSameMonth', 'lastNinetyDays'];
    const tempHistoricalData = {};
    for (let i = 0; i < keys.length; i++) {
      const data = results[i].data;
      // console.log('data', data);
      const tempObj = {};
      for (const r of data) {
        if (!tempObj[r.productType]) {
          tempObj[r.productType] = {
            qty: 0,
            variants: {},
          };
        }
        if (!tempObj[r.productType].variants[r.upc]) {
          tempObj[r.productType].variants[r.upc] = {
            qty: 0,
            channels: {},
          };
        }
        if (!tempObj[r.productType].variants[r.upc].channels[r.salesChannel]) {
          tempObj[r.productType].variants[r.upc].channels[r.salesChannel] = 0;
        }
        tempObj[r.productType].variants[r.upc].channels[r.salesChannel] += r.qty / (i === 2 ? 3 : 1);
        tempObj[r.productType].variants[r.upc].qty += r.qty / (i === 2 ? 3 : 1);
        tempObj[r.productType].qty += r.qty / (i === 2 ? 3 : 1);
      }
      tempHistoricalData[keys[i]] = tempObj;
    }
    setHistoricalData(tempHistoricalData);
    setLoadingHistorical(false);
  };
  const fetchLists = async () => {
    try {
      const listSnap = await getDocs(collection(db, 'lists'));
      const tempLists = {};
      listSnap.forEach((doc) => {
        tempLists[doc.id] = doc.data().items;
      });
      setLists(tempLists);
    } catch (error) {
      alert('Error fetching lists: '+error);
    }
  };
  useEffect(() => {
    // console.log('useEffect');
    fetchOpsForecast();
    fetchHistoricalData();
    fetchLists();
  }, [monthStr]);
  useEffect(() => {
    setAllLocked(Object.values(forecastData).every((x) => Object.values(x.variants).every((y) => Object.values(y.channels).every((z) => z.locked))));
  }, [forecastData]);

  const filterData = (d) => {
    // console.log('d', d, selectedBrand, selectedProductTypes, selectedLifeStatuses);
    if (selectedBrand && d.brand && d.brand !== selectedBrand) return false;
    if (selectedProductTypes.length > 0 && d.product && !selectedProductTypes.includes(d.product)) return false;
    if (selectedSpecifications.length > 0 && !selectedSpecifications.includes(d.productSpecification)) return false;
    if (selectedLifeStatuses.length > 0 && d.lifeStatus && !selectedLifeStatuses.includes(d.lifeStatus)) return false;
    return true;
  };
  const getHistoricalData = (key, level, rec) => {
    if (loadingHistorical) {
      return 'Loading...';
    }
    if (level === 'product') {
      if (!historicalData[key] || !historicalData[key][rec.productType]) {
        return 0;
      }
      return historicalData[key][rec.productType].qty.toLocaleString('en-US', { maximumFractionDigits: 0 });
    } else if (level === 'variant') {
      if (!historicalData[key] || !historicalData[key][rec.productType] || !historicalData[key][rec.productType].variants[rec.upc]) {
        return 0;
      }
      return historicalData[key][rec.productType].variants[rec.upc].qty.toLocaleString('en-US', { maximumFractionDigits: 0 });
    } else if (level === 'channel') {
      // eslint-disable-next-line max-len
      if (!historicalData[key] || !historicalData[key][rec.productType] || !historicalData[key][rec.productType].variants[rec.upc] || !historicalData[key][rec.productType].variants[rec.upc].channels[rec.salesChannel]) {
        return 0;
      }
      return historicalData[key][rec.productType].variants[rec.upc].channels[rec.salesChannel].toLocaleString('en-US', { maximumFractionDigits: 0 });
    }
  };
  const totalRev = Object.values(forecastData).reduce((acc, x) => acc += x.revenue, 0);

  const handleInputChange = useCallback(debounce((value, rec) => {
    setForecastData((prev) => {
      const newForecastData = { ...prev };
      const productType = newForecastData[rec.productType];
      const variant = productType.variants[rec.upc];
      const channel = variant.channels[rec.salesChannel];

      // Update the forecast value
      const qtyDiff = parseInt(value) - channel.forecast;
      const revDiff = qtyDiff * channel.basePrice;
      const totalRev = Object.values(newForecastData).reduce((acc, x) => acc += x.revenue, 0) + revDiff;
      channel.forecast = parseInt(value);
      channel.revenue += revDiff;
      channel.percent = (channel.revenue / totalRev) * 100;
      // Update the variant forecast
      variant.forecast += qtyDiff;
      variant.revenue += revDiff;
      variant.percent = (variant.revenue / totalRev) * 100;
      // Update the product type forecast
      productType.forecast += qtyDiff;
      productType.revenue += revDiff;
      productType.percent = (productType.revenue / totalRev) * 100;

      return newForecastData;
    });
  }, 300), []);
  const productColumns = [
    {
      title: (val, rec) => (
        <Space>
          <Text>Product</Text>
          {!loadingForecast && (
            <PlusOutlined
              onClick={() => {
                setAddObject({ addType: 'Product' });
                setShowAddChannelModal(true);
              }}
            ></PlusOutlined>
          )}
        </Space>
      ), dataIndex: 'productType', key: 'productType', render: (val, rec) => (
        <>
          <Space>
            <Text>
              {val}
            </Text>
            <DeleteOutlined onClick={async () => {
              setForecastData((prev) => {
                const newForecastData = { ...prev };
                delete newForecastData[rec.productType];
                return newForecastData;
              });
            }}
            />
          </Space>
        </>
      ),
    },
    {
      title: dayjs(selectedMonth).subtract(1, 'year').format('MMM YYYY'),
      dataIndex: 'productType',
      key: 'productType',
      render: (val, rec) => getHistoricalData('lastYearSameMonth', 'product', rec),
    },
    {
      title: 'Last 90', dataIndex: 'productType', key: 'productType', render: (val, rec) => getHistoricalData('lastNinetyDays', 'product', rec),
    },
    {
      title: dayjs(selectedMonth).subtract(1, 'month').format('MMM YYYY'),
      dataIndex: 'productType', key: 'productType', render: (val, rec) => getHistoricalData('lastMonth', 'product', rec),
    },
    { title: 'Quantity', dataIndex: 'forecast', key: 'forecast', render: (val) => val.toLocaleString() },
    { title: 'Percent', dataIndex: 'percent', key: 'percent', render: (val) => `${val.toFixed(2)}%` },
    {
      title: 'Revenue', dataIndex: 'revenue', key: 'revenue', render: (val) =>
        new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(val),
    },
  ];
  const variantColumns = [
    { title: 'Product', dataIndex: 'productType', key: 'productType', hidden: true },
    {
      title: 'Color',
      dataIndex: 'description',
      key: 'sku',
      render: (val, rec) => (
        <>
          <Space>
            <Text
              onClick={() => {
                setSelectedVariant(rec);
                setIsModalVisible(true);
              }}
              style={{ cursor: 'pointer', textDecoration: 'underline', color: 'blue', fontWeight: 'bold' }}>
              {val}
            </Text>
            <DeleteOutlined onClick={async () => {
              setForecastData((prev) => {
                const newForecastData = { ...prev };
                newForecastData[rec.productType].forecast -= rec.forecast;
                newForecastData[rec.productType].revenue -= rec.revenue;
                delete newForecastData[rec.productType].variants[rec.upc];
                return newForecastData;
              });
            }}
            />
          </Space>
        </>
      ),
    },
    { title: 'UPC', dataIndex: 'upc', key: 'upc' },
    {
      title: dayjs(selectedMonth).subtract(1, 'year').format('MMM YYYY'),
      dataIndex: 'upc',
      key: 'upc',
      render: (val, rec) => getHistoricalData('lastYearSameMonth', 'variant', rec),
    },
    {
      title: 'Last 90', dataIndex: 'upc', key: 'upc', render: (val, rec) => getHistoricalData('lastNinetyDays', 'variant', rec),
    },
    {
      title: dayjs(selectedMonth).subtract(1, 'month').format('MMM YYYY'),
      dataIndex: 'upc', key: 'upc', render: (val, rec) => getHistoricalData('lastMonth', 'variant', rec),
    },
    { title: 'Quantity', dataIndex: 'forecast', key: 'forecast', render: (val) => val.toLocaleString() },
    { title: 'Percent', dataIndex: 'percent', key: 'percent', render: (val) => `${val.toFixed(2)}%` },
    {
      title: 'Revenue', dataIndex: 'revenue', key: 'revenue', render: (val) =>
        new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(val),
    },
  ];
  const channelColumns = [
    { title: 'uniqueKey', dataIndex: 'uniqueKey', key: 'uniqueKey', hidden: true },
    {
      title: 'Locked', dataIndex: 'locked', key: 'locked', hidden: false, render: (val, rec) => {
        return (
          <Button
            type={val ? 'primary' : 'default'}
            onClick={() => {
              lockRows({ rowIds: [rec.uniqueKey], locked: !val });
            }}
          >
            {val? 'Unlock' : 'Lock'}
            {val ? <UnlockFilled/> : <LockFilled/>}
          </Button>
        );
      }
    }, {
      title: 'Channel', dataIndex: 'salesChannel', key: 'salesChannel', render: (val, rec) => (
        <>
          <Text>{val}</Text>
          <DeleteOutlined onClick={async () => {
            setForecastData((prev) => {
              const newForecastData = { ...prev };
              newForecastData[rec.productType].variants[rec.upc].forecast -= rec.forecast;
              newForecastData[rec.productType].variants[rec.upc].revenue -= rec.revenue;
              newForecastData[rec.productType].forecast -= rec.forecast;
              newForecastData[rec.productType].revenue -= rec.revenue;
              delete newForecastData[rec.productType].variants[rec.upc].channels[rec.salesChannel];
              return newForecastData;
            });
          }}
          />
        </>
      ),
    },
    {
      title: dayjs(selectedMonth).subtract(1, 'year').format('MMM YYYY'),
      dataIndex: 'uniqueKey',
      key: 'uniqueKey',
      render: (val, rec) => getHistoricalData('lastYearSameMonth', 'channel', rec),
    },
    {
      title: 'Last 90', dataIndex: 'upc', key: 'upc', render: (val, rec) => getHistoricalData('lastNinetyDays', 'channel', rec),
    },
    {
      title: dayjs(selectedMonth).subtract(1, 'month').format('MMM YYYY'),
      dataIndex: 'upc', key: 'upc', render: (val, rec) => getHistoricalData('lastMonth', 'channel', rec),
    },
    {
      title: 'Quantity', dataIndex: 'forecast', key: 'forecast', render: (qty, rec) => {
        return (
          <Input
            disabled={rec.locked}
            type='number'
            defaultValue={qty}
            style={{ width: '100px', border: `1px solid ${qty <= 0 ? 'red' : 'black'}` }}
            onBlur={(e) => {
              handleInputChange(e.target.value, rec);
            }}
          />)
          ;
      },
    },
    {
      title: 'Price', dataIndex: 'basePrice', key: 'basePrice', render: (val) =>
        new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(val),
    },
    { title: 'Percent', dataIndex: 'percent', key: 'percent', render: (val) => `${val.toFixed(2)}%` },
    {
      title: 'Revenue', dataIndex: 'revenue', key: 'revenue', render: (val) =>
        new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(val),
    },
  ];

  const channelRowRender = (v) => (
    <Table
      columns={channelColumns}
      dataSource={Object.values(v.channels).filter(filterData)}
      pagination={false}
      size="small"
      rowKey={(record) => record.uniqueKey}
    />
  );

  const variantRowRender = (p) => (
    // console.log('p', p),
    <Table
      columns={variantColumns}
      dataSource={Object.values(p.variants).filter(filterData).sort((a, b) => b.forecast - a.forecast)}
      pagination={false}
      size="small"
      rowKey={(record) => record.upc}
      expandable={{
        expandedRowRender: channelRowRender, defaultExpandedRowKeys: ['0'], onExpand: (expanded, record) => {
          // console.log('expanded', record);
          setExpandedChannelKeys(expanded ? [record.upc] : []);
        }, expandedRowKeys: expandedChannelKeys,
      }}
    />
  );
  // eslint-disable-next-line react/display-name
  const MemoizedProductTable = memo((props) => (
    <Table
      sticky
      scroll={{ x: 1000, y: 'calc(100vh - 200px)' }}
      columns={props.productColumns}
      dataSource={props.productData.filter(filterData)}
      expandable={{
        expandedRowRender: props.variantRowRender,
        defaultExpandedRowKeys: ['0'],
        onExpand: (expanded, record) => {
          // console.log('expanded', record);
          props.setExpandedRowKeys(expanded ? [record.productType] : []);
        },
        expandedRowKeys: props.expandedRowKeys,
      }}
      size="small"
      pagination={false}
      rowKey={(record) => record.productType}
    />
  ));
  // const computePercent = () => {
  //   const revTotal = Object.values(forecastData).reduce((acc, x) => acc += x.revenue, 0);
  //   const fdata = {...forecastData};
  //   for (const productType in fdata) {
  //     fdata[productType].percent = (fdata[productType].revenue / revTotal) * 100;
  //     for (const upc in fdata[productType].variants) {
  //       fdata[productType].variants[upc].percent = (fdata[productType].variants[upc].revenue / revTotal) * 100;
  //       for (const salesChannel in fdata[productType].variants[upc].channels) {
  //         fdata[productType].variants[upc].channels[salesChannel].percent = (fdata[productType].variants[upc].channels[salesChannel].revenue / revTotal) * 100;
  //       }
  //     }
  //   }
  //   setForecastData(fdata);
  // };

  return (
    <div>
      <Layout>
        <Title>Operations Forecast</Title>
        <Content>
          <Space direction="horizontal" style={{ paddingLeft: '25px', paddingRight: '25px', minWidth: '95%' }}>
            <Space direction="vertical" size={'small'} >
              <label htmlFor="month">Select Month</label>
              <DatePicker
                id='month'
                picker="month"
                defaultValue={selectedMonth}
                // minDate={dayjs(new Date().toISOString().slice(0, 7), dateFormat)}
                onChange={(date, dateString) => {
                  // console.log('dateString', dateString, date);
                  setSelectedMonth(date);
                  // setMonthStr(dateString);
                }}
              />
            </Space>
            <Space direction="vertical" size={'small'}>
              <label htmlFor="totalRev">Revenue</label>
              <Text id="totalRev">{totalRev.toLocaleString()}</Text>
            </Space>
            <Space direction="vertical" size={'small'}>
              <label htmlFor="brand">Select Brand</label>
              <Select
                id='brand'
                placeholder="Select Brand"
                style={{ width: 200 }}
                onChange={(value) => setSelectedBrand(value)}
                allowClear
                options={lists.brands ? lists.brands.map((b) => ({ label: b, value: b })) : []}
              />
            </Space>
            <Space direction="vertical" size={'small'}>
              <label htmlFor="productType">Product Type</label>
              <Select
                id='productType'
                placeholder="Select Product"
                style={{ width: 200 }}
                mode='multiple'
                value={selectedProductTypes}
                onChange={(value) => setSelectedProductTypes(value)}
                options={lists.productTypes ? lists.productTypes.map((b) => ({ label: b, value: b })) : []}
              />
            </Space>
            <Space direction="vertical" size={'small'}>
              <label htmlFor="prodSpec">Product Specification</label>
              <Select
                id='prodSpec'
                placeholder="Select Specifications"
                style={{ width: 200 }}
                mode='multiple'
                onChange={(value) => setSelectedSpecifications(value)}
                value={selectedSpecifications}
                options={lists.productSpecifications ? productSpecifications.map((b) => ({ label: b, value: b })) : []}
              />
            </Space>
            <Space direction="vertical" size={'small'}>
              <label htmlFor="lifeStatus">Life Status</label>
              <Select
                id='lifeStatus'
                placeholder="Select Life Status"
                style={{ width: 200 }}
                mode='multiple'
                value={selectedLifeStatuses}
                onChange={(value) => setSelectedLifeStatuses(value)}
                options={lists.lifeStatuses ? lists.lifeStatuses.map((b) => ({ label: b, value: b })) : []}
              />
            </Space>
            <Space direction='vertical' size={'small'}>
              <Button type="primary"
                onClick={() => {
                  setShowAutoPopulateModal(true);
                }}
              >Auto Populate</Button>
            </Space>
            {/* <Space direction='vertical' size={'small'}>
              <Button type="primary"
                onClick={() => {
                  computePercent();
                }}
              >Compute Percent</Button>
            </Space> */}
            <Space direction='vertical' size={'small'}>
              <Button type="secondary"
                onClick={async () => {
                  message.loading('Clearing Forecast', 0);
                  await api.runQueryOnCall({
                    options: {
                      query:
                        // eslint-disable-next-line max-len
                        `DELETE FROM \`hj-reporting.forecasts.opsForecast\` WHERE month = "${monthStr}"`,
                    },
                  });
                  await fetchOpsForecast();
                  message.destroy();
                  message.success('Forecast Cleared', { duration: 2 });
                  message.destroy();
                }}
              >Clear Forecast</Button>
            </Space>
            <Space direction='vertical' size={'small'}>
              <Button type="primary"
                onClick={async () => {
                  saveChanges({ lock: false });
                }}
              >Save Changes</Button>
            </Space>
            <Space direction='vertical' size={'small'}>
              <Button type="primary"
                onClick={async () => {
                  lockRows({ rowIds: Object.values(forecastData).flatMap((x) => Object.values(x.variants).flatMap((y) => Object.values(y.channels).map((z) => z.uniqueKey))), locked: !allLocked });
                }}
              >{allLocked ? 'Unlock' : 'Lock'} Forecast</Button>
            </Space>
            <Space direction='vertical' size={'small'}>
              <Button type="primary"
                onClick={async () => {
                  saveForecast();
                }}
              >Save Forecast</Button>
            </Space>
          </Space>
          {loadingForecast && <h2>Loading...</h2>}
          {!loadingForecast && (
            <MemoizedProductTable
              productColumns={productColumns}
              productData={Object.values(forecastData).sort((a, b) => b.forecast - a.forecast)}
              filterData={filterData}
              variantRowRender={variantRowRender}
              expandedRowKeys={expandedRowKeys}
              setExpandedRowKeys={setExpandedRowKeys}
            />
          )}
        </Content>
      </Layout>
      <Suspense fallback={<div>Loading...</div>}>
        <OpsForecastDetailModal
          visible={isModalVisible}
          setVisible={setIsModalVisible}
          selectedVariant={selectedVariant}
          selectedMonth={selectedMonth} />
        <AutoPopulateModal
          visible={showAutoPopulateModal}
          setVisible={setShowAutoPopulateModal}
          selectedMonth={selectedMonth}
          fetchOpsForecast={fetchOpsForecast}
          api={api}
        />
        {showAddChannelModal && (
          <OpsForecastAddModal
            open={showAddChannelModal}
            setOpen={setShowAddChannelModal}
            addObject={addObject}
            monthStr={monthStr}
            setForecastData={setForecastData}
            setLoadingForecast={setLoadingForecast}
          />)}
      </Suspense>
    </div>
  );
};
export default OpsForecast;
