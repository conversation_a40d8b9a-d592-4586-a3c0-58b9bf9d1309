import React, {useEffect, useState} from 'react';
import {
  Modal, Select, Divider, Space,
  // Input, Button,
  message, Table,
  Checkbox,
} from 'antd';
// import {PlusOutlined, DeleteOutlined} from '@ant-design/icons';
// import Text from 'antd/es/typography/Text';
import {api} from '../pages/firebase';

const OpsForecastAddModal = ({
  open,
  setOpen,
  addObject,
  monthStr,
  setForecastData,
}) => {
  // const salesChannelOptions = ['Shopify', 'TikTok', 'Amazon', 'Wholesale'];
  const brandOptions = ['HydroJug', 'ACTA', 'PureLyte'];
  const lifeStatusOptions = ['active', 'phasingOut', 'launching'];
  const [colorOptions, setColorOptions] = useState(['Black']);
  const [productOptions, setProductOptions] = useState(['Bottle']);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [selectedColor, setSelectedColor] = useState();
  const [selectedBrands, setSelectedBrands] = useState([]);
  const [selectedLifeStatus, setSelectedLifeStatus] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [showSelected, setShowSelected] = useState(false);
  const [salesChannelOptions, setSalesChannelOptions] = useState([]);
  const [allData, setAllData] = useState([]);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        if (!open) return;
        const res = await api.runQueryOnCall({
          options: {
            query: `SELECT
              items.upc,
              items.brand,
              items.productType,
              items.description,
              items.lifeStatus,
              items.productSpecification,
              CASE
                WHEN opsForecast.salesChannel = 'Wholesale' THEN ROUND(items.basePrice / 2, 2)
                ELSE items.basePrice
              END as basePrice,
              ARRAY_AGG(IFNULL(opsForecast.salesChannel, '')) AS salesChannels
              FROM \`hj-reporting.items.variants\` AS items
              LEFT JOIN \`hj-reporting.forecasts.opsForecast\` AS opsForecast ON items.upc = opsForecast.upc
              WHERE items.upc IS NOT NULL
              GROUP BY items.upc, items.brand, items.productType, items.description, items.lifeStatus, items.productSpecification,basePrice
          `,
          },
        });
        const netsuiteSalesChannels = await api.makeSuiteQlQuery({ query: 'select s.id id, s.name channelname from saleschannel s', paginate: true });
        setSalesChannelOptions(netsuiteSalesChannels.data.map((x) => x.channelname));
        setAllData([...res.data.map((x)=>({...x, salesChannels: x.salesChannels.filter((y)=>y)}))]);
        const uniqueColors = new Set(res.data.map((x) => x.description));
        setColorOptions([...uniqueColors]);
        const uniqueProducts = new Set(res.data.map((x) => x.productType));
        setProductOptions([...uniqueProducts]);
      } catch (error) {
        console.error(error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [open]);
  const filterData = (x) => {
    let showRow = true;
    if (selectedBrands && selectedBrands.length > 0 && !selectedBrands.includes(x.brand)) {
      showRow = false;
    };
    if (selectedLifeStatus && selectedLifeStatus.length > 0 && !selectedLifeStatus.includes(x.lifeStatus)) {
      showRow = false;
    };
    if (selectedColor && selectedColor.length > 0 && !selectedColor.includes(x.description)) {
      showRow = false;
    };
    if (selectedProducts && selectedProducts.length > 0 && !selectedProducts.includes(x.productType)) {
      showRow = false;
    };
    if (showSelected && !selectedRowKeys.includes(x.upc)) {
      showRow = false;
    };
    return showRow;
  };
  const columns = [
    {title: 'Brand', dataIndex: 'brand', key: 'brand'},
    {title: 'Product Type', dataIndex: 'productType', key: 'productType', sorter: (a, b) => a.productType.localeCompare(b.productType)},
    {title: 'Color', dataIndex: 'description', key: 'description', sorter: (a, b) => a.description.localeCompare(b.description)},
    {title: 'UPC', dataIndex: 'upc', key: 'upc', sorter: (a, b) => a.upc.localeCompare(b.upc)},
    {title: 'Base Price', dataIndex: 'basePrice', key: 'basePrice', sorter: (a, b) => a.basePrice.localeCompare(b.basePrice?b.basePrice:0)},
    {title: 'Launch Date', dataIndex: 'launchDate', key: 'launchDate', sorter: (a, b) => a.launchDate.localeCompare(b.launchDate)},
    {title: 'End Date', dataIndex: 'endDate', key: 'endDate', sorter: (a, b) => a.endDate.localeCompare(b.endDate)},
    {title: 'Life Status', dataIndex: 'lifeStatus', key: 'lifeStatus', sorter: (a, b) => a.lifeStatus.localeCompare(b.lifeStatus)},
    {
      title: 'Sales Channels', dataIndex: 'salesChannel', key: 'salesChannel', sorter: (a, b) => a.salesChannel.localeCompare(b.salesChannel),
      render: (text, rec) => {
        return (
          <Select
            id="salesChannelSelect"
            style={{width: 150}}
            allowClear
            options={salesChannelOptions && salesChannelOptions.length>0?salesChannelOptions.map((x) => ({value: x, label: x})):[]}
            value={allData.find((x) => x.upc === rec.upc).salesChannels.filter((x)=>x).map((y)=>({value: y, label: y}))}
            mode='multiple'
            onChange={(value) => {
              console.log('value', value, rec);
              setAllData((prev)=>prev.map((x)=>{
                if (x.upc === rec.upc) {
                  return {...x, salesChannels: value};
                }
                return x;
              }));
              setSelectedRowKeys((prev)=>value.length>0?[...prev, rec.upc]:prev.filter((x)=>x!==rec.upc));
            }}
          />
        );
      },
    },
  ];
  if (!open) return null;
  return (
    <Modal
      title={'Add ' + addObject.addType}
      open={open}
      style={{minWidth: '1500px'}}
      onOk={async () => {
        setForecastData((prevData) => {
          const newData = {...prevData};
          for (const item of allData) {
            const addChannels = item.salesChannels;
            if (!addChannels || addChannels.length===0) {
              if (newData[item.productType] && newData[item.productType].variants[item.upc]) {
                delete newData[item.productType].variants[item.upc];
              }
            }
            if (addChannels.length >0) {
              if (!newData[item.productType]) {
                newData[item.productType] = {
                  productType: item.productType,
                  brand: item.brand,
                  revenue: 0,
                  forecast: 0,
                  percent: 0,
                  variants: {},
                };
              }
              if (!newData[item.productType].variants[item.upc]) {
                newData[item.productType].variants[item.upc] = {
                  upc: item.upc,
                  description: item.description,
                  launchDate: item.launchDate,
                  endDate: item.endDate,
                  lifeStatus: item.lifeStatus,
                  productSpecification: item.productSpecification,
                  channels: {},
                  forecast: 0,
                  revenue: 0,
                  percent: 0,
                };
              }
              const existingChannels = Object.keys(newData[item.productType].variants[item.upc].channels);
              const removeChannels = existingChannels.filter((x) => !addChannels.includes(x));
              const channelsToAdd = addChannels.filter((x) => !existingChannels.includes(x));
              for (const channel of removeChannels) {
                delete newData[item.productType].variants[item.upc].channels[channel];
              }
              for (const channel of channelsToAdd) {
                newData[item.productType].variants[item.upc].channels[channel] = {
                  uniqueKey: `${monthStr}_${item.upc}_${channel}`,
                  productType: item.productType,
                  month: monthStr,
                  upc: item.upc,
                  basePrice: item.basePrice,
                  salesChannel: channel,
                  forecast: 0,
                  revenue: 0,
                  percent: 0,
                };
              }
            }
          }
          return newData;
        });
        message.success('Channels Added', {duration: 2});
        message.destroy();
        setOpen(false);
      }}
      onCancel={() => {
        setAllData([]);
        setSelectedRowKeys([]);
        setSelectedBrands([]);
        setOpen(false);
      }}>
      {loading && <div>{'Loading...'}</div>}
      {!loading && (<Space direction='vertical'>
        <Space direction='horizontal'>
          <Select
            id='brandSelect'
            style={{width: 150}}
            placeholder='Brand...'
            allowClear
            value={selectedBrands}
            onChange={(value) => {
              setSelectedBrands(value);
            }}
            options={brandOptions.map((x) => ({value: x, label: x}))}
          />
          <Select
            id="lifeStatusSelect"
            style={{width: 150}}
            placeholder='Life Status...'
            allowClear
            value={selectedLifeStatus}
            onChange={(value) => {
              setSelectedLifeStatus(value);
            }}
            options={lifeStatusOptions.map((x) => ({value: x, label: x}))}
          />
          <Select
            id="colorSelect"
            style={{width: 150}}
            placeholder='Color...'
            allowClear
            showSearch
            mode='multiple'
            value={selectedColor}
            onChange={(value) => {
              setSelectedColor(value);
            }}
            options={colorOptions.map((x) => ({value: x, label: x}))}
          />
          <Select
            id="productTypeSelect"
            style={{width: 150}}
            placeholder='Product Type...'
            allowClear
            showSearch
            mode='multiple'
            value={selectedProducts}
            onChange={(value) => {
              setSelectedProducts(value);
            }}
            options={productOptions.map((x) => ({value: x, label: x}))}
          />
          <Checkbox
            id="showSelectedCheckbox"
            checked={showSelected}
            onChange={(e) => {
              setShowSelected(e.target.checked);
            }}
          >Show Selected</Checkbox>
        </Space>
        <Divider />
        <Table
          style={{overflowY: 'auto', maxWidth: '1450px'}}
          rowClassName={(record) => (record.salesChannels && record.salesChannels.length > 0 ? 'highlight-row' : '')}
          dataSource={allData.filter((x) => filterData(x))}
          columns={columns}
          rowKey='upc'
          pagination={{pageSize: 10}}
          size='small'
        />
      </Space>)}
    </Modal>
  );
};
export default OpsForecastAddModal;
