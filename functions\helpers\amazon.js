/* eslint-disable no-loop-func */
/* eslint-disable camelcase */
/* eslint-disable guard-for-in */
// TODOS for Amazon Seller Central API
// order create
// refund create
// ? inventory sync
// ? settlement sync
// v2 transfer create
// v2 transfer watching

const axios = require('axios');
const zlib = require('zlib');
const { parse } = require('csv-parse/sync'); 
const qs = require('qs');
const {
  modifyDoc,
} = require('./firestore');
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

const getAccessToken = async (connection) => {
  const data = qs.stringify({
    grant_type: 'refresh_token',
    refresh_token: connection.refreshToken,
    client_id: connection.clientId,
    client_secret: connection.clientSecret,
  });

  const config = {
    method: 'post',
    maxBodyLength: Infinity,
    url: 'https://api.amazon.com/auth/o2/token',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  };
  try {
    const response = await axios.request(config);
    const accessToken = response.data.access_token;
    const expireDate = new Date(Date.now() + response.data.expires_in * 1000);
    return { accessToken, expireDate };
  } catch (error) {
    console.log(error);
    if (error.code === 'ETIMEDOUT') {
      await sleep(500);
      return await getAccessToken(connection);
    }
    return false;
  }
};

const amazonGetReportDocument = async (connection, reportDocumentId) => {
  const { accessToken } = await getAccessToken(connection);
  const config = {
    method: 'get',
    maxBodyLength: Infinity,
    url: `https://sellingpartnerapi-na.amazon.com/reports/2021-06-30/documents/${reportDocumentId}`,
    headers: {
      'Accept': 'application/json',
      'x-amz-access-token': accessToken,
    },
  };

  const reportDocumentResponse = await axios.request(config);
  const compressionAlgorithm = reportDocumentResponse.data.compressionAlgorithm;
  const response = await axios({
    method: 'get',
    url: reportDocumentResponse.data.url,
    responseType: compressionAlgorithm === 'GZIP' ? 'stream' : null,
  });
  const chunks = [];
  let content;
  if (compressionAlgorithm === 'GZIP') {
    const gunzip = zlib.createGunzip();
    response.data.pipe(gunzip); // Pipe the response into the gunzip stream
    const inputStream = gunzip;
    for await (const chunk of inputStream) {
      chunks.push(chunk);
    }
    content = Buffer.concat(chunks).toString('utf8');
  } else {
    content = response.data;
  }
  // The report is typically in TSV format. Parse and extract ASIN and SKU.
  const headers = content.split('\n')[0].split('\t');
  const lines = content.split('\n');
  const items = [];
  lines.shift();
  // eslint-disable-next-line array-callback-return
  lines.map((line) => {
    const columns = line.split('\t');
    const item = headers.reduce((acc, header, index) => {
      acc[header] = columns[index];
      acc['connection'] = connection;
      acc['platform'] = 'amazon';
      return acc;
    }, {});
    items.push(item);
  });
  return items;
};

const amazonGetOrders = async (connection, startDate, endDate) => {
  try {
    const { accessToken } = await getAccessToken(connection);
    const baseURL = 'https://sellingpartnerapi-na.amazon.com';

    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'x-amz-access-token': accessToken,
    };

    console.log(`Requesting Amazon order report by purchase date: ${startDate.toISOString()} to ${endDate.toISOString()}`);

    const reportRequestRes = await axios.post(
      `${baseURL}/reports/2021-06-30/reports`,
      {
        reportType: "GET_FLAT_FILE_ALL_ORDERS_DATA_BY_ORDER_DATE_GENERAL",
        marketplaceIds: [connection.marketplaceId],
        dataStartTime: startDate.toISOString(),
        dataEndTime: endDate.toISOString()
      },
      { headers }
    );

    console.log('reportRequestRes',reportRequestRes);

    const reportId = reportRequestRes.data.reportId;
    console.log(`Report submitted: ${reportId}`);

    let reportDocumentId = null;
    let retries = 0;

    while (retries < 50) {
      const statusRes = await axios.get(
        `${baseURL}/reports/2021-06-30/reports/${reportId}`,
        { headers }
      );

      const status = statusRes.data.processingStatus;
      if (status === 'DONE') {
        reportDocumentId = statusRes.data.reportDocumentId;
        console.log(`Report ready: ${reportDocumentId}`);
        break;
      } else if (['CANCELLED', 'FATAL'].includes(status)) {
        throw new Error(`Report failed with status: ${status}`);
      }

      await new Promise(res => setTimeout(res, 15000)); // Wait 15s
      retries++;
    }

    if (!reportDocumentId) throw new Error("Report generation did not complete in time");

    const orders = amazonGetReportDocument(connection, reportDocumentId);

    console.log(`Parsed ${orders.length} orders from purchase-date report`);
    return orders;

  } catch (error) {
    const errDetails = error.response?.data?.errors || error.message;
    console.error('Amazon SP-API Report Error:', JSON.stringify(errDetails, null, 2));
    return [];
  }
};
const amazonGetLiveInventory = async (connection) => {
  const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

  const fetchWithRetry = async (url, config, maxRetries = 5) => {
    let attempt = 0;

    while (attempt <= maxRetries) {
      try {
        return await axios.get(url, config);
      } catch (error) {
        const isQuotaError = error.response?.data?.errors?.some(
          err => err.code === 'QuotaExceeded'
        );

        if (isQuotaError && attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000; // exponential backoff
          console.warn(`Quota exceeded. Retrying in ${delay}ms... (Attempt ${attempt + 1})`);
          await sleep(delay);
          attempt++;
        } else {
          throw error;
        }
      }
    }
  };

  try {
    const { accessToken } = await getAccessToken(connection);
    const baseURL = 'https://sellingpartnerapi-na.amazon.com';

    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'x-amz-access-token': accessToken,
    };

    console.log('Requesting Amazon live inventory data...');

    let nextToken = null;
    let allInventory = [];
    let param = {
      details: true,
      granularityType: 'Marketplace',
      granularityId: connection.marketplaceId,
      marketplaceIds: connection.marketplaceId,
    };

    do {
      const response = await fetchWithRetry(
        `${baseURL}/fba/inventory/v1/summaries`,
        {
          headers,
          params: param
        }
      );

      const inventoryData = response.data.payload.inventorySummaries || [];
      console.log(`Retrieved ${inventoryData.length} inventory records`);

      allInventory = allInventory.concat(inventoryData);

      nextToken = response.data.pagination?.nextToken || null;
      param.nextToken = nextToken;

      if (nextToken) {
        await sleep(600); // Respect Amazon's pacing (600ms = ~1.6 req/sec)
      }

    } while (nextToken);

    return allInventory;

  } catch (error) {
    const errDetails = error.response?.data?.errors || error.message;
    console.error('Amazon SP-API Inventory Error:', JSON.stringify(errDetails, null, 2));
    return [];
  }
};

exports.amazonGetOrders = amazonGetOrders;
exports.amazonGetLiveInventory = amazonGetLiveInventory;