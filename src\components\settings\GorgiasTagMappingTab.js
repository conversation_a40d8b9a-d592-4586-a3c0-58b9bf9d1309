import React, { useState, useEffect } from 'react';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { 
  Typography, 
  Collapse, 
  Input, 
  Button, 
  Tag, 
  Space, 
  message, 
  Spin,
  Alert 
} from 'antd';
import { PlusOutlined, SaveOutlined, UndoOutlined } from '@ant-design/icons';

const { Panel } = Collapse;
const { Text } = Typography;

const tagTypeLabels = {
  perfectOrders: 'Non-Perfect Orders',
  accurateOrders: 'Non-Accurate Orders',
  defectiveItems: 'Defective Items',
  nonDefectiveItems: 'Poor Quality Items'
};

const GorgiasTagMappingTab = () => {
  const [tagMappings, setTagMappings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [newTag, setNewTag] = useState('');
  const [selectedKPI, setSelectedKPI] = useState(null);
  const [selectedTagType, setSelectedTagType] = useState(null);

  const functions = getFunctions();
  const getGorgiasTagMappingsFn = httpsCallable(functions, 'getGorgiasTagMappings');
  const updateGorgiasTagMappingsFn = httpsCallable(functions, 'updateGorgiasTagMappings');
  const resetGorgiasTagMappingsFn = httpsCallable(functions, 'resetGorgiasTagMappings');

  useEffect(() => {
    fetchTagMappings();
  }, []);

  const fetchTagMappings = async () => {
    try {
      const result = await getGorgiasTagMappingsFn();
      const mappings = result.data?.result;
      
      if (mappings && Object.keys(mappings).length > 0) {
        setTagMappings(mappings);
      } else {
        message.error('No mappings found in response');
      }
    } catch (err) {
      console.error('Error fetching Gorgias tag mappings:', err);
      message.error('Failed to load tag mappings');
    } finally {
      setLoading(false);
    }
  };

  const handleAddTag = (kpi, tagType) => {
    if (!newTag.trim()) return;

    const updatedMappings = {
      ...tagMappings,
      [kpi]: {
        ...tagMappings[kpi],
        tags: {
          ...tagMappings[kpi].tags,
          [tagType]: [...tagMappings[kpi].tags[tagType], newTag.trim()]
        }
      }
    };

    setTagMappings(updatedMappings);
    setNewTag('');
    setSelectedKPI(null);
    setSelectedTagType(null);
  };

  const handleRemoveTag = (kpi, tagType, tagToRemove) => {
    const updatedMappings = {
      ...tagMappings,
      [kpi]: {
        ...tagMappings[kpi],
        tags: {
          ...tagMappings[kpi].tags,
          [tagType]: tagMappings[kpi].tags[tagType].filter(tag => tag !== tagToRemove)
        }
      }
    };

    setTagMappings(updatedMappings);
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      const result = await updateGorgiasTagMappingsFn({ tagMappings });
      const updatedMappings = result.data?.result;
      if (updatedMappings) {
        setTagMappings(updatedMappings);
        message.success('Tag mappings saved successfully');
      }
    } catch (err) {
      console.error('Error saving tag mappings:', err);
      message.error('Failed to save tag mappings');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = async () => {
    setSaving(true);
    try {
      const result = await resetGorgiasTagMappingsFn();
      const defaultMappings = result.data?.result;
      if (defaultMappings) {
        setTagMappings(defaultMappings);
        message.success('Tag mappings reset to defaults successfully');
      }
    } catch (err) {
      console.error('Error resetting tag mappings:', err);
      message.error('Failed to reset tag mappings');
    } finally {
      setSaving(false);
    }
  };

  const getTagSectionLabel = (kpi, tagType) => {
    switch (kpi) {
      case 'Perfect Order Rate':
        return 'Non-Perfect Orders:';
      case 'Order Accuracy Rate':
        return 'Non-Accurate Orders:';
      case 'Supplier Defect Rate':
        return 'Defective Items:';
      case 'Supplier Quality Score':
        return 'Poor Quality Items:';
      default:
        return tagTypeLabels[tagType] || tagType;
    }
  };

  if (loading) {
    return <Spin size="large" />;
  }

  if (!tagMappings) {
    return <Alert message="No tag mappings available. Try resetting to defaults." type="info" />;
  }

  return (
    <div style={{ background: '#fff', padding: '24px' }}>
      <Space style={{ marginBottom: 16, width: '100%', justifyContent: 'space-between' }}>
        <Typography.Title level={3}>Gorgias Tag Mappings</Typography.Title>
        <Space>
          <Button 
            icon={<UndoOutlined />} 
            onClick={handleReset}
            disabled={saving}
          >
            Reset to Default
          </Button>
          <Button 
            type="primary" 
            icon={<SaveOutlined />} 
            onClick={handleSave}
            disabled={saving}
            loading={saving}
          >
            Save Changes
          </Button>
        </Space>
      </Space>

      <Collapse defaultActiveKey={['0']}>
        {Object.entries(tagMappings).map(([kpi, config], index) => (
          <Panel header={kpi} key={index}>
            <Text type="secondary">{config.description}</Text>
            
            {config.tags && Object.entries(config.tags)
              .filter(([tagType]) => !tagType.includes('total'))
              .map(([tagType, tags]) => (
                <div key={tagType} style={{ 
                  background: '#f5f5f5', 
                  padding: 16, 
                  marginTop: 16, 
                  borderRadius: 2 
                }}>
                  <Typography.Title level={5}>
                    {getTagSectionLabel(kpi, tagType)}
                  </Typography.Title>

                  <div style={{ marginBottom: 16 }}>
                    {tags.map((tag, tagIndex) => (
                      <Tag 
                        key={`${tag}-${tagIndex}`}
                        closable
                        onClose={() => handleRemoveTag(kpi, tagType, tag)}
                        style={{ margin: '4px' }}
                      >
                        {tag}
                      </Tag>
                    ))}
                  </div>

                  <Space.Compact style={{ width: '100%' }}>
                    <Input
                      placeholder="Add new tag"
                      value={selectedKPI === kpi && selectedTagType === tagType ? newTag : ''}
                      onChange={(e) => setNewTag(e.target.value)}
                      onFocus={() => {
                        setSelectedKPI(kpi);
                        setSelectedTagType(tagType);
                      }}
                    />
                    <Button 
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => handleAddTag(kpi, tagType)}
                      disabled={!newTag.trim() || selectedKPI !== kpi || selectedTagType !== tagType}
                    />
                  </Space.Compact>
                </div>
              ))}
          </Panel>
        ))}
      </Collapse>
    </div>
  );
};

export default GorgiasTagMappingTab;
