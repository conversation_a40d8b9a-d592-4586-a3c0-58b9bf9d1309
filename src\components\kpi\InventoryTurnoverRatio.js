import React, { useState, useEffect } from 'react';
import { Box, Typography, Grid, Paper, CircularProgress, Alert, FormControl, Autocomplete, TextField } from '@mui/material';
import {
    ComposedChart, Line, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { useFormatAxisTick } from '../../hooks/useFormatAxisTick';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import ChartExportWrapper from './ChartExportWrapper';

const InventoryTurnoverRatio = () => {
    const [inventoryData, setInventoryData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [selectedUPC, setSelectedUPC] = useState(null);
    const [lastCompleteMonth, setLastCompleteMonth] = useState('');
    const [earliestDataMonth, setEarliestDataMonth] = useState('');
    const formatYAxisTick = useFormatAxisTick();

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const functions = getFunctions();
            const getInventoryTurnoverRatio = httpsCallable(functions, 'getInventoryTurnoverRatio');
            const getKPIGoalsForReport = httpsCallable(functions, 'getKPIGoalsForReport');

            const [inventoryResult, goalsResult] = await Promise.all([
                getInventoryTurnoverRatio(),
                getKPIGoalsForReport({ reportName: 'inventory-turnover' })
            ]);

            setInventoryData(inventoryResult.data);
            setLastCompleteMonth(inventoryResult.data.lastCompleteMonth);
            setEarliestDataMonth(inventoryResult.data.earliestDataMonth);

            if (goalsResult.data.success) {
                setKpiGoals(goalsResult.data.data);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            setError('Failed to fetch data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            const turnoverRatio = payload.find(p => p.dataKey === 'turnoverRatio')?.value;
            const cogs = payload.find(p => p.dataKey === 'cogs')?.value;
            const averageInventory = payload.find(p => p.dataKey === 'averageInventory')?.value;

            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                    <Typography variant="body2">{`Month: ${label}`}</Typography>
                    {turnoverRatio !== undefined && (
                        <Typography variant="body2" color="primary">
                            {`Turnover Ratio: ${turnoverRatio.toFixed(2)}`}
                        </Typography>
                    )}
                    {cogs !== undefined && (
                        <Typography variant="body2" color="secondary">
                            {`COGS: $${cogs.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                        </Typography>
                    )}
                    {averageInventory !== undefined && (
                        <Typography variant="body2" color="error">
                            {`Avg Inventory Cost: $${averageInventory.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                        </Typography>
                    )}
                </Paper>
            );
        }
        return null;
    };

    const getChartData = () => {
        if (!inventoryData) return [];
        let data = !selectedUPC ? inventoryData.runningData :
            Object.entries(inventoryData.turnoverRatios[selectedUPC].monthlyData || {})
                .map(([date, data]) => ({
                    date,
                    turnoverRatio: data.turnoverRatio,
                    cogs: data.cogs,
                    averageInventory: data.averageInventory
                }));
        return data.filter(item => item.date >= earliestDataMonth && item.date <= lastCompleteMonth);
    };

    const calculateTotals = (data) => {
        return data.reduce((acc, item) => {
            acc.totalCOGS += item.cogs || 0;
            acc.totalAverageInventory += item.averageInventory || 0;
            return acc;
        }, { totalCOGS: 0, totalAverageInventory: 0 });
    };

    const calculateTurnoverTrend = () => {
        const chartData = getChartData();
        if (chartData.length < 2) return 0;
        const lastTwo = chartData.slice(-2);
        return lastTwo[1].turnoverRatio - lastTwo[0].turnoverRatio;
    };

    const getCurrentTurnoverRatio = () => {
        if (!selectedUPC) {
            return inventoryData.overallTurnoverRatio;
        }
        return inventoryData.turnoverRatios[selectedUPC].overallTurnoverRatio;
    };

    // Calculate domains including the goal values
    const calculateDomains = (data) => {
        if (!data.length) return { ratioDomain: [0, 10], amountDomain: [0, 1000] };

        const ratioValues = data.map(item => item.turnoverRatio);
        const amountValues = data.map(item => Math.max(item.cogs || 0, item.averageInventory || 0));

        // Include goal values in ratio domain calculation
        const goalValue = kpiGoals?.['Inventory Turnover Ratio']?.value;
        const maxRatio = Math.max(...ratioValues, goalValue ? parseFloat(goalValue) : 0);

        return {
            ratioDomain: [0, Math.ceil(maxRatio * 1.1)],
            amountDomain: [0, Math.ceil(Math.max(...amountValues) * 1.1)]
        };
    };

    // Use the KPI reference lines hook
    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: kpiGoals?.['Inventory Turnover Ratio'],
        yAxisId: "left",
        styles: {
            stroke: "#ff9800",
            strokeDasharray: "3 3",
            label: {
                fill: "#ff9800",
                fontSize: 12,
                position: "right"
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Min",
            max: "Max"
        }
    });

    if (isLoading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Alert severity="error" sx={{ mt: 2 }}>
                {error}
            </Alert>
        );
    }

    if (!inventoryData || !kpiGoals) return null;

    const chartData = getChartData();
    const { ratioDomain, amountDomain } = calculateDomains(chartData);
    const { totalCOGS, totalAverageInventory } = calculateTotals(chartData);
    const upcOptions = [{ label: 'Overall', value: null }, ...Object.keys(inventoryData.turnoverRatios).map(upc => ({ label: upc, value: upc }))];
    const turnoverConfig = kpiGoals['Inventory Turnover Ratio'];
    const currentTurnoverRatio = getCurrentTurnoverRatio();

    return (
        <ChartExportWrapper title={`Inventory_Turnover${selectedUPC ? `_${selectedUPC}` : ''}`}>
            <Box sx={{ p: { xs: 2, sm: 3 }, mb: 3 }}>
                <FormControl sx={{ mb: 2, width: '300px' }}>
                    <Autocomplete
                        options={upcOptions}
                        getOptionLabel={(option) => option.label}
                        renderInput={(params) => <TextField {...params} label="Select UPC" size="small" />}
                        value={upcOptions.find(option => option.value === selectedUPC) || upcOptions[0]}
                        onChange={(event, newValue) => {
                            setSelectedUPC(newValue ? newValue.value : null);
                        }}
                        size="small"
                    />
                </FormControl>

                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <ResponsiveContainer width="100%" height={500}>
                        <ComposedChart data={chartData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis
                                yAxisId="left"
                                domain={ratioDomain}
                                label={{ value: 'Turnover Ratio', angle: -90, position: 'insideLeft' }}
                            />
                            <YAxis
                                yAxisId="right"
                                orientation="right"
                                domain={amountDomain}
                                label={{ value: 'Amount', angle: 90, position: 'insideRight' }}
                                tickFormatter={formatYAxisTick}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            {goalReferenceLines}
                            <Bar yAxisId="right" dataKey="cogs" fill="#82ca9d" name="COGS" />
                            <Bar yAxisId="right" dataKey="averageInventory" fill="#ffc658" name="Avg Inventory Cost" />
                            <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="turnoverRatio"
                                stroke="#8884d8"
                                strokeWidth={3}
                                dot={{ r: 4, fill: "#8884d8" }}
                                activeDot={{ r: 8 }}
                                name="Turnover Ratio"
                            />
                        </ComposedChart>
                    </ResponsiveContainer>

                    <Grid container spacing={2} justifyContent="center" sx={{ mt: 2 }}>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <KPICard
                                title={!selectedUPC ? 'Overall Inventory Turnover Ratio' : `UPC ${selectedUPC} Turnover Ratio`}
                                value={currentTurnoverRatio.toFixed(2)}
                                bgColor="#f0f4ff"
                                textColor="primary"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <KPICard
                                title="Total COGS"
                                value={`$${totalCOGS.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                                bgColor="#f0fff0"
                                textColor="secondary"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4} lg={3}>
                            <KPICard
                                title="Total Avg Inventory Cost"
                                value={`$${totalAverageInventory.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
                                bgColor="#fff0f0"
                                textColor="error"
                            />
                        </Grid>
                    </Grid>
                </Paper>

                {selectedUPC && (
                    <Paper elevation={3} sx={{ p: 2, mt: 2 }}>
                        <Typography variant="subtitle1">UPC Details</Typography>
                        <Typography variant="body2">
                            Life Status: {inventoryData.turnoverRatios[selectedUPC].lifeStatus || 'N/A'}
                        </Typography>
                        <Typography variant="body2">
                            Launch Date: {inventoryData.turnoverRatios[selectedUPC].launchDate || 'N/A'}
                        </Typography>
                        <Typography variant="body2">
                            End Date: {inventoryData.turnoverRatios[selectedUPC].endDate || 'N/A'}
                        </Typography>
                    </Paper>
                )}

                <GoalStatusDisplay
                    currentValue={currentTurnoverRatio}
                    goalConfig={turnoverConfig}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculateTurnoverTrend()}
                    size="medium"
                    title={!selectedUPC ? 'Overall Turnover Ratio Performance' : `UPC ${selectedUPC} Turnover Ratio Performance`}
                />
            </Box>
        </ChartExportWrapper>
    );
};

export default InventoryTurnoverRatio;