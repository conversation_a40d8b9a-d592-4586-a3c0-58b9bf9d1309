/* eslint-disable react/jsx-key */
import React, { useState, useEffect } from 'react';
import { Modal, Input, Button, Typography, Table, Select, Tag, Image } from 'antd';
import { gapi } from 'gapi-script';
import { arrayUnion } from 'firebase/firestore';
import { ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { storage } from '../../pages/firebase';
const { Title } = Typography;
const ImportImages = ({ setOpenModal, importImagesModalIsOpen }) => {
  const [folderLink, setFolderLink] = useState('');
  const [uploadedImages, setUploadedImages] = useState([]);
  const CLIENT_ID = '825215331266-gr1v4q57q0k3cml98cuhtfknmrvdq63k.apps.googleusercontent.com';
  const API_KEY = 'AIzaSyBskLFZ3AvZZLi5ndcickvyqFrBKG9eN5c';
  const SCOPES = 'https://www.googleapis.com/auth/drive.readonly';
  let uploadList = [];

  const handleSaveImages = () => {
    const updatedUploadedImages = uploadedImages.map(image => ({
      ...image,
      status: saveImageURL(image)
    }));
    setUploadedImages(updatedUploadedImages);
  };

  const saveImageURL = (image) => {
    if (!image.item || !image.field)
      return 'error';
    if (image.status === 'success')
      return 'success';

    const field = itemAppFields.find(field => field.fieldId === image.field);
    updateItemFieldData(
      'items',
      image.item,
      image.field,
      (field.fieldType === 'multiimage') ? arrayUnion(image.imageURL) : image.imageURL
    );
    // const updatedItems = items.map(item => 
    //   item.id === image.item ? { ...item, [image.field]: ((field.fieldType === 'multiimage') ? [...item[image.field], image.imageURL] : image.imageURL) } : item
    // );
    // setItems(updatedItems);
    return 'success';
  };

  const handleSubmitFolderLink = async () => {
    gapi.auth2.getAuthInstance().signIn().then(() => {
      getFiles();
    });
  };

  const getFiles = () => {
    uploadList = [];
    const folderId = extractFileId(folderLink);
    gapi.client.drive.files.list({
      q: `'${folderId}' in parents and mimeType contains 'image/' and trashed=false`,
      fields: 'files(id, name, mimeType)',
    }).then((response) => {
      const fileList = response.result.files;
      fileList.map(async (file) => {
        console.log(file);
        if (['image/jpeg', 'image/png'].indexOf(file.mimeType) >= 0) {
          gapi.client.drive.files.get({
            fileId: file.id,
            alt: 'media',
          }, {
            responseType: 'arraybuffer',
          })
            .then(
              (response) => {
                const binary = response.body;
                const l = binary.length;
                let array = new Uint8Array(l);
                for (let i = 0; i < l; i++) {
                  array[i] = binary.charCodeAt(i);
                }
                const blob = new Blob([array], { type: file.mimeType });
                uploadFileToFirebase(blob, file.id);
              },
              (error) => {
                console.error('Error: ', error);
              }
            );
        }
      });
    });
  };

  const uploadFileToFirebase = (fileUpload, fileName) => {
    const imageName = 'GD_' + fileName;
    const storageRef = ref(storage, `item_images/${imageName}`);
    const uploadTask = uploadBytesResumable(storageRef, fileUpload);

    uploadTask.on(
      'state_changed',
      (snapshot) => {
        console.log(snapshot);
      },
      (error) => {
        console.error('Upload failed', error);
      },
      () => {
        // Get the uploaded image URL
        getDownloadURL(uploadTask.snapshot.ref).then((downloadURL) => {
          const imageData = {
            imageURL: downloadURL,
            item: '',
            field: 'images',
            status: 'pending',
            key: imageName,
          };
          setUploadedImages([...uploadList, imageData]);
          uploadList.push(imageData);
        });
      }
    );
  };

  const extractFileId = (url) => {
    const regex = /[-\w]{25,}/; // Simplified regex for file ID
    const matches = url.match(regex);
    return matches ? matches[0] : null;
  };

  const handleSelectChange = (value, key, column) => {
    const newData = [...uploadedImages];
    const index = newData.findIndex((item) => item.key === key);
    if (index > -1) {
      newData[index][column] = value;
      newData[index]['status'] = 'pending';
      setUploadedImages(newData);
    }
  };

  const columns = [
    {
      title: 'Image',
      dataIndex: 'imageURL',
      key: 'imageURL',
      render: (url) => <Image src={url} height={50} />
    },
    {
      title: 'Item',
      dataIndex: 'item',
      key: 'item',
      render: (val, record) => (
        <Select
          defaultValue={val}
          style={{ width: 250 }}
          showSearch={true}
          filterOption={(input, option) =>
            option.children.toLowerCase().includes(input.toLowerCase())
          }
          status={!(val) ? 'error' : ''}
          onChange={(value) => handleSelectChange(value, record.key, 'item')}
          options={itemsAsOptions.map((option) => ({ value: option.id, label: option.label }))}
        />
      )
    },
    {
      title: 'Field',
      dataIndex: 'field',
      key: 'field',
      render: (val, record) => (
        <Select
          defaultValue={val}
          style={{ width: 250 }}
          showSearch={true}
          filterOption={(input, option) =>
            option.children.toLowerCase().includes(input.toLowerCase())
          }
          status={!(val) ? 'error' : ''}
          onChange={(value) => handleSelectChange(value, record.key, 'field')}
          options={itemAppFields.filter(field => (field.fieldType === 'image' || field.fieldType === 'multiimage')).map((field) => ({ value: field.fieldId, label: field.label }))}
        />
      )
    },
    {
      title: 'Status',
      key: 'status',
      dataIndex: 'status',
      render: (val) => (
        <Tag color={(val === 'success') ? 'green' : (val === 'error') ? 'red' : 'gray'} key={val}>
          {val.toUpperCase()}
        </Tag>
      ),
    },
  ];

  const handleCloseModal = (e) => {
    setOpenModal('');
  };

  useEffect(() => {
    const initClient = () => {
      gapi.client.init({
        apiKey: API_KEY,
        clientId: CLIENT_ID,
        scope: SCOPES,
        discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/drive/v3/rest'],
      });
    };

    gapi.load('client:auth2', initClient);
  }, []);

  return (importImagesModalIsOpen) ? (
    <Modal maskClosable={false} open={true} width={(uploadedImages.length === 0) ? 600 : 1000} onCancel={handleCloseModal} footer={[
      <div>
        <Button onClick={() => {
          setOpenModal('');
        }}>
          {(uploadedImages.length === 0) ? 'Cancel' : 'Close'}
        </Button>
        {(uploadedImages.length === 0) ? (
          <Button type="primary" onClick={handleSubmitFolderLink} style={{ marginLeft: '5px' }}>
            Submit
          </Button>
        ) : (
          <Button type="primary" onClick={handleSaveImages} style={{ marginLeft: '5px' }}>
            Save Images
          </Button>
        )}
      </div>
    ]}>
      <Title level={3} style={{ marginTop: '0px' }}>Import Images</Title>
      {(uploadedImages.length === 0) ? (
        <>
          <Title level={5} style={{ marginTop: '0px' }}>Google Drive Link:</Title>
          <Input placeholder="Enter the folder link" value={folderLink} onChange={(e) => setFolderLink(e.target.value)} />
        </>
      ) : (
        <Table
          columns={columns}
          dataSource={uploadedImages}
          pagination={false}
          rowKey="key"
        />
      )}
    </Modal>
  ) : '';
};
export default ImportImages;