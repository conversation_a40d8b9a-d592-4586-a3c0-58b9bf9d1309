import React, { useState, useEffect } from 'react';
import { Modal, Input, Button, Typography, Form, Select, Checkbox, Row, Col, message, Popconfirm } from 'antd';
import { db, api } from '../../pages/firebase';
import { defaultFieldTypes as fieldTypeList, defaultDisplayTypes as displayTypeList, defaultMandatoryStages as mandatoryStageList, defaultPlatformGroups as platformGroups, PRODUCT_TABLE, VARIANTS_TABLE } from './template';
import { collection, getDocs } from 'firebase/firestore';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const defaultFieldViewData = {
  mandatoryStage: [],
  fieldType: 'text',
  display: 'normal',
  platform: ''
};

const FieldView = ({ fieldViewModalIsOpen, setOpenModal, fields, platformList, currentFormView = 'default', viewType = 'Product' }) => {
  const [fieldData, setFieldData] = useState(defaultFieldViewData);
  const [fieldOptions, setFieldOptions] = useState(fields);
  const [fieldWithError, setFieldWithError] = useState({});
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (!fieldViewModalIsOpen || viewType === 'Product') return;
    const fetchData = async () => {
      const productFields = await getDocs(collection(db, `pimProductFields`));
      const fetchedProductFields = [];
      for (const field of productFields.docs) {
        const fieldData = field.data();
        fetchedProductFields.push({ ...fieldData, id: field.id, fieldId: `product.${fieldData.fieldId}`, label: `(PRODUCT) ${fieldData.label}` });
      }
      console.log('fetchedProductFields', fetchedProductFields);
      setFieldOptions([...fields, ...fetchedProductFields]);
    };
    fetchData();
  }, [fieldViewModalIsOpen]);

  const handleChangeLabel = (value) => {
    setFieldData(prevData => ({ ...prevData, label: value, fieldId: toCamelCase(value) }));
  };

  const sortFieldOptions = (optionA, optionB) => {
    if (optionA.platform === 'PRIMARY' && optionB.platform !== 'PRIMARY') return -1;
    if (optionA.platform !== 'PRIMARY' && optionB.platform === 'PRIMARY') return 1;
    if (optionA.platform < optionB.platform) return -1;
    if (optionA.platform > optionB.platform) return 1;
    if (optionA.label < optionB.label) return -1;
    if (optionA.label > optionB.label) return 1;
    return 0;
  };

  const handleChangeDropdownOptions = (value) => {
    const optionList = value.split(',');
    let optionParsedList = [];
    optionList.map(val => {
      if (val.trim()) {
        optionParsedList.push({
          id: toCamelCase(val.trim()),
          label: val.trim(),
        });
      }
    });

    setFieldData(prevData => ({ ...prevData, options: optionParsedList }));
  };

  const handleMandatoryStage = (id, value) => {
    let mandatoryStage = (fieldData.mandatoryStage) ? fieldData.mandatoryStage : [];

    if (value) {
      if (mandatoryStage.indexOf(id) === -1)
        mandatoryStage.push(id);
    } else {
      mandatoryStage = mandatoryStage.filter(item => item !== id);
    }
    setFieldData(prevData => ({ ...prevData, mandatoryStage: mandatoryStage }));
  };

  const handleSaveField = () => {
    let errors = {};

    if (!fieldData.label)
      errors.label = 'Enter value to this field.';
    if (!fieldData.fieldType)
      errors.fieldType = 'Enter value to this field.';
    if (!fieldData.fieldId)
      errors.fieldId = 'Enter value to this field.';
    if (!fieldData.platform)
      errors.platform = 'Enter value to this field.';
    if (!fieldData.group)
      errors.group = 'Enter value to this field.';
    if (!fieldData.display)
      errors.display = 'Enter value to this field.';

    if (fieldData.fieldId && containsSpacesAndSpecialChars(fieldData.fieldId)) {
      errors.fieldId = 'The Field ID should not contain space or special character.';
    }

    const newFieldId = `${fieldData.platform.toLowerCase()}_${fieldData.fieldId}`;

    const existingField = fieldOptions.find(field => {
      if (!field.fieldId) return false;
      return field.fieldId.toLowerCase() === newFieldId.toLowerCase();
    });
  
    if (fieldData.id) {
      if (existingField && existingField.id !== fieldData.id) {
        errors.fieldId = 'The Field ID is already used by another field.';
      }
    } else {
      if (existingField) {
        errors.fieldId = 'The Field ID is already used by another field.';
      }
    }
  
    if (fieldData.fieldType === 'select' && (!fieldData.options || fieldData.options.length === 0))
      errors.options = 'Enter value to this field.';

    if (fieldData.relatedField && fieldData.defaultField) {
      errors.relatedField = 'You cannot have a related field and a default field.';
      errors.defaultField = 'You cannot have a related field and a default field.';
    }

    setFieldWithError(errors);

    if (Object.keys(errors).length === 0) {
      const fieldType = fieldTypeList.find(field => field.id === fieldData.fieldType);
      fieldData.fieldId = newFieldId;
      if (fieldData.relatedField || fieldData.defaultField) {
        const linkedField = fieldData.relatedField ? JSON.parse(fieldData.relatedField) : JSON.parse(fieldData.defaultField);
        fieldData.note = fieldData.relatedField ? `Linked to ${linkedField.fieldId} [${linkedField.platform}]. ${fieldData.note}` : `Default from ${linkedField.fieldId} [${linkedField.platform}]. ${fieldData.note}`;
        if (fieldData.relatedField) {
          fieldData.display = 'inline';
          fieldData.relatedField = fieldData.relatedField.fieldId; // NOTE: overriding the full field object with just the id
        }
        if (fieldData.defaultField) {
          fieldData.defaultField = fieldData.defaultField.fieldId;
        }
      }

      setSaving(true); // TODO: Add loading state
      try {
        api.createDocOnCall({ collectionName: `pim${viewType}Fields`, info: fieldData })
          .then((result) => {
            console.log('updateResult:', result);

            const updateTableResult = api.bigQueryAddColumnOnCall({
              datasetId: 'items',
              tableId: viewType.toLowerCase() + 's',
              newColumnName: fieldData.fieldId,
              newColumnType: fieldType.bigQueryType,
              options: {
                description: fieldData.label
              }
            });

            updateTableResult.then((sResult) => {
              console.log('updateTableResult:', sResult);

              const finalizeSave = () => {
                message.success('The field has been added.');
                setOpenModal('');
                setFieldData(defaultFieldViewData);
                setSaving(false);
              };

              if (fieldData.relatedField || fieldData.defaultField) {
                const fieldToUpdate = fieldData.relatedField ? fieldData.relatedField : fieldData.defaultField;
                const query = `
              UPDATE items.${(viewType === 'Product') ? 'products' : 'items'}
              SET ${fieldData.fieldId} = ${fieldToUpdate}
              WHERE TRUE;
              `;
                api.runQueryOnCall({ options: { query: query } }).then((vResult) => {
                  console.log('updateTableRowsResult:', vResult);
                  finalizeSave();
                });
              } else {
                finalizeSave();
              }
            });
          });
      } catch (error) {
        console.error('Error saving field', error);
        message.error('Error saving field');
        return;
      }
    }
  };

  const toCamelCase = (str) => {
    return str
      .split(' ')
      .map((word, index) => {
        if (index === 0) {
          return word.toLowerCase();
        }
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      })
      .join('');
  };

  const containsSpacesAndSpecialChars = (str) => {
    const hasSpaces = /\s/.test(str);
    const hasSpecialChars = /[!@#$%^&*(),.?":{}|<>]/.test(str);

    return hasSpaces || hasSpecialChars;
  };

  return (fieldViewModalIsOpen) ? (
    <Modal maskClosable={false} open={true} width={1000} footer={
      <div>
        <Button type="primary" onClick={handleSaveField} style={{ marginLeft: '5px' }} disabled={saving}>
          {saving ? 'Saving...' : 'Save'}
        </Button>
      </div>
    } onCancel={() => setOpenModal('')}>
      <Title level={3} style={{ marginTop: '0px' }}>
        {(fieldData.id) ? `Edit ${viewType} Field` : `Add ${viewType} Field`}
      </Title>
      <Form layout='vertical'>
        <Row>
          <Col span={12} style={{ padding: '10px' }}>
            <Form.Item label='Label:' validateStatus={(fieldWithError.label) ? 'error' : ''} help={fieldWithError.label}>
              <Input
                value={fieldData.label}
                onChange={(e) => handleChangeLabel(e.target.value)}
              />
            </Form.Item>
            <Form.Item label='Field Type:' validateStatus={(fieldWithError.fieldType) ? 'error' : ''} help={fieldWithError.fieldType}>
              <Select
                defaultValue={(fieldData.fieldType) ? fieldData.fieldType : 'text'}
                style={{ width: 250 }}
                onChange={(value) => {
                  setFieldData(prevData => ({ ...prevData, fieldType: value, defaultField: '', relatedField: '' })); // TODO: Reset related and default fields
                }}
              >
                <Option value="">-Select-</Option>
                {
                  fieldTypeList.map((option) => {
                    return (
                      <Option key={option.id} value={option.id}>{option.label}</Option>
                    );
                  })
                }
              </Select>
            </Form.Item>
            {(fieldData.fieldType === 'select') ? (
              <Form.Item label='Dropdown Options:' validateStatus={(fieldWithError.options) ? 'error' : ''} help={fieldWithError.options}>
                <TextArea
                  defaultValue={(fieldData.options) ? fieldData.options.map(item => item.label).join(', ') : ''}
                  onChange={(e) => {
                    handleChangeDropdownOptions(e.target.value);
                  }}
                  placeholder="Comma separated values"
                />
              </Form.Item>
            ) : ''}
            <Form.Item label='Display Type:' validateStatus={(fieldWithError.display) ? 'error' : ''} help={fieldWithError.display}>
              <Select
                defaultValue={(fieldData.display) ? fieldData.display : 'normal'}
                style={{ width: 250 }}
                onChange={(value) => setFieldData(prevData => ({ ...prevData, display: value }))}
              >
                <Option value="">-Select-</Option>
                {
                  displayTypeList.map((option) => {
                    return (
                      <Option key={option.id} value={option.id}>{option.label}</Option>
                    );
                  })
                }
              </Select>
            </Form.Item>
            <Form.Item label='Note:'>
              <TextArea
                defaultValue={(fieldData.note) ? fieldData.note : ''}
                onChange={(e) => {
                  setFieldData(prevData => ({ ...prevData, note: e.target.value }));
                }}
              />
            </Form.Item>
            <Form.Item label='Set as Mandatory:'
              extra={
                (fieldData.mandatoryStage && fieldData.mandatoryStage.length > 0)
                  ? (
                    <>This field is mandatory when the life status is set to {(fieldData.mandatoryStage) ? mandatoryStageList.filter(item => fieldData.mandatoryStage.indexOf(item.id) >= 0).map(item => item.label).join(', ') : ''}.</>
                  ) : ''}
            >
              {mandatoryStageList.map((option) => {
                return (
                  <Checkbox key={option.id} onChange={(e) => {
                    handleMandatoryStage(option.id, e.target.checked);
                  }} checked={(fieldData.mandatoryStage && fieldData.mandatoryStage.indexOf(option.id) >= 0)}>{option.label}</Checkbox>
                );
              })}
            </Form.Item>
          </Col>
          <Col span={12} style={{ padding: '10px' }}>
            <Form.Item label='Field ID:' validateStatus={(fieldWithError.fieldId) ? 'error' : ''} help={fieldWithError.fieldId}>
              <Input.Group compact>
                {fieldData.platform && fieldData.platform !== 'PRIMARY' && (
                  <Input
                    style={{ width: '30%' }}
                    value={`${fieldData.platform.toLowerCase()}_`}
                    disabled
                  />
                )}
                <Input
                  style={{ width: fieldData.platform ? '70%' : '100%' }}
                  value={fieldData.fieldId}
                  onChange={(e) => {
                    setFieldData(prevData => ({ ...prevData, fieldId: e.target.value }));
                  }}
                />
              </Input.Group>
            </Form.Item>
            <Form.Item label='Platform:' validateStatus={(fieldWithError.platform) ? 'error' : ''} help={fieldWithError.platform}>
              <Select
                defaultValue={(fieldData.platform) ? fieldData.platform : ''}
                style={{ width: 250 }}
                onChange={(value) => setFieldData(prevData => ({ ...prevData, platform: value }))}
              >
                <Option value="">-Select-</Option>
                {
                  platformList.map((option) => {
                    return (
                      <Option key={option.id} value={option.id}>{option.label}</Option>
                    );
                  })
                }
              </Select>
            </Form.Item>
            <Form.Item label='Platform Group:' validateStatus={(fieldWithError.group) ? 'error' : ''} help={fieldWithError.group}>
              <Select
                defaultValue={(fieldData.group) ? fieldData.group : ''}
                style={{ width: 250 }}
                onChange={(value) => setFieldData(prevData => ({ ...prevData, group: value }))}
              >
                <Option value="">-Select-</Option>
                {
                  (platformGroups[fieldData.platform]) ? platformGroups[fieldData.platform].map((option) => {
                    return (
                      <Option key={option.id} value={option.id}>{option.label}</Option>
                    );
                  }) : ''
                }
              </Select>
            </Form.Item>
            <Form.Item label='Related Field:' validateStatus={(fieldWithError.relatedField) ? 'error' : ''} help={fieldWithError.relatedField}>
              <Select
                showSearch
                defaultValue={(fieldData.relatedField) ? fieldData.relatedField.fieldId : ''}
                style={{ width: 250 }}
                onChange={(value) => setFieldData(prevData => ({ ...prevData, relatedField: value }))}
              >
                <Option value="">-Select-</Option>
                {
                  fieldOptions.filter(field => field.fieldType === fieldData.fieldType)
                    .sort((a, b) => sortFieldOptions(a, b))
                    .map((field) => {
                      return (
                        <Option key={field.fieldId} value={JSON.stringify(field)}>{field.label} [{field.platform}]</Option>
                      );
                    })
                }
              </Select>
            </Form.Item>
            <Form.Item label='Default Field:' validateStatus={(fieldWithError.defaultField) ? 'error' : ''} help={fieldWithError.defaultField}>
              <Select
                showSearch
                defaultValue={(fieldData.defaultField) ? fieldData.defaultField.fieldId : ''}
                style={{ width: 250 }}
                onChange={(value) => setFieldData(prevData => ({ ...prevData, defaultField: value }))}
              >
                <Option value="">-Select-</Option>
                {
                  fieldOptions.filter(field => field.fieldType === fieldData.fieldType)
                    .sort((a, b) => sortFieldOptions(a, b))
                    .map((field) => {
                      return (
                        <Option key={field.fieldId} value={JSON.stringify(field)}>{field.label} [{field.platform}]</Option>
                      );
                    })
                }
              </Select>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  ) : '';
};
export default FieldView;