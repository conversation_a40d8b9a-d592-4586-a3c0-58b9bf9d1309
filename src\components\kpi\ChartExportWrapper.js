import React, { useRef, useState } from 'react';
import { 
  Box,
  Alert,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import html2canvas from 'html2canvas';

const ChartExportWrapper = ({ children, title = "Chart" }) => {
  const contentRef = useRef(null);
  const [error, setError] = useState(null);
  const [isExporting, setIsExporting] = useState(false);

  const downloadContent = async () => {
    try {
      setIsExporting(true);
      setError(null);
      if (!contentRef.current) return;
      
      const element = contentRef.current;
      const canvas = await html2canvas(element, {
        backgroundColor: '#ffffff',
        scale: 2,
        logging: false,
        useCORS: true,
        allowTaint: true
      });

      const link = document.createElement('a');
      link.download = `${title}_${new Date().toISOString().split('T')[0]}.png`;
      link.href = canvas.toDataURL('image/png');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (err) {
      console.error('Error exporting content:', err);
      setError('Failed to export content. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Box position="relative">
      <Box position="absolute" top={-24} right={8} zIndex={1}>
        <Tooltip title="Export KPI">
          <IconButton
            onClick={downloadContent}
            disabled={isExporting}
            size="small"
            sx={{
              bgcolor: 'background.paper',
              boxShadow: 1,
              '&:hover': {
                bgcolor: 'background.paper',
              }
            }}
          >
            {isExporting ? (
              <CircularProgress size={20} />
            ) : (
              <FileDownloadIcon fontSize="small" />
            )}
          </IconButton>
        </Tooltip>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box ref={contentRef}>
        {children}
      </Box>
    </Box>
  );
};

export default ChartExportWrapper;