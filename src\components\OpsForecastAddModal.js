import React, {useEffect, useState} from 'react';
import {Modal, Table, Space, Select, Divider, Input, Button, message} from 'antd';
import {PlusOutlined, DeleteOutlined} from '@ant-design/icons';

import {api} from '../pages/firebase';
// TODO launch date, and end date
const OpsForecastAddModal = ({
  open,
  setOpen,
  selectedMonth,
  productData,
  setProductData,
  variantData,
  setVariantData,
  channelData,
  setChannelData,
}) => {
  const [productsFilter, setProductsFilter] = useState([]);
  const [lifestatusFilter, setLifestatusFilter] = useState([]);
  const [brandFilter, setBrandFilter] = useState([]);
  const [brandOptions, setBrandOptions] = useState([]);
  const [productOptions, setProductOptions] = useState([]);
  const [lifestatusOptions, setLifestatusOptions] = useState([]);
  const [variantOptions, setVariantOptions] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [newSalesChannel, setNewSalesChannel] = useState('');
  const [salesChannelOptions, setSalesChannelOptions] = useState([]);
  const [selectedSalesChannels, setSelectedSalesChannels] = useState([]);

  const [loadingVariantData, setLoadingVariantData] = useState(true);
  const onSelectChange = (newSelectedRowKeys) => {
    console.log('selectedRowKeys changed: ', newSelectedRowKeys);
    setSelectedRowKeys(newSelectedRowKeys);
  };
  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };
  // const hasSelected = selectedRowKeys.length > 0;

  const fetchVariants = async () => {
    setLoadingVariantData(true);
    try {
      const netsuiteSalesChannels = await api.makeSuiteQlQuery({ query: 'select s.id id, s.name channelname from saleschannel s', paginate: true });
      // eslint-disable-next-line max-len
      const itemQuery = `SELECT sku,upc,lifeStatus,productSpecification,lifeStatus,productType,brand,description from \`hj-reporting.items.variants\` WHERE upc NOT IN (${variantData.map((x) => `'${x.upc}'`).join(',')});`;
      const variantQueryRes = await api.runQueryOnCall({options: {query: itemQuery}});
      // debugger;
      const uniqueBrands = new Set(variantQueryRes.data.map((x) => x.brand));
      const uniqueProducts = new Set(variantQueryRes.data.map((x) => x.productType));
      const uniqueLifeStatus = new Set(variantQueryRes.data.map((x) => x.lifeStatus));
      console.log('lists', uniqueBrands, uniqueProducts, uniqueLifeStatus);
      setBrandOptions([...uniqueBrands]);
      setProductOptions([...uniqueProducts]);
      setLifestatusOptions([...uniqueLifeStatus]);
      setVariantOptions(variantQueryRes.data);
      setSalesChannelOptions(netsuiteSalesChannels.data.map((x) => x.channelname));
    } catch (error) {
      console.error('Error fetching variants:', error);
    } finally {
      setLoadingVariantData(false);
    }
  };


  useEffect(() => {
    // debugger;
    if (!open || !variantData || variantData.length < 1) return;
    fetchVariants();
  }, [open, variantData]);
  const columns = [
    {
      title: 'Product',
      dataIndex: 'productType',
      key: 'productType',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'UPC',
      dataIndex: 'upc',
      key: 'upc',
    },
    {
      title: 'Life Status',
      dataIndex: 'lifeStatus',
      key: 'lifeStatus',
    },
    {
      title: 'Specification',
      dataIndex: 'productSpecification',
      key: 'productSpecification',
    },
    {
      title: 'Brand',
      dataIndex: 'brand',
      key: 'brand',
      hidden: true,
    },
  ];

  return (
    <Modal
      title="Add To Forecast"
      style={{height: 'calc(100vh - 40px)', minWidth: '1000px'}}
      open={open}
      onOk={async () => {
        const forecastData = [];
        const dtStr = `${selectedMonth.$y}-${String(selectedMonth.$M + 1).padStart(2, '0')}`;
        for (const upc of selectedRowKeys) {
          for (const salesChannel of selectedSalesChannels) {
            const forecastObj = {
              uniqueKey: `${dtStr}_${upc}_${salesChannel}`,
              month: dtStr,
              upc: upc,
              salesChannel,
              forecast: 0,
            };
            forecastData.push(forecastObj);
          }
        }
        setOpen(false);
        message.loading('Adding to Forecast', 0);
        await api.upsertOnCall({datasetId: 'forecasts', table: 'opsForecast', key: 'uniqueKey', rows: forecastData});
        message.destroy();
        fetchOpsForecast();
        message.success('Forecast Saved', {duration: 2});
        // Close modal
      }}
      onCancel={() => {
        setOpen(false);
      }}>
      {loadingVariantData ? (
        <>Loading...</>
      ) : (
        <>
          <Space direction="horizontal">
            <Select style={{minWidth: '150px'}} placeholder="Brand..." allowClear
              value={brandFilter} options={brandOptions.map((x) => ({value: x, label: x}))} onChange={setBrandFilter} />
            <Select style={{minWidth: '150px'}} placeholder="Product..." allowClear
              value={productsFilter} options={productOptions.map((x) => ({value: x, label: x}))} onChange={setProductsFilter} />
            <Select style={{minWidth: '150px'}} placeholder="Life Status..." allowClear
              value={lifestatusFilter} options={lifestatusOptions.map((x) => ({value: x, label: x}))} onChange={setLifestatusFilter} />
            <Select style={{minWidth: '200px'}} placeholder="Sales Channels" allowClear mode='multiple'
              value={selectedSalesChannels} options={salesChannelOptions.map((x) => ({value: x, label: x}))} onChange={setSelectedSalesChannels}
              optionRender={(el) => (
                <Space direction='horizontal'>
                  <span style={{width: '90%', paddingRight: '50px'}}>{el.label}</span>
                  <DeleteOutlined onClick={(e) => {
                    e.stopPropagation();
                    if (carrier === el.label) setCarrier(null);
                    setSalesChannelOptions(salesChannelOptions.filter((c) => c !== el.label));
                  }} />
                </Space>
              )}
              dropdownRender={(menu) => (
                <>
                  {menu}
                  <Divider style={{margin: '8px 0'}} />
                  <Space style={{padding: '0 8px 4px'}}>
                    <Input
                      placeholder="Add Sales Channel"
                      value={newSalesChannel}
                      onChange={setNewSalesChannel}
                      onKeyDown={(e) => e.stopPropagation()}
                    />
                    <Button type="text" icon={<PlusOutlined />} onClick={()=>setSalesChannelOptions(...salesChannelOptions, newSalesChannel)} />
                  </Space>
                </>
              )}
            />
          </Space>
          <Table
            style={{maxHeight: 'calc(100vh - 200px)', overflowY: 'auto'}}
            columns={columns}
            dataSource={variantOptions.filter((x) => {
              if (productsFilter && productsFilter.length && !productsFilter.includes(x.productType)) {
                return false;
              }
              if (brandFilter && brandFilter != x.brand) {
                return false;
              }
              return true;
            })}
            rowSelection={rowSelection}
            rowKey={'upc'}
            size={'small'}
          />
        </>
      )}
    </Modal>
  );
};

export default OpsForecastAddModal;
