import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Modal, Form, Input, Select, Button, Row, Col, Tag, Space, message, DatePicker } from 'antd';
import { AgGridReact } from 'ag-grid-react';
import { collection, query, where, addDoc, updateDoc, deleteDoc, doc, onSnapshot } from 'firebase/firestore';
import { db, api } from '../../pages/firebase';
import { EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { themeBalham } from 'ag-grid-community';

const { TextArea } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const NotesModal = ({ 
  visible, 
  onClose, 
  initialSelection = { upcs: [], forecastNodes: [], dateRange: null },
  rowData = []
}) => {
  const [form] = Form.useForm();
  const gridRef = useRef(null);
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editingNote, setEditingNote] = useState(null);
  const [viewMode, setViewMode] = useState('list'); // 'list', 'create', 'edit'
  
  // Selection state - starts with initial selection but user can modify
  const [selection, setSelection] = useState({
    upcs: initialSelection.upcs || [],
    forecastNodes: initialSelection.forecastNodes || [],
    dateRange: initialSelection.dateRange || null
  });
  
  // Date range state for new notes
  const [dateRange, setDateRange] = useState(null);
  
  // Initialize date range from initial selection if available
  useEffect(() => {
    if (initialSelection && initialSelection.dateRange) {
      console.log('NotesModal: initialSelection.dateRange:', initialSelection.dateRange);
      // Use the date range directly from the initial selection
      // Parse the date strings with Day.js
      const startDate = dayjs(initialSelection.dateRange.start);
      const endDate = dayjs(initialSelection.dateRange.end);
      console.log('NotesModal: parsed startDate:', startDate, 'endDate:', endDate);
      
      if (startDate.isValid() && endDate.isValid()) {
        setDateRange([startDate, endDate]);
      } else {
        console.error('Invalid dates:', initialSelection.dateRange);
        setDateRange(null);
      }
    }
  }, [initialSelection]);
  
  // Get unique values from rowData for selection options
  const availableOptions = useMemo(() => {
    // Create UPC options with descriptive labels
    const upcMap = new Map();
    rowData.forEach(row => {
      if (row.upc) {
        const label = `${row.upc} - ${row.product || ''} ${row.color || ''} ${row.size || ''}`.trim();
        upcMap.set(row.upc, label);
      }
    });
    const upcs = Array.from(upcMap.entries()).sort((a, b) => a[0].localeCompare(b[0]));
    
    const forecastNodes = [...new Set(rowData.map(row => row.forecast_node))].sort();
    const dates = [...new Set(rowData.map(row => {
      // Extract dates from the row data structure
      const dateFields = Object.keys(row).filter(key => 
        key.match(/^\d{4}-\d{2}-\d{2}$/) || 
        key.match(/^(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d{2}$/)
      );
      return dateFields;
    }).flat())].sort();
    
    return { upcs, forecastNodes, dates };
  }, [rowData]);

  // Fetch notes based on current selection
  useEffect(() => {
    if (!visible) {
      setNotes([]);
      return;
    }

    // Build query conditions dynamically based on what's selected
    const conditions = [];
    
    if (selection.upcs.length > 0) {
      conditions.push(where('upcs', 'array-contains-any', selection.upcs));
    }
    
    if (selection.forecastNodes.length > 0) {
      conditions.push(where('forecast_nodes', 'array-contains-any', selection.forecastNodes));
    }

    // If no conditions, fetch all notes
    if (conditions.length === 0) {
      const notesQuery = query(collection(db, 'demandPlanNotes'));
      const unsubscribe = onSnapshot(notesQuery, (snap) => {
        let notesData = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        
        // Apply date range filter on the client side if dateRange is selected
        if (dateRange && dateRange[0] && dateRange[1]) {
          const startDate = dayjs(dateRange[0]);
          const endDate = dayjs(dateRange[1]);
          
          notesData = notesData.filter(note => {
            if (!note.date_range) return false;
            
            const noteStart = dayjs(note.date_range.start);
            const noteEnd = dayjs(note.date_range.end);
            
            // Check if there's any overlap between the selected date range and the note's date range
            return (noteStart.isBefore(endDate) || noteStart.isSame(endDate)) && 
                   (noteEnd.isAfter(startDate) || noteEnd.isSame(startDate));
          });
        }
        
        // Sort by type priority: Todo > Review > General
        const typePriority = { todo: 1, review: 2, general: 3 };
        notesData.sort((a, b) => typePriority[a.type] - typePriority[b.type]);
        setNotes(notesData);
      });
      return unsubscribe;
    }

    const notesQuery = query(
      collection(db, 'demandPlanNotes'),
      ...conditions
    );

    const unsubscribe = onSnapshot(notesQuery, (snap) => {
      let notesData = snap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      
              // Apply date range filter on the client side if dateRange is selected
        if (dateRange && dateRange[0] && dateRange[1]) {
          const startDate = dayjs(dateRange[0]);
          const endDate = dayjs(dateRange[1]);
          
          notesData = notesData.filter(note => {
            if (!note.date_range) return false;
            
            const noteStart = dayjs(note.date_range.start);
            const noteEnd = dayjs(note.date_range.end);
            
            // Check if there's any overlap between the selected date range and the note's date range
            return (noteStart.isBefore(endDate) || noteStart.isSame(endDate)) && 
                   (noteEnd.isAfter(startDate) || noteEnd.isSame(startDate));
          });
        }
      
      // Sort by type priority: Todo > Review > General
      const typePriority = { todo: 1, review: 2, general: 3 };
      notesData.sort((a, b) => typePriority[a.type] - typePriority[b.type]);
      setNotes(notesData);
    });

    return () => unsubscribe();
  }, [visible, selection, dateRange]);

  // Column definitions for ag-grid
  const columnDefs = useMemo(() => [
    {
      field: 'type',
      headerName: 'Type',
      width: 100,
      cellRenderer: (params) => {
        const typeColors = {
          todo: '#ff4d4f',
          review: '#fa8c16',
          general: '#1890ff'
        };
        const typeLabels = {
          todo: 'Todo',
          review: 'Review',
          general: 'General'
        };
        return (
          <Tag color={typeColors[params.value]} style={{ margin: 0 }}>
            {typeLabels[params.value]}
          </Tag>
        );
      },
      cellStyle: (params) => {
        const typeColors = {
          todo: '#fff1f0',
          review: '#fff7e6',
          general: '#f0f8ff'
        };
        return { backgroundColor: typeColors[params.value] };
      }
    },
    {
      field: 'upcs',
      headerName: 'UPCs',
      width: 150,
      cellRenderer: (params) => (
        <div>
          {params.value?.slice(0, 2).map(upc => (
            <Tag key={upc} size="small" style={{ margin: '2px' }}>
              {upc}
            </Tag>
          ))}
          {params.value?.length > 2 && (
            <Tag size="small">+{params.value.length - 2}</Tag>
          )}
        </div>
      )
    },
    {
      field: 'forecast_nodes',
      headerName: 'Forecast Nodes',
      width: 200,
      cellRenderer: (params) => (
        <div>
          {params.value?.slice(0, 2).map(node => (
            <Tag key={node} size="small" style={{ margin: '2px' }}>
              {node}
            </Tag>
          ))}
          {params.value?.length > 2 && (
            <Tag size="small">+{params.value.length - 2}</Tag>
          )}
        </div>
      )
    },
    {
      field: 'date_range',
      headerName: 'Date Range',
      width: 150,
      cellRenderer: (params) => {
        if (!params.value) return '-';
        const start = new Date(params.value.start);
        const end = new Date(params.value.end);
        return (
          <div>
            <Tag size="small" style={{ margin: '2px' }}>
              {start.toLocaleDateString()} - {end.toLocaleDateString()}
            </Tag>
          </div>
        );
      }
    },
    {
      field: 'note',
      headerName: 'Content',
      flex: 1,
      cellRenderer: (params) => (
        <div style={{ 
          whiteSpace: 'pre-wrap', 
          maxHeight: '60px', 
          overflow: 'hidden',
          textOverflow: 'ellipsis'
        }}>
          {params.value}
        </div>
      )
    },
    {
      headerName: 'Actions',
      width: 120,
      cellRenderer: (params) => (
        <Space>
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditNote(params.data)}
          />
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteNote(params.data.id)}
          />
        </Space>
      )
    }
  ], []);

  const handleCreateNote = async (values) => {
    if (selection.upcs.length === 0 || selection.forecastNodes.length === 0 || !dateRange) {
      message.error('Please select at least one UPC, forecast node, and date range');
      return;
    }

    setLoading(true);
    try {
      const noteData = {
        upcs: selection.upcs,
        forecast_nodes: selection.forecastNodes,
        date_range: {
          start: dateRange[0].toISOString(),
          end: dateRange[1].toISOString()
        },
        note: values.note,
        type: values.type,
        created_at: new Date(),
        updated_at: new Date()
      };

      await addDoc(collection(db, 'demandPlanNotes'), noteData);
      message.success('Note created successfully');
      form.resetFields();
      setDateRange(null);
      setViewMode('list');
    } catch (error) {
      message.error('Failed to create note: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleEditNote = (note) => {
    setEditingNote(note);
    // Set the date range from the note's date_range
    if (note.date_range && note.date_range.start && note.date_range.end) {
      setDateRange([
        dayjs(note.date_range.start),
        dayjs(note.date_range.end)
      ]);
    } else {
      setDateRange(null);
    }
    form.setFieldsValue({
      note: note.note,
      type: note.type
    });
    setViewMode('edit');
  };

  const handleUpdateNote = async (values) => {
    if (!dateRange || dateRange.length !== 2) {
      message.error('Please select a valid date range');
      return;
    }

    setLoading(true);
    try {
      await updateDoc(doc(db, 'demandPlanNotes', editingNote.id), {
        note: values.note,
        type: values.type,
        date_range: {
          start: dateRange[0].toISOString(),
          end: dateRange[1].toISOString()
        },
        updated_at: new Date()
      });
      message.success('Note updated successfully');
      form.resetFields();
      setEditingNote(null);
      setDateRange(null);
      setViewMode('list');
    } catch (error) {
      message.error('Failed to update note: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteNote = async (noteId) => {
    try {
      await deleteDoc(doc(db, 'demandPlanNotes', noteId));
      message.success('Note deleted successfully');
    } catch (error) {
      message.error('Failed to delete note: ' + error.message);
    }
  };

  const handleClose = () => {
    setViewMode('list');
    setEditingNote(null);
    setDateRange(null);
    form.resetFields();
    setSelection({ upcs: [], forecastNodes: [], dateRange: null });
    onClose();
  };

  // Render different views based on viewMode
  const renderListView = () => (
    <>
      {/* Selection Panel */}
      <div style={{ marginBottom: 16, padding: 16, border: '1px solid #d9d9d9', borderRadius: 6 }}>
        <h4>Selection</h4>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <div>
              <strong>UPCs ({selection.upcs.length})</strong>
              <Select
                mode="multiple"
                style={{ width: '100%', marginTop: 8 }}
                placeholder="Select UPCs"
                value={selection.upcs}
                onChange={(values) => setSelection(prev => ({ ...prev, upcs: values }))}
                options={availableOptions.upcs.map(([upc, label]) => ({ label: label, value: upc }))}
                maxTagCount={3}
                allowClear={true}
                filterOption={(input, option) => {
                  const searchText = input.toLowerCase();
                  const upc = option.value.toLowerCase();
                  const label = option.label.toLowerCase();
                  return upc.includes(searchText) || label.includes(searchText);
                }}
                showSearch
              />
            </div>
          </Col>
          <Col span={8}>
            <div>
              <strong>Forecast Nodes ({selection.forecastNodes.length})</strong>
              <Select
                mode="multiple"
                style={{ width: '100%', marginTop: 8 }}
                placeholder="Select Forecast Nodes"
                value={selection.forecastNodes}
                onChange={(values) => setSelection(prev => ({ ...prev, forecastNodes: values }))}
                options={availableOptions.forecastNodes.map(node => ({ label: node, value: node }))}
                maxTagCount={3}
                allowClear={true}
              />
            </div>
          </Col>
          <Col span={8}>
            <div>
              <strong>Date Range</strong>
              <RangePicker
                style={{ width: '100%', marginTop: 8 }}
                placeholder={['Start Date', 'End Date']}
                value={dateRange}
                onChange={setDateRange}
                format="YYYY-MM-DD"
              />
            </div>
          </Col>
        </Row>
      </div>

      {/* Notes Grid */}
      <div style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle" style={{ marginBottom: 8 }}>
          <Col>
            <h4>Notes ({notes.length})</h4>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setViewMode('create')}
            >
              Create Note
            </Button>
          </Col>
        </Row>
        <div style={{ height: '400px', width: '100%' }}>
          <AgGridReact
            ref={gridRef}
            columnDefs={columnDefs}
            rowData={notes}
            pagination={true}
            paginationPageSize={20}
            animateRows={false}
            getRowId={(params) => params.data.id}
            rowSelection="single"
            suppressRowClickSelection={true}
            theme={themeBalham}
          />
        </div>
      </div>
    </>
  );

  const renderCreateView = () => (
    <div style={{ padding: 16 }}>
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <h3>Create New Note</h3>
        </Col>
        <Col>
          <Button onClick={() => setViewMode('list')}>
            Back to List
          </Button>
        </Col>
      </Row>
      
      <div style={{ marginBottom: 24, padding: 16, border: '1px solid #d9d9d9', borderRadius: 6 }}>
        <h4>Note Details</h4>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <div>
              <strong>UPCs</strong>
              <Select
                mode="multiple"
                style={{ width: '100%', marginTop: 8 }}
                placeholder="Select UPCs"
                value={selection.upcs}
                onChange={(values) => setSelection(prev => ({ ...prev, upcs: values }))}
                options={availableOptions.upcs.map(([upc, label]) => ({ label: label, value: upc }))}
                maxTagCount={3}
                filterOption={(input, option) => {
                  const searchText = input.toLowerCase();
                  const upc = option.value.toLowerCase();
                  const label = option.label.toLowerCase();
                  return upc.includes(searchText) || label.includes(searchText);
                }}
                showSearch
              />
            </div>
          </Col>
          <Col span={8}>
            <div>
              <strong>Forecast Nodes</strong>
              <Select
                mode="multiple"
                style={{ width: '100%', marginTop: 8 }}
                placeholder="Select Forecast Nodes"
                value={selection.forecastNodes}
                onChange={(values) => setSelection(prev => ({ ...prev, forecastNodes: values }))}
                options={availableOptions.forecastNodes.map(node => ({ label: node, value: node }))}
                maxTagCount={3}
                allowClear={false}
              />
            </div>
          </Col>
          <Col span={8}>
            <div>
              <strong>Date Range</strong>
              <RangePicker
                style={{ width: '100%', marginTop: 8 }}
                placeholder={['Start Date', 'End Date']}
                value={dateRange}
                onChange={setDateRange}
                format="YYYY-MM-DD"
              />
            </div>
          </Col>
        </Row>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleCreateNote}
      >
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="type"
              label="Note Type"
              rules={[{ required: true, message: 'Please select a note type' }]}
            >
              <Select placeholder="Select note type">
                <Option value="todo">Todo</Option>
                <Option value="review">Review</Option>
                <Option value="general">General</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={16}>
            <Form.Item
              name="note"
              label="Note Content"
              rules={[{ required: true, message: 'Please enter note content' }]}
            >
              <TextArea
                rows={6}
                placeholder="Enter your note here..."
                maxLength={1000}
                showCount
              />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                Create Note
              </Button>
              <Button onClick={() => setViewMode('list')}>
                Cancel
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>
    </div>
  );

  const renderEditView = () => (
    <div style={{ padding: 16 }}>
      <Row justify="space-between" align="middle" style={{ marginBottom: 24 }}>
        <Col>
          <h3>Edit Note</h3>
        </Col>
        <Col>
          <Button onClick={() => setViewMode('list')}>
            Back to List
          </Button>
        </Col>
      </Row>
      
      <div style={{ marginBottom: 24, padding: 16, border: '1px solid #d9d9d9', borderRadius: 6 }}>
        <h4>Note Details</h4>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <div>
              <strong>UPCs</strong>
              <div style={{ marginTop: 8 }}>
                {editingNote?.upcs?.map(upc => (
                  <Tag key={upc} style={{ margin: '2px' }}>{upc}</Tag>
                ))}
              </div>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <strong>Forecast Nodes</strong>
              <div style={{ marginTop: 8 }}>
                {editingNote?.forecast_nodes?.map(node => (
                  <Tag key={node} style={{ margin: '2px' }}>{node}</Tag>
                ))}
              </div>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <strong>Date Range</strong>
              <RangePicker
                style={{ width: '100%', marginTop: 8 }}
                placeholder={['Start Date', 'End Date']}
                value={dateRange}
                onChange={setDateRange}
                format="YYYY-MM-DD"
              />
            </div>
          </Col>
        </Row>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleUpdateNote}
      >
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="type"
              label="Note Type"
              rules={[{ required: true, message: 'Please select a note type' }]}
            >
              <Select placeholder="Select note type">
                <Option value="todo">Todo</Option>
                <Option value="review">Review</Option>
                <Option value="general">General</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={16}>
            <Form.Item
              name="note"
              label="Note Content"
              rules={[{ required: true, message: 'Please enter note content' }]}
            >
              <TextArea
                rows={6}
                placeholder="Enter your note here..."
                maxLength={1000}
                showCount
              />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                Update Note
              </Button>
              <Button onClick={() => setViewMode('list')}>
                Cancel
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>
    </div>
  );

  return (
    <Modal
      title="Notes"
      open={visible}
      onCancel={handleClose}
      footer={null}
      width="95vw"
      style={{ top: 20 }}
      destroyOnClose
    >
      {viewMode === 'list' && renderListView()}
      {viewMode === 'create' && renderCreateView()}
      {viewMode === 'edit' && renderEditView()}
    </Modal>
  );
};

export default NotesModal;
