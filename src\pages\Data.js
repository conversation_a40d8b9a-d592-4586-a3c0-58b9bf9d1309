import React, { useState, useEffect } from 'react';
import { Layout, Tabs, Button, Table, Card, Typography, Tag, Popconfirm, Spin } from 'antd';
import { Modal, Form, Input, Select, message } from 'antd';
import { db, api } from './firebase';
import { getDocs, collection, doc, deleteDoc, onSnapshot, addDoc, setDoc } from 'firebase/firestore';
import AddSavedQueryModal from '../components/settings/AddSavedQueryModal';
import { TableOutlined, SearchOutlined, PlusOutlined } from '@ant-design/icons';
const { Content } = Layout;
import { SYSTEMS } from '../constants';
const { Text } = Typography;
import ConnectorsTab from '../components/ConnectorsTab';
import DatasetsTab from '../components/DatasetsTab';
const Data = () => {
  const tabs = [{
    key: '1',
    label: 'Connectors',
    children: <ConnectorsTab />,
  }, {
    key: '2',
    label: 'Datasets',
    children: <DatasetsTab />,
  },
  ];
  return (
    <Layout>
      <Content>
        <h1>Data</h1>
        <Tabs
          items={tabs}
        />
      </Content>
    </Layout>
  );
};

export default Data;