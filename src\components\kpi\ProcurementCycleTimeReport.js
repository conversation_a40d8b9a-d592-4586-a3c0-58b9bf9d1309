import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    Grid,
    Paper,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    CircularProgress,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow
} from '@mui/material';
import {
    ComposedChart,
    Line,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import { useFormatAxisTick } from '../../hooks/useFormatAxisTick';
import ChartExportWrapper from './ChartExportWrapper';

const ProcurementCycleTimeReport = () => {
    const [cycleTimeData, setCycleTimeData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedSupplier, setSelectedSupplier] = useState('');
    const [allSuppliers, setAllSuppliers] = useState([]);
    const formatAxisTick = useFormatAxisTick({ currency: '', decimalPlaces: 0 }); // No currency symbol for order counts

    const CHART_COLORS = {
        cycleTime: '#82ca9d',
        orderCount: '#8884d8',
        lightGreen: '#e8f5e9',
        lightPurple: '#f3f1ff',
        goalLine: '#ff9800'  // Added color for goal lines
    };

    const functions = getFunctions();
    const getProcurementCycleTime = httpsCallable(functions, 'getProcurementCycleTime');

    const daysToWeeks = (days) => {
        return days / 7;
    };

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setIsLoading(true);
        try {
            const [cycleTimeResult, goalsResult] = await Promise.all([
                getProcurementCycleTime(),
                httpsCallable(functions, 'getKPIGoalsForReport')({ reportName: 'procurement-cycle' })
            ]);

            if (cycleTimeResult.data && cycleTimeResult.data.monthlyData) {
                const currentDate = new Date();
                const filteredData = {
                    ...cycleTimeResult.data,
                    monthlyData: cycleTimeResult.data.monthlyData
                        .filter(month => new Date(month.month) <= currentDate)
                        .sort((a, b) => new Date(a.month) - new Date(b.month))
                };
                setCycleTimeData(filteredData);
                setAllSuppliers(['All Suppliers', ...new Set(filteredData.monthlyData.flatMap(month =>
                    month.suppliers.map(s => s.name)))]);
            }

            if (goalsResult.data.success) {
                setKpiGoals(goalsResult.data.data);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleSupplierChange = (event) => {
        setSelectedSupplier(event.target.value);
    };

    const formatNumber = (num) => {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    };

    const getFilteredChartData = () => {
        if (!cycleTimeData || !cycleTimeData.monthlyData) return [];

        return cycleTimeData.monthlyData.map(monthData => {
            if (selectedSupplier === '' || selectedSupplier === 'All Suppliers') {
                return {
                    month: monthData.month,
                    averageCycleTime: parseFloat(monthData.averageCycleTime),
                    averageCycleTimeWeeks: daysToWeeks(parseFloat(monthData.averageCycleTime)),
                    orderCount: monthData.orderCount,
                };
            } else {
                const supplierData = monthData.suppliers.find(s => s.name === selectedSupplier) || {
                    averageCycleTime: '0',
                    orderCount: 0,
                };
                return {
                    month: monthData.month,
                    averageCycleTime: parseFloat(supplierData.averageCycleTime),
                    averageCycleTimeWeeks: daysToWeeks(parseFloat(supplierData.averageCycleTime)),
                    orderCount: supplierData.orderCount,
                };
            }
        });
    };

    const getFilteredSupplierPerformance = () => {
        if (!cycleTimeData || !cycleTimeData.supplierPerformance) return [];

        return cycleTimeData.supplierPerformance
            .filter(supplier => selectedSupplier === '' || selectedSupplier === 'All Suppliers' || supplier.name === selectedSupplier)
            .map(supplier => ({
                ...supplier,
                averageCycleTime: supplier.averageCycleTime === 'NaN' ? 'N/A' : `${supplier.averageCycleTime} days (${daysToWeeks(parseFloat(supplier.averageCycleTime)).toFixed(1)} weeks)`
            }));
    };

    const calculateOverallMetrics = () => {
        if (!cycleTimeData) return {
            averageCycleTime: 0,
            averageCycleTimeWeeks: 0,
            totalOrders: 0
        };

        if (selectedSupplier && selectedSupplier !== 'All Suppliers') {
            const supplierData = cycleTimeData.supplierPerformance.find(s => s.name === selectedSupplier);
            if (supplierData) {
                const days = parseFloat(supplierData.averageCycleTime);
                return {
                    averageCycleTime: days,
                    averageCycleTimeWeeks: daysToWeeks(days),
                    totalOrders: supplierData.orderCount
                };
            }
        }

        return {
            averageCycleTime: parseFloat(cycleTimeData.overallAverageCycleTime),
            averageCycleTimeWeeks: daysToWeeks(parseFloat(cycleTimeData.overallAverageCycleTime)),
            totalOrders: cycleTimeData.totalOrders
        };
    };

    const calculateTrend = () => {
        if (!cycleTimeData?.monthlyData || cycleTimeData.monthlyData.length < 2) return 0;
        const relevantData = getFilteredChartData();
        if (relevantData.length < 2) return 0;
        const lastTwo = relevantData.slice(-2);
        return daysToWeeks(lastTwo[1].averageCycleTime - lastTwo[0].averageCycleTime);
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.95)', maxWidth: 250 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>{`Month: ${label}`}</Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Typography variant="body2" color="primary">
                            Average Cycle Time: {`${data.averageCycleTime.toFixed(1)} days (${data.averageCycleTimeWeeks.toFixed(1)} weeks)`}
                        </Typography>
                        <Typography variant="body2">
                            Order Count: {data.orderCount}
                        </Typography>
                    </Box>
                </Paper>
            );
        }
        return null;
    };
    // Calculate domains including the goal values
    const calculateDomains = (data) => {
        if (!data?.length) return { cycleTimeDomain: [0, 100], orderCountDomain: [0, 100] };

        const cycleTimeValues = data.map(item => item.averageCycleTime);
        const orderCountValues = data.map(item => item.orderCount);

        // Include goal values in cycle time domain calculation
        const goalMin = kpiGoals?.['Procurement Cycle Time']?.min;
        const goalMax = kpiGoals?.['Procurement Cycle Time']?.max;

        // Convert goal values from weeks to days for domain calculation
        const goalMinDays = goalMin ? parseFloat(goalMin) * 7 : null;
        const goalMaxDays = goalMax ? parseFloat(goalMax) * 7 : null;

        const allCycleTimeValues = [
            ...cycleTimeValues,
            goalMinDays,
            goalMaxDays
        ].filter(Boolean);

        return {
            cycleTimeDomain: [
                0,
                Math.ceil(Math.max(...allCycleTimeValues) * 1.1)
            ],
            orderCountDomain: [0, Math.ceil(Math.max(...orderCountValues) * 1.1)]
        };
    };

    // Use the KPI reference lines hook
    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: {
            ...kpiGoals?.['Procurement Cycle Time'],
            // Convert weeks to days for the reference lines
            min: kpiGoals?.['Procurement Cycle Time']?.min * 7,
            max: kpiGoals?.['Procurement Cycle Time']?.max * 7,
            value: kpiGoals?.['Procurement Cycle Time']?.value * 7
        },
        yAxisId: "left",
        styles: {
            stroke: CHART_COLORS.goalLine,
            strokeDasharray: "3 3",
            label: {
                fill: CHART_COLORS.goalLine,
                fontSize: 12,
                position: "right"
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Target Min",
            max: "Target Max"
        }
    });
    if (isLoading) return <CircularProgress />;
    if (!cycleTimeData || !kpiGoals) return null;

    const filteredChartData = getFilteredChartData();
    const { cycleTimeDomain, orderCountDomain } = calculateDomains(filteredChartData);
    const { averageCycleTime, averageCycleTimeWeeks, totalOrders } = calculateOverallMetrics();
    const cycleTimeConfig = kpiGoals['Procurement Cycle Time'];

    return (
        <ChartExportWrapper title={`Procurement_Cycle_Time${selectedSupplier ? `_${selectedSupplier}` : ''}`}>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <FormControl sx={{ minWidth: 200 }}>
                    <InputLabel id="supplier-select-label">Filter by Supplier</InputLabel>
                    <Select
                        labelId="supplier-select-label"
                        id="supplier-select"
                        value={selectedSupplier}
                        onChange={handleSupplierChange}
                        label="Filter by Supplier"
                    >
                        {allSuppliers.map((supplier) => (
                            <MenuItem key={supplier} value={supplier}>
                                {supplier}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            </Box>

            <ResponsiveContainer width="100%" height={400}>
                <ComposedChart data={filteredChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis
                        yAxisId="left"
                        orientation="left"
                        stroke={CHART_COLORS.cycleTime}
                        domain={cycleTimeDomain}
                        label={{ value: 'Cycle Time (Days)', angle: -90, position: 'insideLeft' }}
                    />
                    <YAxis
                        yAxisId="right"
                        orientation="right"
                        stroke={CHART_COLORS.orderCount}
                        domain={orderCountDomain}
                        label={{ value: 'Order Count', angle: 90, position: 'insideRight' }}
                        tickFormatter={formatAxisTick}  // Add this line
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    {goalReferenceLines}
                    <Bar
                        yAxisId="right"
                        dataKey="orderCount"
                        name="Order Count"
                        fill={CHART_COLORS.orderCount}
                    />
                    <Line
                        yAxisId="left"
                        type="monotone"
                        dataKey="averageCycleTime"
                        name="Cycle Time (Days)"
                        stroke={CHART_COLORS.cycleTime}
                        strokeWidth={2}
                        dot={{ r: 4, fill: CHART_COLORS.cycleTime }}
                        activeDot={{ r: 8 }}
                    />
                </ComposedChart>
            </ResponsiveContainer>

            <Box sx={{ mt: 4 }}>
                <Typography variant="h6" gutterBottom>Performance Summary</Typography>
                <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={4}>
                        <KPICard
                            title="Average Cycle Time (Days)"
                            value={`${averageCycleTime.toFixed(1)} days`}
                            bgColor="#f0fff0"
                            textColor="primary"
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                        <KPICard
                            title="Average Cycle Time (Weeks)"
                            value={`${averageCycleTimeWeeks.toFixed(1)} weeks`}
                            bgColor="#fff5f5"
                            textColor="error.main"
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                        <KPICard
                            title="Total Orders"
                            value={formatNumber(totalOrders)}
                            bgColor={CHART_COLORS.lightPurple}
                            textColor={CHART_COLORS.orderCount}
                        />
                    </Grid>
                </Grid>
            </Box>

            <Box sx={{ mt: 4 }}>
                <GoalStatusDisplay
                    currentValue={averageCycleTimeWeeks}
                    goalConfig={cycleTimeConfig}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculateTrend()}
                    size="medium"
                    title={selectedSupplier && selectedSupplier !== 'All Suppliers'
                        ? `${selectedSupplier} Procurement Cycle Time Performance`
                        : "Overall Procurement Cycle Time Performance"
                    }
                />
            </Box>

            <Box sx={{ mt: 4 }}>
                <Typography variant="h6" gutterBottom>Supplier Performance</Typography>
                <TableContainer component={Paper}>
                    <Table>
                        <TableHead>
                            <TableRow>
                                <TableCell>Supplier</TableCell>
                                <TableCell align="right">Average Cycle Time</TableCell>
                                <TableCell align="right">Total Orders</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {getFilteredSupplierPerformance().map((supplier) => (
                                <TableRow key={supplier.name}>
                                    <TableCell component="th" scope="row">
                                        {supplier.name}
                                    </TableCell>
                                    <TableCell align="right">{supplier.averageCycleTime}</TableCell>
                                    <TableCell align="right">{formatNumber(supplier.orderCount)}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            </Box>
        </ChartExportWrapper>
    );
};

export default ProcurementCycleTimeReport;