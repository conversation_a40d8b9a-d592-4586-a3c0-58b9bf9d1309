import React, { useState, useEffect } from 'react';
import { Modal, Form, Select, Button, Row, Col, Tabs, Spin, message, DatePicker } from 'antd';
import { AgGridReact } from 'ag-grid-react';
import { AgCharts } from 'ag-charts-react';
import { themeBalham } from 'ag-grid-community';
import { api } from '../../pages/firebase';
import dayjs from 'dayjs';
const { RangePicker } = DatePicker;

const HistoricalSales = ({ open, onClose, selectedDemandPlans }) => {
  const gridRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [loadingFilters, setLoadingFilters] = useState(true);
  const [salesData, setSalesData] = useState([]);
  const [chartData, setChartData] = useState([]);
  const [dateRange, setDateRange] = useState('90');
  const [customDateRange, setCustomDateRange] = useState(null);
  const [lastFetchTime, setLastFetchTime] = useState(0);

  // Selection state - starts with initial selection but user can modify
  const [selection, setSelection] = useState({
    upcs: initialSelection.upcs || [],
    forecastNodes: initialSelection.forecastNodes || [],
    dateRange: initialSelection.dateRange || null,
    products: [],
    divisions: [],
    colors: [],
    sizes: [],
    categories: [],
    lifeStatuses: [],
    specifications: []
  });

  // Date range options
  const dateRangeOptions = [
    { value: '30', label: 'Last 30 days' },
    { value: '90', label: 'Last 90 days' },
    { value: '180', label: 'Last 180 days' },
    { value: '360', label: 'Last 360 days' },
    { value: 'custom', label: 'Custom range' }
  ];
  // State for filter options
  const [filterOptions, setFilterOptions] = useState({
    upcs: [],
    forecastNodes: [],
    products: [],
    divisions: [],
    colors: [],
    sizes: [],
    categories: [],
    lifeStatuses: [],
    specifications: []
  });

  // Fetch filter options from items.netsuite table
  const fetchFilterOptions = async () => {
    setLoadingFilters(true);
    try {
      // Fetch forecast nodes from forecast.forecast_nodes table
      const forecastNodeQuery = `
        SELECT DISTINCT code as forecast_node
        FROM \`hj-reporting.forecast.forecast_nodes\`
        WHERE code IS NOT NULL AND code != ''
        ORDER BY code
      `;

      // Fetch product attributes
      const productAttrsQuery = `
        SELECT DISTINCT
          upc,
          CONCAT(upc, ' - ', COALESCE(producttype, ''), ' ', COALESCE(color, ''), ' ', COALESCE(size, '')) as label,
          producttype as product,
          productdivision as division,
          color,
          size,
          productcategory as category,
          lifestatus,
          productspecification as specification
        FROM \`hj-reporting.items.items_netsuite\`
        WHERE upc IS NOT NULL
        LIMIT 1000
      `;

      const [{ data: forecastNodeResult }, { data: productAttrsResult }] = await Promise.all([
        api.bigQueryRunQueryOnCall({ options: { query: forecastNodeQuery } }),
        api.bigQueryRunQueryOnCall({ options: { query: productAttrsQuery } })
      ]);

      // Process UPC results
      const upcs = (productAttrsResult || []).map(row => [row.upc, row.label]);

      // Process forecast node results
      const forecastNodes = (forecastNodeResult || []).map(row => row.forecast_node);

      // Process product attribute results
      const products = [...new Set((productAttrsResult || []).map(row => row.product).filter(Boolean))].sort();
      const divisions = [...new Set((productAttrsResult || []).map(row => row.division).filter(Boolean))].sort();
      const colors = [...new Set((productAttrsResult || []).map(row => row.color).filter(Boolean))].sort();
      const sizes = [...new Set((productAttrsResult || []).map(row => row.size).filter(Boolean))].sort();
      const categories = [...new Set((productAttrsResult || []).map(row => row.category).filter(Boolean))].sort();
      const lifeStatuses = [...new Set((productAttrsResult || []).map(row => row.lifestatus).filter(Boolean))].sort();
      const specifications = [...new Set((productAttrsResult || []).map(row => row.specification).filter(Boolean))].sort();

      setFilterOptions({
        upcs,
        forecastNodes,
        products,
        divisions,
        colors,
        sizes,
        categories,
        lifeStatuses,
        specifications
      });
    } catch (error) {
      console.error('Error fetching filter options:', error);
      message.error('Failed to load filter options. Please refresh the page.');
    } finally {
      setLoadingFilters(false);
    }
  };

  // Load filter options when component mounts
  useEffect(() => {
    fetchFilterOptions();
  }, []);

  // Calculate date range based on selection
  const getDateRange = () => {
    if (dateRange === 'custom' && customDateRange) {
      return {
        start: customDateRange[0].format('YYYY-MM-DD'),
        end: customDateRange[1].format('YYYY-MM-DD')
      };
    }

    const days = parseInt(dateRange);
    const endDate = dayjs();
    const startDate = endDate.subtract(days, 'day');

    return {
      start: startDate.format('YYYY-MM-DD'),
      end: endDate.format('YYYY-MM-DD')
    };
  };

  // Fetch historical sales data
  const fetchSalesData = async () => {
    // Rate limiting - prevent multiple rapid requests
    const now = Date.now();
    if (now - lastFetchTime < 2000) { // 2 second cooldown
      message.warning('Please wait a moment before making another request');
      return;
    }
    setLastFetchTime(now);

    setLoading(true);
    try {
      const dateRange = getDateRange();

      // Validate and sanitize inputs to prevent SQL injection
      const sanitizeInput = (input) => {
        if (typeof input !== 'string') return '';
        // Remove any potentially dangerous characters and limit length
        return input.replace(/[^a-zA-Z0-9_\-\.]/g, '').substring(0, 50);
      };

      // Additional validation for array lengths to prevent excessive queries
      if (selection.upcs.length > 100 || selection.forecastNodes.length > 50) {
        throw new Error('Too many items selected. Please reduce your selection.');
      }

      // Build WHERE clause for the query with input validation
      let whereConditions = [
        `b.account IN ('40100 Gross Revenue','40200 Discounts')`
      ];
      if (selection.upcs.length > 0) {
        const sanitizedUpcs = selection.upcs.map(upc => sanitizeInput(upc)).filter(upc => upc.length > 0);
        if (sanitizedUpcs.length > 0) {
          whereConditions.push(`b.upc IN (${sanitizedUpcs.map(upc => `'${upc}'`).join(',')})`);
        }
      }

      if (selection.forecastNodes.length > 0) {
        const sanitizedNodes = selection.forecastNodes.map(node => sanitizeInput(node)).filter(node => node.length > 0);
        if (sanitizedNodes.length > 0) {
          whereConditions.push(`b.forecast_node IN (${sanitizedNodes.map(node => `'${node}'`).join(',')})`);
        }
      }

      // Add additional filters for product attributes
      if (selection.products.length > 0) {
        const sanitizedProducts = selection.products.filter(product => product.length > 0);
        if (sanitizedProducts.length > 0) {
          if (sanitizedProducts.length > 1) {
            whereConditions.push(`i.producttype IN (${sanitizedProducts.map(product => `'${product}'`).join(',')})`);
          } else {
            whereConditions.push(`i.producttype = '${sanitizedProducts[0]}'`);
          }
        }
      }

      if (selection.divisions.length > 0) {
        const sanitizedDivisions = selection.divisions.filter(division => division.length > 0);
        if (sanitizedDivisions.length > 0) {
          if (sanitizedDivisions.length > 1) {
            whereConditions.push(`i.productdivision IN (${sanitizedDivisions.map(division => `'${division}'`).join(',')})`);
          } else {
            whereConditions.push(`i.productdivision = '${sanitizedDivisions[0]}'`);
          }
        }
      }

      if (selection.colors.length > 0) {
        const sanitizedColors = selection.colors.filter(color => color.length > 0);
        if (sanitizedColors.length > 0) {
          if (sanitizedColors.length > 1) {
            whereConditions.push(`i.color IN (${sanitizedColors.map(color => `'${color}'`).join(',')})`);
          } else {
            whereConditions.push(`i.color = '${sanitizedColors[0]}'`);
          }
        }
      }

      if (selection.sizes.length > 0) {
        const sanitizedSizes = selection.sizes.filter(size => size.length > 0);
        if (sanitizedSizes.length > 0) {
          if (sanitizedSizes.length > 1) {
            whereConditions.push(`i.size IN (${sanitizedSizes.map(size => `'${size}'`).join(',')})`);
          } else {
            whereConditions.push(`i.size = '${sanitizedSizes[0]}'`);
          }
        }
      }

      if (selection.categories.length > 0) {
        const sanitizedCategories = selection.categories.filter(category => category.length > 0);
        if (sanitizedCategories.length > 0) {
          if (sanitizedCategories.length > 1) {
            whereConditions.push(`i.productcategory IN (${sanitizedCategories.map(category => `'${category}'`).join(',')})`);
          } else {
            whereConditions.push(`i.productcategory = '${sanitizedCategories[0]}'`);
          }
        }
      }

      // Validate date format (YYYY-MM-DD)
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (dateRegex.test(dateRange.start) && dateRegex.test(dateRange.end)) {
        whereConditions.push(`b.date BETWEEN '${dateRange.start}' AND '${dateRange.end}'`);
      } else {
        throw new Error('Invalid date format');
      }

      if (whereConditions.length === 0) {
        throw new Error('No valid filter conditions');
      }

      const whereClause = whereConditions.join(' AND ');

      // Build the SELECT clause
      let selectClause;
      let groupByClause;
      let orderByClause;
      selectClause = `
              FORMAT_DATE('%Y-%m-%d', b.date) as date,
              b.upc as upc,
              b.forecast_node as forecast_node,
              SUM(b.quantity) as total_quantity,
              SUM(b.amount) as total_amount,
              'sales' as source,
              i.producttype as product,
              i.color as color,
              i.size as size,
              i.productfamily as family,
              i.productcategory as category,
              i.productdivision as division,
              i.lifestatus as lifestatus,
              i.productspecification as specification
            `;
      groupByClause = `b.date, b.upc, b.forecast_node, i.producttype, i.color, i.size, i.productfamily, i.productcategory, i.productdivision, i.lifestatus, i.productspecification`;
      orderByClause = `b.date ASC, b.upc ASC, b.forecast_node ASC`;
      const query = `
          SELECT
            ${selectClause}
          FROM \`hj-reporting.sales.billed_sales\` b
          LEFT JOIN \`hj-reporting.items.items_netsuite\` i ON b.upc = i.upc
          WHERE ${whereClause}
          GROUP BY ${groupByClause}
          ORDER BY ${orderByClause}
        `;
      console.log({ query });
      // get demand_plan data
      const demandPlanWhereClauseList = [];
      if (selection.upcs.length > 0) {
        const sanitizedUpcs = selection.upcs.map(upc => sanitizeInput(upc)).filter(upc => upc.length > 0);
        if (sanitizedUpcs.length > 1) {
          demandPlanWhereClauseList.push(`d.upc IN (${sanitizedUpcs.map(upc => `'${upc}'`).join(',')})`);
        } else {
          demandPlanWhereClauseList.push(`d.upc = '${sanitizedUpcs[0]}'`);
        }
      }
      if (selection.forecastNodes.length > 0) {
        const sanitizedNodes = selection.forecastNodes.map(node => sanitizeInput(node)).filter(node => node.length > 0);
        if (sanitizedNodes.length > 1) {
          demandPlanWhereClauseList.push(`d.forecast_node IN (${sanitizedNodes.map(node => `'${node}'`).join(',')})`);
        } else {
          demandPlanWhereClauseList.push(`d.forecast_node = '${sanitizedNodes[0]}'`);
        }
      }
      // Build demand plan WHERE clause with additional filters
      if (selection.products.length > 0) {
        const sanitizedProducts = selection.products.map(product => sanitizeInput(product)).filter(product => product.length > 0);
        if (sanitizedProducts.length > 0) {
          demandPlanWhereClauseList.push(`i.producttype IN (${sanitizedProducts.map(product => `'${product}'`).join(',')})`);
        }
      }

      if (selection.divisions.length > 0) {
        const sanitizedDivisions = selection.divisions.map(division => sanitizeInput(division)).filter(division => division.length > 0);
        if (sanitizedDivisions.length > 0) {
          demandPlanWhereClauseList.push(`i.productdivision IN (${sanitizedDivisions.map(division => `'${division}'`).join(',')})`);
        }
      }

      if (selection.colors.length > 0) {
        const sanitizedColors = selection.colors.map(color => sanitizeInput(color)).filter(color => color.length > 0);
        if (sanitizedColors.length > 0) {
          demandPlanWhereClauseList.push(`i.color IN (${sanitizedColors.map(color => `'${color}'`).join(',')})`);
        }
      }

      if (selection.sizes.length > 0) {
        const sanitizedSizes = selection.sizes.map(size => sanitizeInput(size)).filter(size => size.length > 0);
        if (sanitizedSizes.length > 0) {
          demandPlanWhereClauseList.push(`i.size IN (${sanitizedSizes.map(size => `'${size}'`).join(',')})`);
        }
      }

      if (selection.categories.length > 0) {
        const sanitizedCategories = selection.categories.map(category => sanitizeInput(category)).filter(category => category.length > 0);
        if (sanitizedCategories.length > 0) {
          demandPlanWhereClauseList.push(`i.productcategory IN (${sanitizedCategories.map(category => `'${category}'`).join(',')})`);
        }
      }

      if (selection.lifeStatuses.length > 0) {
        const sanitizedLifeStatuses = selection.lifeStatuses.map(status => sanitizeInput(status)).filter(status => status.length > 0);
        if (sanitizedLifeStatuses.length > 0) {
          demandPlanWhereClauseList.push(`i.lifestatus IN (${sanitizedLifeStatuses.map(status => `'${status}'`).join(',')})`);
        }
      }

      if (selection.specifications.length > 0) {
        const sanitizedSpecifications = selection.specifications.map(spec => sanitizeInput(spec)).filter(spec => spec.length > 0);
        if (sanitizedSpecifications.length > 0) {
          demandPlanWhereClauseList.push(`i.productspecification IN (${sanitizedSpecifications.map(spec => `'${spec}'`).join(',')})`);
        }
      }

      const demandPlanWhereClause = demandPlanWhereClauseList.join(' AND ');
      const demandPlanQuery = `
        SELECT
          FORMAT_DATE('%Y-%m-%d', d.date) as date,
          d.upc as upc,
          d.forecast_node as forecast_node,
          SUM(d.qty) as total_quantity,
          i.producttype as product,
          i.color as color,
          i.size as size,
          i.productfamily as family,
          i.productcategory as category,
          i.productdivision as division,
          i.lifestatus as lifestatus,
          i.productspecification as specification,
          'forecast' as source
        FROM \`hj-reporting.forecast.demand_plan\` as d
        LEFT JOIN \`hj-reporting.items.items_netsuite\` i ON d.upc = i.upc
        WHERE ${demandPlanWhereClause} AND d.forecast_node IS NOT NULL
        GROUP BY d.date, d.upc, d.forecast_node, i.producttype, i.color, i.size, i.productfamily, i.productcategory, i.productdivision, i.lifestatus, i.productspecification
        ORDER BY d.date ASC, d.upc ASC, d.forecast_node ASC
      `;
      const dataProms = [
        api.bigQueryRunQueryOnCall({
          options: { query: demandPlanQuery }
        }),
        api.bigQueryRunQueryOnCall({
          options: { query }
        })
      ];
      const [{ data: demandPlanData }, { data: salesData }] = await Promise.all(dataProms);
      const allData = [...demandPlanData, ...salesData].sort((a, b) => new Date(a.date) - new Date(b.date));
      allData.forEach(row => {
        row.month = dayjs(row.date).format('MMM');
        row.year = dayjs(row.date).format('YYYY');
        row.week = dayjs(row.date).startOf('week').format('YYYY-MM-DD');
        // Ensure all rows have the required fields
        row.lifestatus = row.lifestatus || '';
        row.specification = row.specification || '';
      });
      setSalesData(allData || []);

      // Transform data for chart
      transformDataForChart(allData || []);
    } catch (error) {
      console.error('Error fetching sales data:', error);

      // Provide user-friendly error messages
      let errorMessage = 'Failed to fetch sales data';
      if (error.message.includes('Invalid date format')) {
        errorMessage = 'Invalid date format. Please check your date selection.';
      } else if (error.message.includes('Too many items')) {
        errorMessage = error.message;
      } else if (error.message.includes('No valid filter conditions')) {
        errorMessage = 'No valid filter conditions. Please check your selections.';
      } else if (error.message.includes('ambiguous')) {
        errorMessage = 'Database query error: Column name is ambiguous. Please contact support.';
      } else if (error.message.includes('BigQuery')) {
        errorMessage = 'Database error. Please try again later.';
      }

      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Transform data for ag-grid charting
  const transformDataForChart = (data) => {
    if (!data || data.length === 0) {
      setChartData([]);
      return;
    }

    // Validate data structure and sanitize inputs
    const sanitizedData = data.filter(row => {
      return row &&
        typeof row.date === 'string' &&
        typeof row.upc === 'string' &&
        typeof row.forecast_node === 'string' &&
        typeof row.total_quantity === 'number' &&
        row.total_quantity >= 0;
    });

    if (sanitizedData.length === 0) {
      setChartData([]);
      return;
    }

    // Group data by date and create chart series
    const dateMap = new Map();

    sanitizedData.forEach(row => {
      const date = row.date;
      const key = `${row.upc}_${row.forecast_node}`;

      if (!dateMap.has(date)) {
        dateMap.set(date, { date });
      }

      dateMap.get(date)[key] = row.total_quantity;
    });

    // Convert to array and sort by date
    const chartDataArray = Array.from(dateMap.values()).sort((a, b) => {
      return new Date(a.date) - new Date(b.date);
    });
    console.log('chartDataArray', chartDataArray.length, JSON.stringify(chartDataArray.slice(0, 10)));
    setChartData(chartDataArray);
  };

  // Handle form submission
  const handleSubmit = () => {
    if (dateRange === 'custom' && (!customDateRange || customDateRange.length !== 2)) {
      message.warning('Please select a valid custom date range');
      return;
    }
    // Since we're not using form validation for the dynamic fields, just fetch data
    fetchSalesData();
  };

  // Handle date range change
  const handleDateRangeChange = (value) => {
    setDateRange(value);
    if (value !== 'custom') {
      setCustomDateRange(null);
    }
  };

  // Chart options for ag-grid
  const chartOptions = useMemo(() => ({
    title: {
      text: `Historical Sales`,
      fontSize: 18
    },
    subtitle: {
      text: 'Historical sales (solid lines) vs Current demand plan (dashed lines)',
      fontSize: 14,
      color: '#666'
    },
    series: chartData.length > 0 ? Object.keys(chartData[0])
      .filter(key => key !== 'date')
      .slice(0, 20) // Limit to 20 series to prevent performance issues
      .map(key => ({
        type: 'line',
        xKey: 'date',
        yKey: key,
        yName: key.length > 50 ? key.substring(0, 50) + '...' : key, // Truncate long names
        stroke: '#1890ff',
        strokeWidth: 2,
        marker: {
          enabled: false
        }
      })) : [],
    axes: [
      {
        type: 'time',
        position: 'bottom',
        title: {
          text: 'Date'
        },
        label: {
          formatter: (params) => {
            return params.value.toString();
          }
        }
      },
      {
        type: 'number',
        position: 'left',
        title: {
          text: 'Quantity'
        }
      }
    ],
    legend: {
      position: 'bottom'
    },
    tooltip: {
      enabled: true
    }
  }), [chartData]);

  // Column definitions for the data table
  const columnDefs = useMemo(() => [
    {
      field: 'source',
      headerName: 'Source',
      sortable: true,
      filter: true,
      width: 120,
      valueFormatter: params => {
        // make the value proper case
        if (params.value == null) return '';
        return params.value.charAt(0).toUpperCase() + params.value.slice(1).toLowerCase();
      },
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'year',
      headerName: 'Year',
      sortable: true,
      filter: true,
      width: 120,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'month',
      headerName: 'Month',
      sortable: true,
      filter: true,
      width: 120,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'week',
      headerName: 'Week',
      sortable: true,
      filter: true,
      width: 120,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'date',
      headerName: 'Date',
      sortable: true,
      filter: true,
      width: 120,
      filter: 'agTextColumnFilter', // Use text filter for grouped dates
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'upc',
      headerName: 'UPC',
      sortable: true,
      filter: true,
      width: 150,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'forecast_node',
      headerName: 'Forecast Node',
      sortable: true,
      filter: true,
      width: 200,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'product',
      headerName: 'Product',
      sortable: true,
      filter: true,
      width: 150,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'color',
      headerName: 'Color',
      sortable: true,
      filter: true,
      width: 100,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'size',
      headerName: 'Size',
      sortable: true,
      filter: true,
      width: 80,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'family',
      headerName: 'Family',
      sortable: true,
      filter: true,
      width: 120,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'category',
      headerName: 'Category',
      sortable: true,
      filter: true,
      width: 120,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'division',
      headerName: 'Division',
      sortable: true,
      filter: true,
      width: 120,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'lifestatus',
      headerName: 'Life Status',
      sortable: true,
      filter: true,
      width: 120,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'specification',
      headerName: 'Specification',
      sortable: true,
      filter: true,
      width: 150,
      enablePivot: true,
      enableRowGroup: true,
    },
    {
      field: 'total_quantity',
      headerName: 'Quantity',
      sortable: true,
      filter: true,
      width: 100,
      aggFunc: 'sum',
      type: 'numericColumn',
      valueFormatter: params => {
        if (params.value == null) return '';
        return new Intl.NumberFormat('en-US', {
          style: 'decimal',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0
        }).format(params.value);
      }
    },
    {
      field: 'total_amount',
      headerName: 'Amount',
      sortable: true,
      filter: true,
      width: 120,
      aggFunc: 'sum',
      type: 'numericColumn',
      valueFormatter: params => {
        if (params.value == null) return '';
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(params.value);
      }
    }
  ], []);

  // Initialize selection from initialSelection
  useEffect(() => {
    if (initialSelection) {
      // Validate and sanitize initial selection
      const sanitizeArray = (arr) => {
        if (!Array.isArray(arr)) return [];
        return arr.filter(item =>
          typeof item === 'string' &&
          item.length > 0 &&
          item.length <= 50
        ).slice(0, 100); // Limit to 100 items
      };

      setSelection({
        upcs: sanitizeArray(initialSelection.upcs),
        forecastNodes: sanitizeArray(initialSelection.forecastNodes),
        dateRange: initialSelection.dateRange || null,
        products: [],
        divisions: [],
        colors: [],
        sizes: [],
        categories: [],
        lifeStatuses: [],
        specifications: []
      });
    }
  }, [initialSelection]);

  // Auto-fetch data when modal opens with initial selection
  useEffect(() => {
    if (visible && initialSelection.upcs.length > 0 && initialSelection.forecastNodes.length > 0) {
      fetchSalesData();
    }
  }, [visible, initialSelection]);

  // Tab items configuration
  const tabItems = [
    {
      key: 'chart',
      label: 'Chart View',
      children: (
        <div style={{ height: 600, width: '100%' }}>
          {loading ? (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Spin size="large" />
            </div>
          ) : chartData.length > 0 ? (
            <AgCharts options={chartOptions} />
          ) : (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', color: '#666' }}>
              No data to display. Please select filters and click &quot;Load Data&quot;.
            </div>
          )}
        </div>
      )
    },
    {
      key: 'table',
      label: 'Table View',
      children: (
        <div style={{ height: 600, width: '100%' }}>
          {loading ? (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Spin size="large" />
            </div>
          ) : (
            <AgGridReact
              ref={gridRef}
              enableRowGroup={true}
              enablePivot={true}
              columnDefs={columnDefs}
              rowData={salesData}
              pagination={true}
              paginationPageSize={50}
              animateRows={false}
              theme={themeBalham}
              sideBar={{
                toolPanels: [
                  {
                    id: 'columns',
                    labelDefault: 'Columns',
                    labelKey: 'columns',
                    iconKey: 'columns',
                    toolPanel: 'agColumnsToolPanel',
                  },
                  {
                    id: 'filters',
                    labelDefault: 'Filters',
                    labelKey: 'filters',
                    iconKey: 'filter',
                    toolPanel: 'agFiltersToolPanel',
                  }
                ],
              }}
              rowClassRules={{
                'forecast-row': (params) => {
                  return params?.data?.source === 'forecast';
                },
                'sales-row': (params) => {
                  return params?.data?.source === 'sales';
                }
              }}
              defaultColDef={{
                resizable: true,
                sortable: true,
                filter: true
              }}
            />
          )}
        </div>
      )
    }
  ];
  return (
    <div style={{ padding: '20px 0' }}>
      {/* Filters Section - All in One Row */}
      <Row gutter={[8, 16]} style={{ marginBottom: 20 }}>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>UPCs</label>
          <Select
            mode="multiple"
            style={{ width: '200px' }}
            placeholder="Select UPCs"
            value={selection.upcs}
            onChange={(values) => {
              setSelection(prev => ({ ...prev, upcs: values }));
            }}
            options={filterOptions.upcs.map(([value, label]) => ({ value, label }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No UPCs found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Forecast Nodes</label>
          <Select
            mode="multiple"
            style={{ width: '180px' }}
            placeholder="Select Nodes"
            value={selection.forecastNodes}
            onChange={(values) => setSelection(prev => ({ ...prev, forecastNodes: values }))}
            options={filterOptions.forecastNodes.map(node => ({ value: node, label: node }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No nodes found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Date Range</label>
          <Select
            value={dateRange}
            onChange={handleDateRangeChange}
            options={dateRangeOptions}
            style={{ width: '120px' }}
          />
        </Col>
        <Col>
          {dateRange === 'custom' && (
            <>
              <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Custom Dates</label>
              <RangePicker
                value={customDateRange}
                onChange={(dates) => setCustomDateRange(dates)}
                style={{ width: '200px' }}
              />
            </>
          )}
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Product</label>
          <Select
            mode="multiple"
            style={{ width: '140px' }}
            placeholder="Products"
            value={selection.products}
            onChange={(values) => setSelection(prev => ({ ...prev, products: values }))}
            options={filterOptions.products.map(product => ({ value: product, label: product }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No products found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Division</label>
          <Select
            mode="multiple"
            style={{ width: '120px' }}
            placeholder="Divisions"
            value={selection.divisions}
            onChange={(values) => setSelection(prev => ({ ...prev, divisions: values }))}
            options={filterOptions.divisions.map(division => ({ value: division, label: division }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No divisions found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Color</label>
          <Select
            mode="multiple"
            style={{ width: '100px' }}
            placeholder="Colors"
            value={selection.colors}
            onChange={(values) => setSelection(prev => ({ ...prev, colors: values }))}
            options={filterOptions.colors.map(color => ({ value: color, label: color }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No colors found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Size</label>
          <Select
            mode="multiple"
            style={{ width: '100px' }}
            placeholder="Sizes"
            value={selection.sizes}
            onChange={(values) => setSelection(prev => ({ ...prev, sizes: values }))}
            options={filterOptions.sizes.map(size => ({ value: size, label: size }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No sizes found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Category</label>
          <Select
            mode="multiple"
            style={{ width: '120px' }}
            placeholder="Categories"
            value={selection.categories}
            onChange={(values) => setSelection(prev => ({ ...prev, categories: values }))}
            options={filterOptions.categories.map(category => ({ value: category, label: category }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No categories found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Life Status</label>
          <Select
            mode="multiple"
            style={{ width: '120px' }}
            placeholder="Status"
            value={selection.lifeStatuses}
            onChange={(values) => setSelection(prev => ({ ...prev, lifeStatuses: values }))}
            options={filterOptions.lifeStatuses.map(status => ({ value: status, label: status }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No statuses found"}
          />
        </Col>
        <Col>
          <label style={{ fontWeight: 500, marginBottom: 4, display: 'block', fontSize: '12px' }}>Specification</label>
          <Select
            mode="multiple"
            style={{ width: '120px' }}
            placeholder="Specs"
            value={selection.specifications}
            onChange={(values) => setSelection(prev => ({ ...prev, specifications: values }))}
            options={filterOptions.specifications.map(spec => ({ value: spec, label: spec }))}
            showSearch
            filterOption={(input, option) =>
              option.label.toLowerCase().includes(input.toLowerCase())
            }
            loading={loadingFilters}
            notFoundContent={loadingFilters ? <Spin size="small" /> : "No specs found"}
          />
        </Col>
        <Col>
          <Button
            type="primary"
            onClick={handleSubmit}
            loading={loading}
            disabled={Object.values(selection).every(value => value?.length === 0)}
            style={{ marginTop: 24, width: '80px' }}
          >
            Load
          </Button>
        </Col>
        <Col>
          <Button
            onClick={() => {
              setSelection({
                upcs: initialSelection.upcs || [],
                forecastNodes: initialSelection.forecastNodes || [],
                dateRange: initialSelection.dateRange || null,
                products: [],
                divisions: [],
                colors: [],
                sizes: [],
                categories: [],
                lifeStatuses: [],
                specifications: []
              });
            }}
            style={{ marginTop: 24, width: '80px' }}
          >
            Clear
          </Button>
        </Col>
      </Row>

      {/* Data Display Tabs */}
      <Tabs defaultActiveKey="chart" size="large" items={tabItems} />
    </div>
  );
};

export default HistoricalSales;