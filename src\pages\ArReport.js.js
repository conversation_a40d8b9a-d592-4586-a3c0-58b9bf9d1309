/* eslint-disable new-cap */
/* eslint-disable react/prop-types */
/* eslint-disable require-jsdoc */
/* eslint-disable guard-for-in */

// todo move inventory report to this page
// import logo from './logo.svg';
import '../App.css';
import {useEffect, useState} from 'react';
// import {writeFile, utils} from 'xlsx';
// import * as ExcelJS from 'exceljs';
// import {saveAs} from 'file-saver';
import React from 'react';
// import Select from 'react-select';
import {api} from './firebase';
// import {useAuthState} from 'react-firebase-hooks/auth';
// import {auth} from './firebase';
// import {useNavigate} from 'react-router-dom';
import {
  Table,
  Space,
  Input,
  // Button, Input, Select, Modal
} from 'antd';
const ARReport = () => {
  const [data, setData] = useState([]);
  // eslint-disable-next-line max-len
  const q = `SELECT t.id id, e.altName customer, t.trandate date, t.tranid tranid, e.balanceSearch customerBalance, e.overdueBalanceSearch customerOverdue, t.foreignAmountUnpaid openBalance, trm.name termsName, t.daysOpen daysOpen, t.daysOverdueSearch daysOverdue, t.duedate duedate, s.name status FROM transaction t LEFT JOIN transactionstatus s ON s.id = t.status LEFT JOIN term trm ON trm.id = t.terms LEFT JOIN customer e ON e.id = t.entity WHERE t.type = 'CustInvc' AND (s.name IS NULL OR s.name != 'Paid In Full') AND s.trantype = 'CustInvc' ORDER BY t.trandate DESC`;
  ;
  useEffect(() => {
    const fetchData = async () => {
      const tempData = await api.makeSuiteQlQuery({query: q});
      console.log('tempData', tempData);
      setData(tempData.data ? tempData.data : []);
    };
    fetchData();
  }, []);
  const columns = [
    {
      title: 'Doc #',
      dataIndex: 'tranid',
    },
  ];
  return (
    <div>
      <Space>
        <Input placeholder="Search" />
      </Space>
      <Table dataSource={data} columns={columns} />
    </div>
  );
};


export default ARReport;
