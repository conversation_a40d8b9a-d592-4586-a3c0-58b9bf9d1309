import React, { useState } from 'react';
import { Modal } from 'antd';

const ImageCell = ({ imageUrl, altText = "Item", style = { height: '25px', width: 'auto' }, baseUrl = 'https://6810379.app.netsuite.com' }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalImageUrl, setModalImageUrl] = useState('');

  const handleImageClick = (e) => {
    e.stopPropagation(); // Prevent row selection when clicking on image
    const fullImageUrl = imageUrl.startsWith('http') ? imageUrl : `${baseUrl}${imageUrl}`;
    setModalImageUrl(fullImageUrl);
    setIsModalVisible(true);
  };

  const handleModalClose = () => {
    setIsModalVisible(false);
    setModalImageUrl('');
  };

  if (!imageUrl) {
    return (
      <div style={{ 
        height: style.height, 
        width: style.width, 
        backgroundColor: 'lightgray', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        fontSize: '10px',
        color: '#666'
      }}>
        <span>No Image</span>
      </div>
    );
  }

  const fullImageUrl = imageUrl.startsWith('http') ? imageUrl : `${baseUrl}${imageUrl}`;

  return (
    <>
      <img 
        src={fullImageUrl} 
        alt={altText} 
        style={{ 
          ...style, 
          cursor: 'pointer',
          transition: 'transform 0.2s ease-in-out'
        }} 
        onClick={handleImageClick}
        onMouseEnter={(e) => {
          e.target.style.transform = 'scale(1.05)';
        }}
        onMouseLeave={(e) => {
          e.target.style.transform = 'scale(1)';
        }}
      />
      <Modal
        open={isModalVisible}
        onCancel={handleModalClose}
        footer={null}
        width="90vw"
        style={{ top: 20 }}
        centered
      >
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          maxHeight: '80vh',
          overflow: 'auto'
        }}>
          <img 
            src={modalImageUrl} 
            alt={altText} 
            style={{ 
              maxWidth: '100%', 
              maxHeight: '80vh', 
              objectFit: 'contain'
            }} 
          />
        </div>
      </Modal>
    </>
  );
};

export default ImageCell; 