import React, { useState, useEffect } from 'react';
import { Menu } from 'antd';
import SupplyPlan from '../components/forecast/SupplyPlan';
import DemandPlan from '../components/forecast/DemandPlan';
import PurchasePlan from '../components/forecast/PurchasePlan';
import { useLocation, useSearchParams } from 'react-router-dom';
import ItemNodeMatrix from '../components/forecast/ItemNodeMatrix';
import NodeList from '../components/forecast/NodeList';
import ExternalDemandPlans from '../components/forecast/ExternalDemandPlans';
import SeasonalityMatrix from '../components/forecast/SeasonalityMatrix';
import ItemClassification from '../components/forecast/ItemClassification';

const ForecastBeta = ({ userObj }) => {
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();

  // Get tab from query param
  const tabKeyFromQuery = () => searchParams.get('tab') || 'demand-plan';
  const [activeTab, setActiveTab] = useState(tabKeyFromQuery());

  // Sync tab and subtab with query param
  useEffect(() => {
    const tab = searchParams.get('tab') || 'demand-plan';
    const subtab = searchParams.get('subtab') || (tab === 'demand-plan' ? 'current-demand-plan' : tab === 'nodes' ? 'node-list' : undefined);
    setSelectedMenuKey(tab);
    if (tab === 'demand-plan' || tab === 'nodes') {
      setSelectedDemandSubKey(subtab);
    }
    // eslint-disable-next-line
  }, [location.search]);

  const [selectedMenuKey, setSelectedMenuKey] = useState('demand-plan');
  const [selectedDemandSubKey, setSelectedDemandSubKey] = useState('current-demand-plan');

  const menuItems = [
    {
      type: 'group',
      label: 'Demand Planning',
      children: [
        { label: 'Current Demand Plan', key: 'current-demand-plan' },
        { label: 'External Demand Plans', key: 'external-demand-plans' },
      ],
    },
    {
      type: 'group',
      label: 'Supply & Purchase',
      children: [
        { label: 'Supply Plan', key: 'supply-plan' },
        { label: 'Purchase Plan', key: 'purchase-plan' },
      ],
    },
    {
      type: 'group',
      label: 'Settings',
      children: [
        { label: 'Node List', key: 'node-list' },
        { label: 'Item Node Matrix', key: 'item-node-matrix' },
        { label: 'Seasonality Matrix', key: 'seasonality-matrix' },
        { label: 'Item Classification', key: 'item-classification' },
      ],
    },
  ];

  const renderContent = () => {
    const key = selectedMenuKey;
    switch (key) {
      case 'current-demand-plan':
        return <DemandPlan />;
      case 'external-demand-plans':
        return <ExternalDemandPlans userObj={userObj} />;
      case 'supply-plan':
        return <SupplyPlan />;
      case 'purchase-plan':
        return <PurchasePlan />;
      case 'node-list':
        return <NodeList />;
      case 'item-node-matrix':
        return <ItemNodeMatrix />;
      case 'seasonality-matrix':
        return <SeasonalityMatrix />;
      case 'item-classification':
        return <ItemClassification />;
      default:
        return <DemandPlan />;
    }
  };

  const handleMenuClick = (e) => {
    setSelectedMenuKey(e.key);
    setSearchParams({ tab: e.key });
  };

  return (
    <div style={{ display: 'flex', minHeight: '100vh' }}>
      <Menu
        mode="vertical"
        selectedKeys={[selectedMenuKey]}
        onClick={handleMenuClick}
        items={menuItems}
        style={{ width: 200 }}
      />
      <div style={{ flex: 1, padding: 5 }}>
        {renderContent()}
      </div>
    </div>
  );
};

export default ForecastBeta;
