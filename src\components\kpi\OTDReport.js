import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Grid,
  Paper,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  ComposedChart, Line, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import { useFormatAxisTick } from '../../hooks/useFormatAxisTick';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import ChartExportWrapper from './ChartExportWrapper';

const OTDReport = () => {
  const [otdData, setOtdData] = useState(null);
  const [kpiGoals, setKpiGoals] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lateBenchmark, setLateBenchmark] = useState(30);
  const [tempLateBenchmark, setTempLateBenchmark] = useState(30);
  const [selectedSupplier, setSelectedSupplier] = useState('');
  const [allSuppliers, setAllSuppliers] = useState([]);

  const functions = getFunctions();
  const getOnTimeDeliveryV2 = httpsCallable(functions, 'getOnTimeDeliveryV2');
  const formatAxisTick = useFormatAxisTick({ currency: '', decimalPlaces: 0 });

  useEffect(() => {
    fetchData();
  }, [lateBenchmark]);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const [otdResult, goalsResult] = await Promise.all([
        getOnTimeDeliveryV2({ lateBenchmark }),
        httpsCallable(functions, 'getKPIGoalsForReport')({ reportName: 'otd' })
      ]);

      if (otdResult.data && otdResult.data.monthlyData) {
        const currentDate = new Date();
        const filteredData = {
          ...otdResult.data,
          monthlyData: otdResult.data.monthlyData
            .filter(month => new Date(month.month) <= currentDate)
            .sort((a, b) => new Date(a.month) - new Date(b.month))
        };
        setOtdData(filteredData);
        setAllSuppliers(['All Suppliers', ...new Set(filteredData.monthlyData.flatMap(month => month.suppliers.map(s => s.name)))]);
      }

      if (goalsResult.data.success) {
        setKpiGoals(goalsResult.data.data);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateOTDTrend = () => {
    if (!otdData?.monthlyData || otdData.monthlyData.length < 2) return 0;
    const relevantData = getFilteredChartData();
    if (relevantData.length < 2) return 0;
    const lastTwo = relevantData.slice(-2);
    return lastTwo[1].otdScore - lastTwo[0].otdScore;
  };

  // Keep existing functions
  const handleApplyLateBenchmark = () => {
    setLateBenchmark(tempLateBenchmark);
  };

  const handleSupplierChange = (event) => {
    setSelectedSupplier(event.target.value);
  };

  const formatNumber = (num) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const getFilteredChartData = () => {
    if (!otdData || !otdData.monthlyData) return [];

    return otdData.monthlyData.map(monthData => {
      if (selectedSupplier === '' || selectedSupplier === 'All Suppliers') {
        return {
          month: monthData.month,
          otdScore: parseFloat(monthData.otdScore),
          onTimeDeliveries: monthData.onTimeDeliveries,
          lateDeliveries: monthData.totalDeliveries - monthData.onTimeDeliveries,
          totalDeliveries: monthData.totalDeliveries,
        };
      } else {
        const supplierData = monthData.suppliers.find(s => s.name === selectedSupplier) || {
          otdScore: '0',
          onTimeDeliveries: 0,
          totalDeliveries: 0,
        };
        return {
          month: monthData.month,
          otdScore: parseFloat(supplierData.otdScore),
          onTimeDeliveries: supplierData.onTimeDeliveries,
          lateDeliveries: supplierData.totalDeliveries - supplierData.onTimeDeliveries,
          totalDeliveries: supplierData.totalDeliveries,
        };
      }
    });
  };

  const filteredChartData = getFilteredChartData();

  const getFilteredSupplierPerformance = () => {
    if (!otdData || !otdData.supplierPerformance) return [];

    return otdData.supplierPerformance
      .filter(supplier => selectedSupplier === '' || selectedSupplier === 'All Suppliers' || supplier.name === selectedSupplier)
      .map(supplier => ({
        ...supplier,
        otdScore: supplier.otdScore === 'NaN' ? 'N/A' : `${supplier.otdScore}%`,
        lateDeliveries: supplier.totalDeliveries - supplier.onTimeDeliveries
      }));
  };

  const filteredSupplierPerformance = getFilteredSupplierPerformance();

  const calculateOverallMetrics = () => {
    if (!otdData) return { overallOTDScore: 0, totalDeliveries: 0, totalOnTimeDeliveries: 0, totalLateDeliveries: 0 };

    if (selectedSupplier && selectedSupplier !== 'All Suppliers') {
      const supplierData = otdData.supplierPerformance.find(s => s.name === selectedSupplier);
      if (supplierData) {
        return {
          overallOTDScore: supplierData.otdScore === 'NaN' ? 0 : parseFloat(supplierData.otdScore),
          totalDeliveries: supplierData.totalDeliveries,
          totalOnTimeDeliveries: supplierData.onTimeDeliveries,
          totalLateDeliveries: supplierData.totalDeliveries - supplierData.onTimeDeliveries
        };
      }
    }

    return {
      overallOTDScore: otdData.overallOTDScore,
      totalDeliveries: otdData.totalDeliveries,
      totalOnTimeDeliveries: otdData.totalOnTimeDeliveries,
      totalLateDeliveries: otdData.totalDeliveries - otdData.totalOnTimeDeliveries
    };
  };

  const { overallOTDScore, totalDeliveries, totalOnTimeDeliveries, totalLateDeliveries } = calculateOverallMetrics();

  const calculateDomains = (data) => {
    if (!data?.length) return { scoreDomain: [0, 100], deliveriesDomain: [0, 100] };

    const scoreValues = data.map(item => item.otdScore);
    const deliveryValues = data.map(item => item.totalDeliveries);

    // Include goal values in score domain calculation
    const goalMin = kpiGoals?.['On-Time Delivery (OTD) Score']?.min;
    const goalMax = kpiGoals?.['On-Time Delivery (OTD) Score']?.max;

    const allScoreValues = [
      ...scoreValues,
      goalMin && parseFloat(goalMin),
      goalMax && parseFloat(goalMax)
    ].filter(Boolean);

    return {
      scoreDomain: [0, 100], // Keep fixed range for percentages
      deliveriesDomain: [0, Math.ceil(Math.max(...deliveryValues) * 1.1)]
    };
  };

  // Custom tooltip formatter
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.95)', maxWidth: 250 }}>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>{`Month: ${label}`}</Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Typography variant="body2" color="primary">
              OTD Score: {`${data.otdScore.toFixed(2)}%`}
            </Typography>
            <Typography variant="body2" color="success.main">
              On-Time Deliveries: {data.onTimeDeliveries}
            </Typography>
            <Typography variant="body2" color="error.main">
              Late Deliveries: {data.lateDeliveries}
            </Typography>
            <Typography variant="body2" color="primary">
              Total Deliveries: {data.totalDeliveries}
            </Typography>
          </Box>
        </Paper>
      );
    }
    return null;
  };

   // Use the KPI reference lines hook
   const goalReferenceLines = useKPIReferenceLines({
    goalConfig: kpiGoals?.['On-Time Delivery (OTD) Score'],
    yAxisId: "left",
    styles: {
      stroke: "#ff9800",
      strokeDasharray: "3 3",
      label: {
        fill: "#ff9800",
        fontSize: 12,
        position: "right"
      }
    },
    labelPrefix: {
      single: "Target",
      min: "Min Target",
      max: "Max Target"
    }
  });

  if (isLoading) return <CircularProgress />;
  if (!otdData || !kpiGoals) return null;

  const otdConfig = kpiGoals['On-Time Delivery (OTD) Score'];

  const { scoreDomain, deliveriesDomain } = calculateDomains(filteredChartData);
  return (
    <ChartExportWrapper title={`OTD_Report${selectedSupplier ? `_${selectedSupplier}` : ''}`}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <FormControl sx={{ minWidth: 200, marginTop: 3 }}>
          <InputLabel id="supplier-select-label">Filter by Supplier</InputLabel>
          <Select
            labelId="supplier-select-label"
            id="supplier-select"
            value={selectedSupplier}
            onChange={handleSupplierChange}
            label="Filter by Supplier"
          >
            {allSuppliers.map((supplier) => (
              <MenuItem key={supplier} value={supplier}>
                {supplier}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      <ResponsiveContainer width="100%" height={400}>
        <ComposedChart data={filteredChartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis
            yAxisId="left"
            orientation="left"
            stroke="#82ca9d"
            domain={scoreDomain}
            label={{ value: 'OTD Score (%)', angle: -90, position: 'insideLeft' }}
          />
          <YAxis
            yAxisId="right"
            orientation="right"
            stroke="#8884d8"
            domain={deliveriesDomain}
            label={{ value: 'Number of Deliveries', angle: 90, position: 'insideRight' }}
            tickFormatter={formatAxisTick}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          {goalReferenceLines}
          <Bar yAxisId="right" dataKey="onTimeDeliveries" name="On-Time Deliveries" fill="#8884d8" stackId="a" />
          <Bar yAxisId="right" dataKey="lateDeliveries" name="Late Deliveries" fill="#ff8042" stackId="a" />
          <Line 
            yAxisId="left" 
            type="monotone" 
            dataKey="otdScore" 
            name="OTD Score (%)" 
            stroke="#82ca9d" 
            strokeWidth={2}
            dot={{ r: 4, fill: "#82ca9d" }}
            activeDot={{ r: 8 }}
          />
        </ComposedChart>
      </ResponsiveContainer>

      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>Performance Summary</Typography>
        <Grid container spacing={2}>
          <Grid item xs={6} sm={6} md={3}>
            <KPICard
              title="Overall OTD Score"
              value={`${overallOTDScore}%`}
              bgColor="#f0f4ff"
              textColor="primary"
            />
          </Grid>
          <Grid item xs={6} sm={6} md={3}>
            <KPICard
              title="Total Deliveries"
              value={formatNumber(totalDeliveries)}
              bgColor="#f0fff0"
              textColor="primary"
            />
          </Grid>
          <Grid item xs={6} sm={6} md={3}>
            <KPICard
              title="On-Time Deliveries"
              value={formatNumber(totalOnTimeDeliveries)}
              bgColor="#e8f5e9"
              textColor="success.main"
            />
          </Grid>
          <Grid item xs={6} sm={6} md={3}>
            <KPICard
              title="Late Deliveries"
              value={formatNumber(totalLateDeliveries)}
              bgColor="#fff5f5"
              textColor="error.main"
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 4 }}>
          <Grid container spacing={2} alignItems="flex-start">
            <Grid item xs={12} sm={6} md={4}>
              <TextField
                label="Late Benchmark (Days)"
                type="number"
                value={tempLateBenchmark}
                onChange={(e) => setTempLateBenchmark(parseInt(e.target.value))}
                size="small"
                fullWidth
              />
              <Button variant="contained" onClick={handleApplyLateBenchmark} sx={{ mt: 1, width: '100%' }}>
                Apply Benchmark
              </Button>
            </Grid>
            <Grid item xs={12} md={8}>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Deliveries up to {lateBenchmark} days late are considered on-time.
              </Typography>
            </Grid>
          </Grid>
        </Box>
      </Box>

      <Box sx={{ mt: 4 }}>
        <GoalStatusDisplay
          currentValue={overallOTDScore}
          goalConfig={otdConfig}
          showScore={true}
          showTrend={true}
          trendValue={calculateOTDTrend()}
          size="medium"
          title={selectedSupplier && selectedSupplier !== 'All Suppliers'
            ? `${selectedSupplier} OTD Performance`
            : "Overall OTD Performance"
          }
        />
      </Box>

      <Box sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom>Supplier Performance</Typography>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Supplier</TableCell>
                <TableCell align="right">OTD Score</TableCell>
                <TableCell align="right">On-Time Deliveries</TableCell>
                <TableCell align="right">Late Deliveries</TableCell>
                <TableCell align="right">Total Deliveries</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredSupplierPerformance.map((supplier) => (
                <TableRow key={supplier.name}>
                  <TableCell component="th" scope="row">
                    {supplier.name}
                  </TableCell>
                  <TableCell align="right">{supplier.otdScore}</TableCell>
                  <TableCell align="right">{formatNumber(supplier.onTimeDeliveries)}</TableCell>
                  <TableCell align="right">{formatNumber(supplier.lateDeliveries)}</TableCell>
                  <TableCell align="right">{formatNumber(supplier.totalDeliveries)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>
   </ChartExportWrapper>
  );
};

export default OTDReport;
