import React, {useEffect, useState} from 'react';
import {api} from '../pages/firebase';
import {Modal, message, Typography, Space, Select} from 'antd';
import dayjs from 'dayjs';
import {Line} from 'react-chartjs-2';
import {Chart, registerables} from 'chart.js';
// import 'chartjs-adapter-date-fns';
import Spreadsheet from 'react-spreadsheet';


Chart.register(...registerables);
const {Text} = Typography;

const OpsForecastDetailModal = ({visible, setVisible, selectedVariant, selectedMonth}) => {
  const historicalDataMethodOptions = [
    {
      value: 'lastMonth',
      label: 'Last Month',
      startDate: dayjs(selectedMonth).subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
      endDate: dayjs(selectedMonth).subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
    },
    {
      value: 'lastYearSameMonth',
      label: 'Last Year Same Month',
      startDate: dayjs(selectedMonth).subtract(1, 'year').startOf('month').format('YYYY-MM-DD'),
      endDate: dayjs(selectedMonth).subtract(1, 'year').endOf('month').format('YYYY-MM-DD'),
    },
    {
      value: 'last90Days',
      label: 'Last 90 Days',
      startDate: dayjs(selectedMonth).subtract(90, 'days').format('YYYY-MM-DD'),
      endDate: dayjs(selectedMonth).format('YYYY-MM-DD'),
    },
  ];
  const channelMap = {
    'Wholesale': 'Wholesale',
    'Shopify US': 'Shopify',
    'HydroJug US': 'Shopify',
    'Wholesale Inbound': 'Wholesale',
    'Wholesale Outbound': 'Wholesale',
    'TikTok Shops': 'TikTok',
    'Amazon': 'Amazon',
    '- None -': 'Wholesale',
    'Shopify CA': 'Shopify',
    'Shopify UK': 'Shopify',
    'Shopify AU': 'Shopify',
  };
  const salesChannels = [{
    value: 'Wholesale',
    color: '#0000FF', // blue
  }, {
    value: 'Shopify',
    color: 'rgba(0, 100, 0, 1)', // dark green
  }, {
    value: 'Amazon',
    color: 'rgba(165, 42, 42, 1)', // brown
  }, {
    value: 'TikTok',
    color: 'rgba(0, 0, 0, 1)', // black
  }];

  const fetchHistoricalData = async () => {
    // eslint-disable-next-line max-len
    const salesQuery = `SELECT soDate as date, quantity as quantity, salesChannel as salesChannel FROM \`hj-reporting.sales.dailySalesByChannel\` WHERE soDate >= '${historicalDataMethod.startDate}' AND soDate <= '${historicalDataMethod.endDate}' AND upc = '${selectedVariant.upc}'`;
    console.log('salesQuery2', salesQuery);
    const salesData = await api.runQueryOnCall({options: {query: salesQuery}});
    console.log('salesData', salesData);
    if (!salesData || !salesData.data) return [];
    const salesDataArray = [];

    const salesChannels = [...new Set(salesData.data.map((x) => channelMap[x.salesChannel]))];
    for (const channel of salesChannels) {
      const channelData = salesData.data.filter((x) => channelMap[x.salesChannel] === channel);
      const channelArray = [];
      for (let i = 0; i < historicalDays; i++) {
        const date = dayjs(historicalDataMethod.startDate).add(i, 'day').format('YYYY-MM-DD');
        console.log('date', date, channel);
        const salesDataItem = channelData.filter((x) => x.date.value === date);
        if (salesDataItem.length > 0) {
          channelArray.push({date, quantity: salesDataItem.reduce((a, b)=>a+=b.quantity, 0), salesChannel: channel});
        } else {
          channelArray.push({date, quantity: 0, salesChannel: channel});
        }
      }
      channelArray.sort((a, b) => a.date.localeCompare(b.date));
      salesDataArray.push(channelArray);
    }
    return salesDataArray;
  };
  const [historicalData, setHistoricalData] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [historicalDataMethod, setHistoricalDataMethod] = useState(historicalDataMethodOptions[0]);
  const [loading, setLoading] = useState(true);

  const historicalDays = dayjs(historicalDataMethod.endDate).diff(dayjs(historicalDataMethod.startDate), 'day');

  const labels = [...Array(historicalDays).keys()].map((i) => dayjs(historicalDataMethod.startDate).add(i, 'day').format('YYYY-MM-DD'));
  const sampleData = {
    labels: labels,
    datasets: [{
      label: 'My First Dataset',
      data: [65, 59, 80, 81, 56, 55, 40],
      fill: true,
      stack: true,
      borderColor: 'rgb(255, 99, 132)',
      tension: 0.1,
    },
    {
      label: 'My Second Dataset',
      data: [10, 20, 30, 40, 50, 60, 70],
      fill: true,
      stack: true,
      borderColor: 'rgb(255, 99, 132)',
      tension: 0.1,
    }],
  };
  const config = {
    type: 'area',
    data: historicalData,
  };
  const getRandomColor = () => {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  };
  useEffect(() => {
    if (selectedVariant && historicalDataMethod) {
      setLoading(true);
      fetchHistoricalData().then((data) => {
        if (!data || !data.length || !data instanceof Array) {
          setHistoricalData({
            labels,
            datasets: data.map((channelData) => {
              return {
                label: channelData[0].salesChannel,
                data: channelData.map((item) => item.quantity),
                fill: true,
                stack: true,
                borderColor: salesChannels.find((x) => x.value === channelData[0].salesChannel)?.color || getRandomColor(),
                tension: 0.1,
              };
            }),
          });
          setLoading(false);
          return;
        };
        console.log('data', data);
        setHistoricalData({
          labels,
          datasets: data.map((channelData) => {
            return {
              label: channelData[0].salesChannel,
              data: channelData.map((item) => item.quantity),
              fill: true,
              stack: true,
              borderColor: salesChannels.find((x) => x.value === channelData[0].salesChannel)?.color || getRandomColor(),
              tension: 0.1,
            };
          }),
        });
        setTableData(data);
        setLoading(false);
      });
    }
  }, [historicalDataMethod, selectedVariant]);
  return (<Modal
    open={visible}
    onCancel={() => setVisible(false)}
    onOk={() => {
      message.info('Worksheet saved for ' + selectedVariant.sku);
      setVisible(false);
    }}
    width={'80%'}
  >
    <Space direction='vertical'>
      <Text>Detail Modal</Text>
      <Text> {selectedVariant.product} {selectedVariant.sku}</Text>
      <Space direction='horizontal' size={'small'}>
        <Select
          style={{width: 150}}
          value={historicalDataMethod}
          onChange={(value) => {
            setHistoricalDataMethod(historicalDataMethodOptions.find((item) => item.value === value));
          }}
          options={historicalDataMethodOptions.map((item) => ({
            label: item.label,
            value: item.value,
          }))}
        />
        <Text>Dates: {historicalDataMethod.startDate}-{historicalDataMethod.endDate}</Text>
      </Space>
      {loading && <Text>Loading...</Text>}
      {!loading && historicalData.datasets.length === 0 && <h4>No Data</h4>}
      {!loading && (
        <Line
          type='line'
          data={historicalData}
          options={config}
          height={600}
          width={1200}
        />
      )}
    </Space>
    {/* <Text>{JSON.stringify(historicalDataMethod)}</Text> */}
  </Modal>);


  // map data to chart by sales channel. Create an array by date for each sales channel in the historical data

  const columnLabels = selectedMonth ? [
    ...Array(
        dayjs(selectedMonth).daysInMonth()).keys()].map((i) => dayjs(selectedMonth).startOf('month').add(i, 'day').format('YYYY-MM-DD')) : [];
  console.log('columnLabels', columnLabels);

  if (!historicalData || !historicalData.length || !tableData || !tableData.length) {
    return <Text>Loading...</Text>;
  }


  return (
    <Modal
      open={rec}
      onCancel={() => setVisible(false)}
      onOk={() => {
        message.info('Worksheet saved for ' + rec.sku);
        setVisible(false);
      }}
      width={'80%'}
    >
      <>
        <Space align='horizontal' size={'small'}>
          <Text>Detail Modal - {rec.product} {rec.sku}</Text>
          <Select
            style={{width: 150}}
            value={historicalDataMethod}
            onChange={(value) => {
              setHistoricalDataMethod(value);
            }}
            options={historicalDataMethodOptions.map((item) => ({
              label: item.label,
              value: item.value,
            }))}
          />
        </Space>
        {historicalData.length === 0 && <Text>No data</Text>}
        {historicalData.length > 0 && (
          <>
            <Line data={sampleData} options={options} />
          </>
        )}
        {tableData.length === 0 && <Text>No data</Text>}
        {tableData.length > 0 && <Text>Forecast Qty</Text>}
        {tableData.length > 0 && (
          <Spreadsheet
            data={tableData}
            columnLabels={columnLabels}
            rowLabels={salesChannels}
          />
        )}
      </>
    </Modal>
  );
};

export default OpsForecastDetailModal;
