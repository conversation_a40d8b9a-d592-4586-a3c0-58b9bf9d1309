import React, { useState, useEffect } from 'react';
import { Box, Grid, Paper, CircularProgress, Alert, Typography } from '@mui/material';
import {
    Composed<PERSON>hart, Line, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, ReferenceLine
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import { useFormatAxisTick } from '../../hooks/useFormatAxisTick';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import ChartExportWrapper from './ChartExportWrapper';

const OutboundTransportationCost = () => {
    const [outboundData, setOutboundData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const formatAxisTick = useFormatAxisTick();

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const functions = getFunctions();
            const getOutboundTransportationCost = httpsCallable(functions, 'getOutboundTransportationCost');
            const getKPIGoalsForReport = httpsCallable(functions, 'getKPIGoalsForReport');

            const [outboundResult, goalsResult] = await Promise.all([
                getOutboundTransportationCost(),
                getKPIGoalsForReport({ reportName: 'outbound-transportation' })
            ]);

            setOutboundData(outboundResult.data);

            if (goalsResult.data.success) {
                // Ensure the goal configuration is correct
                const outboundConfig = {
                    ...goalsResult.data.data['Outbound Shipping Costs'],
                    value: '13',
                    goalCondition: 'lower',
                    unit: '%'
                };
                setKpiGoals({ 'Outbound Shipping Costs': outboundConfig });
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            setError('Failed to fetch data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    const calculateOutboundTrend = () => {
        if (!outboundData?.runningData || outboundData.runningData.length < 2) return 0;
        const lastTwo = outboundData.runningData.slice(-2);
        return lastTwo[1].outboundPercentage - lastTwo[0].outboundPercentage;
    };

    const calculateDomains = () => {
        if (!outboundData?.runningData) return { percentageDomain: [0, 100], amountDomain: [0, 1000] };

        const percentageValues = outboundData.runningData.map(item => item.outboundPercentage);
        const salesValues = outboundData.runningData.map(item => item.salesAmount);
        const shippingCostValues = outboundData.runningData.map(item => item.netShippingCost);

        const goalValue = kpiGoals?.['Outbound Shipping Costs']?.value;
        const goalMin = kpiGoals?.['Outbound Shipping Costs']?.min;
        const goalMax = kpiGoals?.['Outbound Shipping Costs']?.max;

        const allPercentageValues = [
            ...percentageValues,
            goalValue && parseFloat(goalValue),
            goalMin && parseFloat(goalMin),
            goalMax && parseFloat(goalMax)
        ].filter(Boolean);

        const maxPercentage = Math.max(...allPercentageValues);
        const minPercentage = Math.min(...allPercentageValues);

        const maxAmount = Math.max(...salesValues, ...shippingCostValues);
        const minAmount = Math.min(...salesValues, ...shippingCostValues);

        return {
            percentageDomain: [
                Math.max(0, Math.floor(minPercentage * 0.9)),
                Math.ceil(maxPercentage * 1.1)
            ],
            amountDomain: [
                minAmount < 0 ? Math.floor(minAmount * 1.1) : 0,
                Math.ceil(maxAmount * 1.1)
            ]
        };
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            const { outboundPercentage, salesAmount, netShippingCost, shippingIncome } = payload[0].payload;
            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                    <Typography variant="body2">{`Date: ${label}`}</Typography>
                    <Typography variant="body1" color="error" fontWeight="bold">
                        {`Net Outbound %: ${outboundPercentage.toFixed(2)}%`}
                    </Typography>
                    <Typography variant="body2" color="primary">
                        {`Sales: ${formatAxisTick(salesAmount)}`}
                    </Typography>
                    <Typography variant="body2" color="secondary">
                        {`Net Shipping Cost: ${formatAxisTick(netShippingCost)}`}
                    </Typography>
                    <Typography variant="body2" color="success.main">
                        {`Shipping Income: ${formatAxisTick(shippingIncome)}`}
                    </Typography>
                </Paper>
            );
        }
        return null;
    };

    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: kpiGoals?.['Outbound Shipping Costs'],
        yAxisId: "left",
        styles: {
            stroke: "#ff9800",
            strokeDasharray: "3 3",
            label: {
                fill: "#ff9800",
                fontSize: 12,
                position: "right"
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Min",
            max: "Max"
        }
    });

    if (isLoading) return <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}><CircularProgress /></Box>;
    if (error) return <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>;
    if (!outboundData || !kpiGoals) return null;

    const { percentageDomain, amountDomain } = calculateDomains();
    const outboundConfig = kpiGoals['Outbound Shipping Costs'];

    return (
        <ChartExportWrapper title="Outbound_Transportation_Cost">
            <Box sx={{ p: { xs: 2, sm: 3 }, mb: 3 }}>
                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <ResponsiveContainer width="100%" height={500}>
                        <ComposedChart data={outboundData.runningData}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                            <XAxis dataKey="date" />
                            <YAxis
                                yAxisId="left"
                                domain={percentageDomain}
                                label={{ value: 'Outbound %', angle: -90, position: 'insideLeft' }}
                                tickFormatter={(value) => `${value.toFixed(2)}%`}
                            />
                            <YAxis
                                yAxisId="right"
                                orientation="right"
                                domain={amountDomain}
                                label={{ value: 'Amount', angle: 90, position: 'insideRight' }}
                                tickFormatter={formatAxisTick}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            <ReferenceLine y={0} stroke="#000" yAxisId="right" />
                            {goalReferenceLines}
                            <Bar yAxisId="right" dataKey="netShippingCost" fill="#66bb6a" name="Net Shipping Cost" />
                            <Bar yAxisId="right" dataKey="salesAmount" fill="#4fc3f7" name="Sales Amount" />
                            <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="outboundPercentage"
                                stroke="#e53935"
                                strokeWidth={3}
                                dot={{ r: 4, fill: "#e53935" }}
                                activeDot={{ r: 8 }}
                                name="Outbound %"
                            />
                        </ComposedChart>
                    </ResponsiveContainer>

                    <Grid container spacing={2} sx={{ mt: 2 }}>
                        <Grid item xs={12} sm={6} md={3}>
                            <KPICard
                                title="Overall Outbound Cost %"
                                value={`${outboundData.overallOutboundPercentage.toFixed(2)}%`}
                                bgColor="#fff3e0"
                                textColor="error"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                            <KPICard
                                title="Total Sales"
                                value={`$${outboundData.totalSalesAmount.toLocaleString()}`}
                                bgColor="#e1f5fe"
                                textColor="primary"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                            <KPICard
                                title="Total Shipping Cost"
                                value={`$${outboundData.totalShippingCost.toLocaleString()}`}
                                bgColor="#e8f5e9"
                                textColor="success.main"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                            <KPICard
                                title="Total Shipping Income"
                                value={`$${outboundData.totalShippingIncome.toLocaleString()}`}
                                bgColor="#f3e5f5"
                                textColor="secondary"
                            />
                        </Grid>
                    </Grid>
                </Paper>

                <GoalStatusDisplay
                    currentValue={outboundData.overallOutboundPercentage}
                    goalConfig={outboundConfig}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculateOutboundTrend()}
                    size="medium"
                    title="Outbound Transportation Cost % Performance"
                />
            </Box>
        </ChartExportWrapper>
    );
};

export default OutboundTransportationCost;