/* eslint-disable guard-for-in */
const {onCall} = require("firebase-functions/v2/https");
const functions = require("firebase-functions");
const {makeNSSavedSearchRequest, executeNSSuiteQLQuery} = require("../helpers/netsuite");

// Helper function to process data
const processData=(data)=> {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;

  const processedData = data
      .filter((row) => {
        if (row["Month of Date"] === "Total") {
          // console.log(`Filtered out total row: ${JSON.stringify(row)}`);
          return false;
        }

        const [year, month] = row["Month of Date"].split("-").map(Number);

        if (isNaN(year) || isNaN(month) || year > currentYear || (year === currentYear && month > currentMonth)) {
          // console.log(`Filtered out future or invalid date: ${row["Month of Date"]}`);
          return false;
        }

        if (row["Location (no hierarchy)"] === "- None -") {
          // console.log(`Filtered out row with invalid Location: ${JSON.stringify(row)}`);
          return false;
        }

        return true;
      })
      .map((row) => ({
        month: row["Month of Date"],
        amount: parseFloat(row["Sum of Amount"]) || 0,
        location: row["Location (no hierarchy)"],
        upcCode: row["UPC Code"],
      }));

  return processedData;
};

const calculateCogsPercentage=(cogs, revenue)=>{
  return revenue !== 0 ? (cogs / revenue) * 100 : 0;
};

exports.getCogsPercentage = onCall(async (data, context) => {
  try {
    const [cogsData, revenueData] = await Promise.all([
      makeNSSavedSearchRequest("103501"),
      makeNSSavedSearchRequest("103584"),
    ]);

    const processedCogsData = processData(cogsData);
    const processedRevenueData = processData(revenueData);

    const lastCogsMonth = processedCogsData
        .map((item) => item.month)
        .sort((a, b) => new Date(b) - new Date(a))[0];

    const monthlyData = {};
    let totalCogs = 0;
    let totalRevenue = 0;

    processedCogsData.forEach((item) => {
      if (item.month <= lastCogsMonth) {
        if (!monthlyData[item.month]) {
          monthlyData[item.month] = {cogs: 0, revenue: 0};
        }
        monthlyData[item.month].cogs += item.amount;
        totalCogs += item.amount;
      }
    });

    processedRevenueData.forEach((item) => {
      if (item.month <= lastCogsMonth) {
        if (!monthlyData[item.month]) {
          monthlyData[item.month] = {cogs: 0, revenue: 0};
        }
        monthlyData[item.month].revenue += item.amount;
        totalRevenue += item.amount;
      }
    });

    const runningData = Object.entries(monthlyData)
        .map(([month, data]) => ({
          date: month,
          cogs: data.cogs,
          revenue: data.revenue,
          cogsPercentage: calculateCogsPercentage(data.cogs, data.revenue),
        }))
        .sort((a, b) => new Date(a.date) - new Date(b.date));

    const averageCogs = calculateCogsPercentage(totalCogs, totalRevenue);

    return {
      runningData,
      totalRevenue: Math.round(totalRevenue),
      totalCogs: Math.round(totalCogs),
      averageCogs: +averageCogs.toFixed(2),
    };
  } catch (error) {
    console.error("Error calculating COGS percentage:", error);
    throw new functions.https.HttpsError("internal", "Failed to calculate COGS percentage", error.message);
  }
});

/**
 * Fetches and processes COGS data from NetSuite.
 * @return {Promise<Array>} Processed COGS data.
 * @throws Will throw an error if fetching COGS data fails.
 */
async function getCOGSData() {
  try {
    const parsedData = await makeNSSavedSearchRequest("103501");

    return parsedData
        .filter((row) => row["Month of Date"] !== "Total")
        .map((row) => ({
          month: row["Month of Date"],
          location: row["Location"],
          productType: row["Product Type"],
          upcCode: row["UPC Code"],
          totalCOGS: parseFloat(row["Sum of Amount"]) || 0,
        }));
  } catch (error) {
    console.error("Error fetching COGS data:", error);
    throw error;
  }
}

/**
 * Fetches and processes average inventory data from NetSuite.
 * @return {Promise<Array>} Processed average inventory data.
 * @throws Will throw an error if fetching average inventory data fails.
 * @throws Will throw an error if the inventory data structure is invalid.
 */
async function getAverageInventoryData() {
  const inventoryQuery = `
    WITH inventory_data AS (
      SELECT 
        i.upccode AS upc_code,
        TO_CHAR(ih.custrecord_ih_date, 'YYYY-MM') AS month,
        ROUND(AVG(ih.custrecord_ih_qty_available), 2) AS avg_quantity_available,
        i.custitem_shopify_sales_price AS item_cost
      FROM 
        customrecord_inventory_history ih
      JOIN 
        item i ON ih.custrecord_ih_item = i.id
      WHERE 
        ih.custrecord_ih_qty_available > 0
      GROUP BY 
        i.upccode, TO_CHAR(ih.custrecord_ih_date, 'YYYY-MM'), i.custitem_shopify_sales_price
    )
    SELECT 
      id.upc_code,
      id.month,
      id.avg_quantity_available,
      id.item_cost,
      ROUND(id.avg_quantity_available * id.item_cost, 2) AS total_avg_inventory_cost,
      i.custitem20 AS life_status,
      i.custitem24 AS launch_date,
      i.custitem25 AS end_date
    FROM 
      inventory_data id
    JOIN 
      item i ON id.upc_code = i.upccode
    ORDER BY 
      id.upc_code, id.month
  `;

  return await executeNSSuiteQLQuery(inventoryQuery);
}

exports.getDIOData = onCall(async (data, context) => {
  try {
    const [cogsResult, inventoryResult] = await Promise.all([
      getCOGSData(),
      getAverageInventoryData(),
    ]);

    const today = new Date();
    const lastCompleteMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    const lastCompleteMonthString = lastCompleteMonth.toISOString().substring(0, 7);

    const earliestInventoryMonth = inventoryResult.items
        .map((item) => item.month)
        .sort()[0];

    const startDate = new Date(earliestInventoryMonth + "-01");
    const endDate = new Date(lastCompleteMonthString + "-01");
    endDate.setMonth(endDate.getMonth() + 1);
    endDate.setDate(endDate.getDate() - 1);
    const totalDays = Math.round((endDate - startDate) / (1000 * 60 * 60 * 24));

    const filteredCogsResult = cogsResult.filter((item) =>
      item.month >= earliestInventoryMonth && item.month <= lastCompleteMonthString,
    );
    const filteredInventoryResult = {
      ...inventoryResult,
      items: inventoryResult.items.filter((item) =>
        item.month >= earliestInventoryMonth && item.month <= lastCompleteMonthString,
      ),
    };

    if (!filteredCogsResult || filteredCogsResult.length === 0) {
      throw new functions.https.HttpsError("internal", "Filtered COGS data is empty or has no items");
    }

    if (!filteredInventoryResult || !filteredInventoryResult.items || !Array.isArray(filteredInventoryResult.items)) {
      throw new functions.https.HttpsError("internal", "Invalid filtered inventory data structure");
    }

    const dioByUPC = {};
    const monthlyTotals = {};
    let totalCOGS = 0;
    let totalAverageInventory = 0;

    filteredCogsResult.forEach((item) => {
      const upc = item.upcCode;
      const month = item.month;
      const cogs = item.totalCOGS;

      if (!dioByUPC[upc]) {
        dioByUPC[upc] = {totalCOGS: 0, totalAverageInventory: 0, monthlyData: {}};
      }
      if (!dioByUPC[upc].monthlyData[month]) {
        dioByUPC[upc].monthlyData[month] = {cogs: 0, averageInventory: 0};
      }
      dioByUPC[upc].monthlyData[month].cogs += cogs;
      dioByUPC[upc].totalCOGS += cogs;

      if (!monthlyTotals[month]) {
        monthlyTotals[month] = {date: month, cogs: 0, averageInventory: 0};
      }
      monthlyTotals[month].cogs += cogs;
      totalCOGS += cogs;
    });

    filteredInventoryResult.items.forEach((item) => {
      const upc = item.upc_code;
      const month = item.month;
      const totalAvgInventoryCost = parseFloat(item.total_avg_inventory_cost) || 0;

      if (!dioByUPC[upc]) {
        dioByUPC[upc] = {totalCOGS: 0, totalAverageInventory: 0, monthlyData: {}};
      }
      if (!dioByUPC[upc].monthlyData[month]) {
        dioByUPC[upc].monthlyData[month] = {cogs: 0, averageInventory: 0};
      }
      dioByUPC[upc].monthlyData[month].averageInventory = totalAvgInventoryCost;
      dioByUPC[upc].totalAverageInventory += totalAvgInventoryCost;

      if (!monthlyTotals[month]) {
        monthlyTotals[month] = {date: month, cogs: 0, averageInventory: 0};
      }
      monthlyTotals[month].averageInventory += totalAvgInventoryCost;
      totalAverageInventory += totalAvgInventoryCost;

      dioByUPC[upc].lifeStatus = item.life_status;
      dioByUPC[upc].launchDate = item.launch_date;
      dioByUPC[upc].endDate = item.end_date;
    });

    for (const upc in dioByUPC) {
      const upcData = dioByUPC[upc];
      upcData.dio = upcData.totalAverageInventory !== 0 ?
        (upcData.totalCOGS / upcData.totalAverageInventory) * totalDays :
        null;

      for (const month in upcData.monthlyData) {
        const monthData = upcData.monthlyData[month];
        monthData.dio = monthData.averageInventory !== 0 ?
          (monthData.cogs / monthData.averageInventory) * totalDays :
          null;
      }
    }

    const dioData = Object.entries(monthlyTotals).map(([month, data]) => ({
      date: month,
      cogs: data.cogs,
      averageInventory: data.averageInventory,
      dio: data.averageInventory !== 0 ? (data.cogs / data.averageInventory) * totalDays : null,
    })).sort((a, b) => a.date.localeCompare(b.date));

    const overallDIO = totalAverageInventory !== 0 ?
      (totalCOGS / totalAverageInventory) * totalDays :
      null;

    return {
      dioByUPC,
      dioData,
      overallDIO,
      totalCOGS,
      totalAverageInventory,
      lastCompleteMonth: lastCompleteMonthString,
      earliestDataMonth: earliestInventoryMonth,
      totalDays,
    };
  } catch (error) {
    console.error("Error calculating DIO:", error);
    throw new functions.https.HttpsError("internal", "Failed to calculate DIO", error.message);
  }
});

exports.getInboundTransportationCost = onCall(async (data, context) => {
  try {
    const rawData = await makeNSSavedSearchRequest("103587");

    const currentDate = new Date();
    const currentYearMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, "0")}`;

    const monthlyData = {};
    let totalInbound = 0;
    let totalRevenue = 0;

    rawData.forEach((row) => {
      if (row["Month of Date"] === "Total" || row["Month of Date"] > currentYearMonth) return;

      const month = row["Month of Date"];
      const inbound = parseFloat(row["Inbound"]) || 0;
      const revenue = parseFloat(row["Revenue"]) || 0;

      if (inbound < 0) {
        if (!monthlyData[month]) {
          monthlyData[month] = {inbound: 0, revenue: 0};
        }

        monthlyData[month].inbound += Math.abs(inbound);
        monthlyData[month].revenue += revenue;

        totalInbound += Math.abs(inbound);
        totalRevenue += revenue;
      }
    });

    const runningData = Object.entries(monthlyData)
        .map(([date, data]) => ({
          date,
          inbound: data.inbound,
          revenue: data.revenue,
          inboundPercentage: data.revenue > 0 ? (data.inbound / data.revenue) * 100 : 0,
        }))
        .sort((a, b) => a.date.localeCompare(b.date));

    const overallInboundPercentage = totalRevenue > 0 ? (totalInbound / totalRevenue) * 100 : 0;

    return {
      runningData,
      totalInbound,
      totalRevenue,
      overallInboundPercentage,
    };
  } catch (error) {
    console.error("Error calculating Inbound Transportation Cost:", error);
    throw new functions.https.HttpsError("internal", "Failed to calculate Inbound Transportation Cost", error.message);
  }
});

exports.getOutboundTransportationCost = onCall(async (data, context) => {
  try {
    const rawData = await makeNSSavedSearchRequest("103185");

    const currentDate = new Date();
    const currentYearMonth = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, "0")}`;

    const monthlyData = {};
    let totalShippingCost = 0;
    let totalSalesAmount = 0;
    let totalShippingIncome = 0;

    // Filter out Total rows and convert dates
    const validRows = rawData.filter(row => row["Date"] !== "Total").map(row => {
      const [month, day, year] = row["Date"].split("/");
      return {
        ...row,
        formattedDate: `${year}-${month.padStart(2, "0")}`
      };
    });

    validRows.forEach((row) => {
      if (row.formattedDate > currentYearMonth) return;

      const shippingCost = parseFloat(row["Sum of Shipping Cost"]) || 0;
      const salesAmount = parseFloat(row["Sum of Sales Amount"]) || 0;
      const shippingIncome = parseFloat(row["Sum of Shipping Income"]) || 0;

      if (!monthlyData[row.formattedDate]) {
        monthlyData[row.formattedDate] = {
          shippingCost: 0,
          salesAmount: 0,
          shippingIncome: 0
        };
      }

      monthlyData[row.formattedDate].shippingCost += shippingCost;
      monthlyData[row.formattedDate].salesAmount += salesAmount;
      monthlyData[row.formattedDate].shippingIncome += shippingIncome;

      totalShippingCost += shippingCost;
      totalSalesAmount += salesAmount;
      totalShippingIncome += shippingIncome;
    });

    const runningData = Object.entries(monthlyData)
      .map(([date, data]) => ({
        date,
        shippingCost: data.shippingCost,
        salesAmount: data.salesAmount,
        shippingIncome: data.shippingIncome,
        netShippingCost: data.shippingCost - data.shippingIncome,
        outboundPercentage: data.salesAmount !== 0 
          ? ((data.shippingCost - data.shippingIncome) / data.salesAmount) * 100 
          : 0
      }))
      .sort((a, b) => a.date.localeCompare(b.date));

    const netShippingCost = totalShippingCost - totalShippingIncome;
    const overallOutboundPercentage = totalSalesAmount !== 0
      ? (netShippingCost / totalSalesAmount) * 100
      : 0;

    return {
      runningData,
      totalShippingCost,
      totalSalesAmount,
      totalShippingIncome,
      netShippingCost,
      overallOutboundPercentage
    };
  } catch (error) {
    console.error("Error calculating Outbound Transportation Cost:", error);
    throw new functions.https.HttpsError("internal", "Failed to calculate Outbound Transportation Cost", error.message);
  }
});
