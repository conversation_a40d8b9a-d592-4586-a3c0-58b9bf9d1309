import React, { useState, useEffect } from 'react';
import { Input, Table } from 'antd';
import { api } from '../../firebase';

const TransferTab = () => {
  const [itemData, setItemData] = useState([]);
  const [binLoadedData, setBinLoadedData] = useState([]);
  const [itemLoadedData, setItemLoadedData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [locations] = useState([
    { id: '3', label: 'HQ', datakey: 'hq_qty', safetykey: 'hq_safety' },
    {
      id: '11',
      label: 'Wholesale',
      datakey: 'wholesale_qty',
      safetykey: 'wholesale_safety',
    },
    {
      id: '19',
      label: 'Small Wholesale',
      datakey: 'small_wholesale_qty',
      safetykey: 'small_wholesale_safety',
    },
    { id: '10', label: 'FBA', datakey: 'fba_qty', safetykey: 'fba_safety' },
  ]);
  const [productTypes, setProductTypes] = useState([]);
  const [lifeStatuses, setLifeStatuses] = useState([]);
  const [fromLocation, setFromLocation] = useState('');
  const [toLocation, setToLocation] = useState('');
  const [confirm, setConfirm] = useState(false);
  const [markAll, setMarkAll] = useState(false);
  console.log(markAll);
  const [filter, setFilter] = useState({
    search: '',
    can_transfer: '',
    need_transfer: '',
    product_type: '',
    life_status: '',
    out_of_stock_location: '',
  });
  const [binDestBins, setBinDestBins] = useState([]);
  const [sortConfig, setSortConfig] = useState({
    key: null,
    direction: 'asc',
  });
  const nsDomain = 'https://6810379.app.netsuite.com/';
  const formatAsLargeInt = (val) => {
    return parseInt(val) ? parseInt(val).toLocaleString() : val;
  };
  const onSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    const sortedData = [...itemData].sort((a, b) => {
      if (a[key] < b[key]) {
        return direction === 'asc' ? -1 : 1;
      }
      if (a[key] > b[key]) {
        return direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
    setItemData(sortedData);
    setSortConfig({ key, direction });
  };
  const handleCheckboxChange = (id) => {
    setItemData((prevItems) =>
      prevItems.map((item) =>
        item.id === id ? { ...item, checked: !item.checked } : item,
      ),
    );
  };
  const handleQuantityChange = (passitem, quantity, ele) => {
    setItemData((prevItems) =>
      prevItems.map((item) =>
        item.id === passitem.id ?
          {
            ...item,
            quantity: quantity,
            bin_transfer: setBinTransfer(passitem.id, parseInt(quantity)),
          } :
          item,
      ),
    );
    const fromLocationObject = locations.find(
      (location) => location.id === fromLocation,
    );
    ele.style.color = 'black';
    if (
      !isNaN(quantity) &&
      parseInt(passitem[fromLocationObject.datakey]) -
      parseInt(passitem[fromLocationObject.safetykey]) <
      parseInt(quantity)
    ) {
      setTimeout(() => {
        ele.style.color = 'red';
      }, 1);
      return;
    }
  };
  const setBinTransfer = (id, quantity) => {
    const binTransfer = [];
    const fromLocationObject = locations.find(
      (location) => location.id === fromLocation,
    );
    itemData.map((itemObj) => {
      if (itemObj.id == id) {
        if (
          quantity <= itemObj[fromLocationObject.datakey] &&
          itemObj.bins.length > 0
        ) {
          itemObj.bins.map((binObj) => {
            if (quantity > 0) {
              const toBin = binDestBins.find(
                (bin) => bin.binnumber === binObj.binnumber,
              );
              const oData = {
                from_bin_id: binObj.binnumber_id,
                to_bin_id: toBin ? toBin.binnumber_id : '',
                quantity:
                  binObj.quantity <= quantity ? binObj.quantity : quantity,
              };

              binTransfer.push(oData);
              quantity -= oData.quantity;
            }
          });
        } else {
          return [];
        }
      }
    });
    return binTransfer;
  };
  const handleSubmit = () => {
    if (!fromLocation || !toLocation) {
      alert('Select first the From Location and To Location.');
      return false;
    }
    let hasError = false;
    let itemSubmittedData = [];
    itemData.map((itemObj) => {
      itemObj.bin_transfer_error = '';
      if (itemObj.checked) {
        const fromLocationObject = locations.find(
          (location) => location.id === fromLocation,
        );
        if (
          parseInt(itemObj[fromLocationObject.datakey]) -
          parseInt(itemObj[fromLocationObject.safetykey]) <
          parseInt(itemObj.quantity)
        ) {
          hasError = true;
          itemObj.bin_transfer_error =
            'The quantity to transfer makes the quantity available less than the safety stock.';
        }
        let total = 0;
        const binFromList = [];
        itemObj.bin_transfer.map((row, index) => {
          if (binFromList.indexOf(row.from_bin_id) >= 0) {
            hasError = true;
            itemObj.bin_transfer_error = 'Duplicate source bin selected.';
          }
          if (row.from_bin_id == '' || row.to_bin_id == '') {
            hasError = true;
            itemObj.bin_transfer_error = 'Empty bin.';
          }
          total += parseInt(row.quantity);
          binFromList.push(row.from_bin_id);
        });
        if (hasError == false && total != itemObj.quantity) {
          hasError = true;
          itemObj.bin_transfer_error =
            'Total quantity does not match. Total Qty: ' + total;
        }
        itemSubmittedData.push(itemObj);
      }
    });
    itemSubmittedData = itemSubmittedData.sort((a, b) => {
      return new Date(b.name) - new Date(a.name);
    });

    if (itemSubmittedData.length == 0) {
      alert('Select line to submit.');
      return false;
    }
    if (hasError == false) {
      setConfirm(true);
      setItemData(itemSubmittedData);
    } else {
      setItemData(itemSubmittedData);
      alert('ERROR: Cannot be submitted. Check the error lines.');
    }
  };
  const handleConfirm = () => {
    setLoading(true);
    let apiURL =
      'https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=3661&deploy=1';
    apiURL +=
      '&action=createinventorytransfer&compid=6810379&ns-at=AAEJ7tMQMYrAVRguSFLy9cAiJ_3qWbbMYyvGMu1ULt198gs3NcQ';
    axios
      .post(apiURL, {
        param_from_location: fromLocation,
        param_to_location: toLocation,
        param_data: itemData,
      })
      .then((response) => {
        if (response.data.success) {
          alert('An inventory transfer has been created.');
          setLoading(false);
          setConfirm(false);
          window.open(
            'https://6810379.app.netsuite.com' +
            response.data.inventorytransfer,
          );
        } else {
          alert(response.data);
          setLoading(false);
        }
      })
      .catch((error) => alert(error));
  };
  const handleCancelConfirm = () => {
    setConfirm(false);
  };
  const handleFromLocationChange = (e) => {
    setFromLocation(e.target.value);
    if (e.target.value) {
      if (binLoadedData[e.target.value]) {
        let itemFilteredData = [];
        itemData.map((itemObj) => {
          itemObj.bins = binLoadedData[e.target.value][itemObj.id] ?
            binLoadedData[e.target.value][itemObj.id] :
            {};
          itemFilteredData.push(itemObj);
        });
        itemFilteredData = itemFilteredData.sort((a, b) => {
          return new Date(b.name) - new Date(a.name);
        });
        setItemData(itemFilteredData);
      }
    }
    handleFilterChange('can_transfer', 'yes', { from: e.target.value });
  };
  const handleToLocationChange = async (e) => {
    setToLocation(e.target.value);
    const binDataResponse = await axios.get(
      'https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=3661&deploy=1' +
      '&action=getdestinationbins&param_location=' +
      e.target.value +
      '&compid=6810379&ns-at=AAEJ7tMQMYrAVRguSFLy9cAiJ_3qWbbMYyvGMu1ULt198gs3NcQ',
    );
    const binData = binDataResponse.data;
    setBinDestBins(binData);
    handleFilterChange('need_transfer', 'yes', { to: e.target.value });
  };
  const handleMarkAll = (isChecked) => {
    let itemFilteredData = [];
    itemData.map((itemObj) => {
      itemObj.checked = isChecked;
      itemFilteredData.push(itemObj);
    });
    itemFilteredData = itemFilteredData.sort((a, b) => {
      return new Date(b.name) - new Date(a.name);
    });
    setItemData(itemFilteredData);
    setMarkAll(isChecked);
  };
  const handleFilterChange = (eleKey, eleValue, oPass = {}) => {
    const tempFromLocation = oPass.from ? oPass.from : fromLocation;
    const tempToLocation = oPass.to ? oPass.to : toLocation;
    if (eleKey == 'can_transfer') {
      if (eleValue == 'yes') {
        if (!tempFromLocation) {
          alert('Select first the From Location.');
          return false;
        }
      }
    }
    if (eleKey == 'need_transfer') {
      if (eleValue == 'yes') {
        if (!tempToLocation) {
          alert('Select first the To Location.');
          return false;
        }
      }
    }
    let tempFilter = {};
    tempFilter = filter;
    tempFilter[eleKey] = eleValue;
    let itemFilteredData = [];

    const fromLocationObject = locations.find(
      (location) => location.id === tempFromLocation,
    );
    const toLocationObject = locations.find(
      (location) => location.id === tempToLocation,
    );
    const noStockLocationObject = locations.find(
      (location) => location.id === tempFilter.out_of_stock_location,
    );

    itemLoadedData.map((itemObj) => {
      if (
        (tempFilter.search == '' &&
          tempFilter.can_transfer == '' &&
          tempFilter.need_transfer == '' &&
          tempFilter.product_type == '' &&
          tempFilter.life_status == '' &&
          tempFilter.out_of_stock_location == '') ||
        ((tempFilter.search == '' ||
          (tempFilter.search != '' &&
            ['name', 'description', 'product_type'].some((prop) =>
              itemObj[prop].toLowerCase().includes(tempFilter.search.toLowerCase()),
            ))) &&
          (tempFilter.can_transfer == '' ||
            (tempFilter.can_transfer == 'yes' &&
              !isNaN(itemObj[fromLocationObject.datakey]) &&
              parseFloat(itemObj[fromLocationObject.datakey]) -
              parseFloat(itemObj[fromLocationObject.safetykey]) >
              0)) &&
          (tempFilter.need_transfer == '' ||
            (tempFilter.need_transfer == 'yes' &&
              parseFloat(itemObj[toLocationObject.datakey]) < 0)) &&
          (tempFilter.product_type == '' ||
            tempFilter.product_type == itemObj.product_type) &&
          (tempFilter.life_status == '' ||
            tempFilter.life_status == itemObj.life_status) &&
          (tempFilter.out_of_stock_location == '' ||
            (tempFilter.out_of_stock_location &&
              (isNaN(itemObj[noStockLocationObject.datakey]) ||
                parseFloat(itemObj[noStockLocationObject.datakey]) <= 0))))
      ) {
        itemFilteredData.push(itemObj);
      }
    });
    itemFilteredData = itemFilteredData.sort((a, b) => {
      return new Date(b.name) - new Date(a.name);
    });
    setItemData(itemFilteredData);
    setFilter((prevFilters) => ({ ...prevFilters, [eleKey]: eleValue }));
  };
  const binHandleChange = (id, index, field, value) => {
    if (field == 'quantity') {
      if (parseInt(value) <= 0) {
        alert('ERROR: Invalid quantity.');
        return;
      }
      const selectitem = itemData.find((item) => item.id === id);
      const selecttransfer = selectitem.bin_transfer[index];
      const selectbin = selectitem.bins.find(
        (bin) =>
          bin.binnumber_id === selecttransfer.from_bin_id &&
          bin.status === 'Good',
      );
      if (selectbin.quantity < parseInt(value)) {
        alert(
          'ERROR: There are only ' +
          selectbin.quantity +
          ' available in the selected bin.',
        );
        return;
      }
    }

    let oData = {};
    oData[field] = value;

    if (field == 'from_bin_id') {
      const fromBinList = binLoadedData[fromLocation][id];
      const fromBin = fromBinList.find((bin) => bin.binnumber_id === value);
      const toBin = binDestBins.find(
        (bin) => bin.binnumber === fromBin.binnumber,
      );
      oData = {
        from_bin_id: fromBin.binnumber_id,
        to_bin_id: toBin ? toBin.binnumber_id : '',
        quantity: fromBin.quantity,
      };
    }

    setItemData((prevItems) =>
      prevItems.map((item) =>
        item.id === id ?
          {
            ...item,
            bin_transfer: item.bin_transfer.map((transferItem, idx) =>
              idx === index ? { ...transferItem, ...oData } : transferItem,
            ),
          } :
          item,
      ),
    );
  };
  const binHandleAddRow = (id) => {
    if (!fromLocation || !toLocation) {
      alert('Select first the From Location and To Location.');
      return;
    }
    const newRow = {
      from_bin_id: '',
      to_bin_id: '',
      quantity: 0,
    };
    setItemData((prevItems) =>
      prevItems.map((item) =>
        item.id === id ?
          { ...item, bin_transfer: [...item.bin_transfer, newRow] } :
          item,
      ),
    );
  };
  const binHandleDeleteRow = (id, index) => {
    setItemData((prevItems) =>
      prevItems.map((item) =>
        item.id === id ?
          {
            ...item,
            bin_transfer: item.bin_transfer.filter((_, i) => i !== index),
          } :
          item,
      ),
    );
  };
  const parseData = (data) => {
    const itemData = [];
    if (data) {
      const lines = data.split('\n');
      const headers = lines[0].split(',');

      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',');
        const itemObj = {};

        for (let j = 0; j < headers.length; j++) {
          itemObj[headers[j]] = values[j];
        }

        itemData.push(itemObj);
      }
    }
    return itemData;
  };
  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;

    const fetchItemData = async () => {
      const aItemList = [];
      const aProductTypeList = [];
      const aLifeStatusList = [];
      try {
        const binDataResponse = await axios.get(
          'https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=3661&deploy=1' +
          '&action=getinventorybins&compid=6810379&ns-at=AAEJ7tMQMYrAVRguSFLy9cAiJ_3qWbbMYyvGMu1ULt198gs3NcQ',
          {
            signal: signal,
          },
        );
        const binData = binDataResponse.data;
        setBinLoadedData(binData);
        const itemDataResponse = await axios.get(searchUrl + 103478, {
          signal: signal,
        });
        let itemData = parseData(itemDataResponse.data);
        itemData = itemData
          .map((itemObj) => {
            aItemList.push({
              id: itemObj['Internal ID'],
              label: itemObj['Name'],
            });
            aProductTypeList.push({
              id: itemObj['Product Type'],
              label: itemObj['Product Type'],
            });
            aLifeStatusList.push({
              id: itemObj['Life Status'],
              label: itemObj['Life Status'],
            });
            return {
              checked: false,
              id: itemObj['Internal ID'],
              name: itemObj['Name'],
              image: itemObj['Item Image'],
              description: itemObj['Description'],
              product_type: itemObj['Product Type'],
              life_status: itemObj['Life Status'],
              hq_qty: itemObj['HQ Qty'],
              wholesale_qty: itemObj['Wholesale Qty'],
              small_wholesale_qty: itemObj['Small Wholesale Qty'],
              fba_qty: itemObj['FBA Qty'],
              hq_safety: itemObj['HQ Safety Stock'],
              wholesale_safety: itemObj['Wholesale Safety Stock'],
              small_wholesale_safety: itemObj['Small Wholesale Safety Stock'],
              fba_safety: itemObj['FBA Safety Stock'],
              hq_total:
                (parseFloat(itemObj['HQ Qty']) || 0) -
                (parseFloat(itemObj['HQ Safety Stock']) || 0),
              wholesale_total:
                (parseFloat(itemObj['Wholesale Qty']) || 0) -
                (parseFloat(itemObj['Wholesale Safety Stock']) || 0),
              small_wholesale_total:
                (parseFloat(itemObj['Small Wholesale Qty']) || 0) -
                (parseFloat(itemObj['Small Wholesale Safety Stock']) || 0),
              fba_total:
                (parseFloat(itemObj['FBA Qty']) || 0) -
                (parseFloat(itemObj['FBA Safety Stock']) || 0),
              quantity: 0,
              bins: {},
              bin_transfer: [],
              bin_transfer_error: '',
            };
          })
          .sort((a, b) => {
            return new Date(b.name) - new Date(a.name);
          });
        setProductTypes(
          aProductTypeList.filter(
            (item, index, self) =>
              index === self.findIndex((obj) => obj.id === item.id),
          ),
        );
        setLifeStatuses(
          aLifeStatusList.filter(
            (item, index, self) =>
              index ===
              self.findIndex(
                (obj) => obj.id === item.id && obj.id != '- None -',
              ),
          ),
        );
        setItemLoadedData(itemData);
        setItemData(itemData);
        setLoading(false);
      } catch (err) {
        if (axios.isCancel(err)) {
          console.log('Request canceled', err.message);
        } else {
          console.log(err);
          setLoading(false);
        }
      }
    };
    fetchItemData();
    return () => {
      controller.abort();
    };
  }, []);

  const fromLocationObject = locations.find(
    (location) => location.id === fromLocation,
  );
  const toLocationObject = locations.find(
    (location) => location.id === toLocation,
  );

  return loading ? (
    <div className="spinner-container">
      <div className="spinner-border text-primary" role="status">
        <span className="sr-only">Loading...</span>
      </div>
    </div>
  ) : (
    <div>
      <div className="card shadow mt-3 mb-4" id="filter-area">
        <div className="card-body">
          <form className="user">
            <div className="form-group row">
              <div className="filter-div">
                <div className="col-auto">
                  <h4>Transfer</h4>
                </div>
              </div>
            </div>
            <div className="form-group row">
              <div className="filter-div">
                <div className="col-auto">
                  <label className="col-form-label">From:</label>
                </div>
                <div className="col-auto">
                  <select
                    value={fromLocation}
                    onChange={handleFromLocationChange}
                    disabled={confirm}
                    className="form-control form-select form-control-sm"
                  >
                    <option value="">--Select From Location--</option>
                    {locations.map((option, index) => (
                      <option key={index} value={option.id}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="filter-div">
                <div className="col-auto">
                  <label className="col-form-label">To:</label>
                </div>
                <div className="col-auto">
                  <select
                    value={toLocation}
                    onChange={handleToLocationChange}
                    disabled={confirm}
                    className="form-control form-select form-control-sm"
                  >
                    <option value="">--Select To Location--</option>
                    {locations.map((option, index) => (
                      <option key={index} value={option.id}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
            {fromLocation && toLocation ? (
              <div className="form-group row">
                <div className="filter-div">
                  <div className="col-auto">
                    <label className="col-form-label">Search:</label>
                  </div>
                  <div className="col-auto">
                    <input
                      value={filter.search}
                      onChange={(e) =>
                        handleFilterChange('search', e.target.value)
                      }
                      className="form-control form-select form-control-sm"
                      disabled={confirm}
                    />
                  </div>
                </div>
                <div className="filter-div">
                  <div className="col-auto">
                    <label className="col-form-label">Can Transfer?</label>
                  </div>
                  <div className="col-auto">
                    <select
                      value={filter.can_transfer}
                      onChange={(e) =>
                        handleFilterChange('can_transfer', e.target.value)
                      }
                      className="form-control form-select form-control-sm"
                      disabled={confirm}
                    >
                      <option value=""></option>
                      <option value="yes">Yes</option>
                    </select>
                  </div>
                </div>
                <div className="filter-div">
                  <div className="col-auto">
                    <label className="col-form-label">Need Transfer?</label>
                  </div>
                  <div className="col-auto">
                    <select
                      value={filter.need_transfer}
                      onChange={(e) =>
                        handleFilterChange('need_transfer', e.target.value)
                      }
                      className="form-control form-select form-control-sm"
                      disabled={confirm}
                    >
                      <option value=""></option>
                      <option value="yes">Yes</option>
                    </select>
                  </div>
                </div>
                <div className="filter-div">
                  <div className="col-auto">
                    <label className="col-form-label">Prdouct Type:</label>
                  </div>
                  <div className="col-auto">
                    <select
                      value={filter.product_type}
                      onChange={(e) =>
                        handleFilterChange('product_type', e.target.value)
                      }
                      className="form-control form-select form-control-sm"
                      disabled={confirm}
                    >
                      <option value="">--Select Product Type--</option>
                      {productTypes.map((option, index) => (
                        <option key={index} value={option.id}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <div className="filter-div">
                  <div className="col-auto">
                    <label className="col-form-label">Life Status:</label>
                  </div>
                  <div className="col-auto">
                    <select
                      value={filter.life_status}
                      onChange={(e) =>
                        handleFilterChange('life_status', e.target.value)
                      }
                      className="form-control form-select form-control-sm"
                      disabled={confirm}
                    >
                      <option value=""></option>
                      {lifeStatuses.map((option, index) => (
                        <option key={index} value={option.id}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <div className="filter-div">
                  <div className="col-auto">
                    <label className="col-form-label">
                      Out of Stock Location:
                    </label>
                  </div>
                  <div className="col-auto">
                    <select
                      value={filter.out_of_stock_location}
                      onChange={(e) =>
                        handleFilterChange(
                          'out_of_stock_location',
                          e.target.value,
                        )
                      }
                      className="form-control form-select form-control-sm"
                      disabled={confirm}
                    >
                      <option value="">--Select Location--</option>
                      {locations.map((option, index) => (
                        <option key={index} value={option.id}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            ) : (
              ''
            )}
          </form>
        </div>
      </div>
      {fromLocation && toLocation ? (
        <div className="card shadow mt-3 mb-4">
          <div className="card-head">
            <div
              className="container-fluid"
              style={{ padding: '20px 20px 0' }}
            >
              <div className="d-flex justify-content-between">
                {confirm ? (
                  <div className="pull-left">
                    <h5>Confirm the following:</h5>
                    <button
                      type="submit"
                      className="btn btn-dark btn-sm"
                      onClick={handleConfirm}
                    >
                      <span className="text">Confirm</span>
                    </button>
                    <button
                      type="submit"
                      className="btn btn-secondary btn-sm"
                      onClick={handleCancelConfirm}
                    >
                      <span className="text">Cancel</span>
                    </button>
                  </div>
                ) : (
                  <div className="pull-left">
                    <button
                      type="submit"
                      className="btn btn-dark btn-sm"
                      onClick={() => handleMarkAll(true)}
                    >
                      <span className="text">Mark All</span>
                    </button>
                    <button
                      type="submit"
                      className="btn btn-dark btn-sm"
                      onClick={() => handleMarkAll(false)}
                    >
                      <span className="text">Unmark All</span>
                    </button>
                    <button
                      type="submit"
                      className="btn btn-dark btn-sm"
                      onClick={handleSubmit}
                    >
                      <span className="text">Submit</span>
                    </button>
                  </div>
                )}
                <div className="pull-right"></div>
              </div>
            </div>
          </div>
          <div className="card-body">
            <div className="table-responsive">
              <table className="table">
                <thead>
                  <tr>
                    {!confirm && <th scope="col"></th>}
                    <th scope="col">Image</th>
                    <th scope="col" onClick={() => onSort('name')}>
                      Name
                    </th>
                    <th
                      scope="col"
                      style={{ display: confirm ? 'none' : 'table-cell' }}
                      onClick={() => onSort('life_status')}
                    >
                      Life Status
                    </th>
                    <th
                      scope="col"
                      style={{
                        display: confirm ? 'none' : 'table-cell',
                        width: '150px',
                      }}
                      onClick={() => onSort(fromLocationObject.datakey)}
                    >
                      {fromLocationObject.label} Qty
                    </th>
                    <th
                      scope="col"
                      style={{
                        display: confirm ? 'none' : 'table-cell',
                        width: '150px',
                      }}
                      onClick={() => onSort(toLocation.datakey)}
                    >
                      {toLocationObject.label} Qty
                    </th>
                    <th scope="col">Transfer Qty</th>
                    <th scope="col">Bin Transfer</th>
                  </tr>
                </thead>
                <tbody>
                  {itemData.map((item, index) => {
                    return (
                      <tr key={item.id}>
                        {!confirm && (
                          <td>
                            <input
                              type="checkbox"
                              disabled={confirm}
                              checked={item.checked}
                              onChange={() => handleCheckboxChange(item.id)}
                            />
                          </td>
                        )}
                        <td>
                          {item.image ? (
                            <img
                              src={nsDomain + item.image}
                              style={{ width: '75px', height: '75px' }}
                            />
                          ) : (
                            ''
                          )}
                        </td>
                        <td>{item.name}</td>
                        <td
                          style={{ display: confirm ? 'none' : 'table-cell' }}
                        >
                          {item.life_status}
                        </td>
                        <td
                          style={{
                            display: confirm ? 'none' : 'table-cell',
                            width: '150px',
                          }}
                          className="cell"
                        >
                          <span className="hidden">
                            {formatAsLargeInt(
                              item[fromLocationObject.datakey] -
                              item[fromLocationObject.safetykey],
                            )}
                          </span>
                          <table
                            style={{ fontSize: '12px', width: '120px' }}
                            className="visible table-light"
                          >
                            <tr className="table-light">
                              <td>Available:</td>
                              <td style={{ textAlign: 'right' }}>
                                {formatAsLargeInt(
                                  item[fromLocationObject.datakey],
                                )}
                              </td>
                            </tr>
                            <tr className="table-light">
                              <td>Safety Stock:</td>
                              <td style={{ textAlign: 'right' }}>
                                {formatAsLargeInt(
                                  item[fromLocationObject.safetykey],
                                )}
                              </td>
                            </tr>
                            <tr className="table-light">
                              <td>
                                <strong>Total:</strong>
                              </td>
                              <td style={{ textAlign: 'right' }}>
                                {formatAsLargeInt(
                                  item[fromLocationObject.datakey] -
                                  item[fromLocationObject.safetykey],
                                )}
                              </td>
                            </tr>
                          </table>
                        </td>
                        <td
                          style={{
                            display: confirm ? 'none' : 'table-cell',
                            width: '150px',
                          }}
                          className="cell"
                        >
                          <span className="hidden">
                            {formatAsLargeInt(item[toLocationObject.datakey])}
                          </span>
                          <table
                            style={{ fontSize: '12px', width: '120px' }}
                            className="visible table-light"
                          >
                            <tr className="table-light">
                              <td>Available:</td>
                              <td style={{ textAlign: 'right' }}>
                                {formatAsLargeInt(
                                  item[toLocationObject.datakey],
                                )}
                              </td>
                            </tr>
                            <tr className="table-light">
                              <td>Safety Stock:</td>
                              <td style={{ textAlign: 'right' }}>
                                {formatAsLargeInt(
                                  item[toLocationObject.safetykey],
                                )}
                              </td>
                            </tr>
                            <tr className="table-light">
                              <td>
                                <strong>Total:</strong>
                              </td>
                              <td style={{ textAlign: 'right' }}>
                                {formatAsLargeInt(
                                  item[toLocationObject.datakey] -
                                  item[toLocationObject.safetykey],
                                )}
                              </td>
                            </tr>
                          </table>
                        </td>
                        <td>
                          <input
                            type="number"
                            disabled={confirm}
                            value={item.quantity}
                            onChange={(e) =>
                              handleQuantityChange(
                                item,
                                e.target.value,
                                e.target,
                              )
                            }
                            className="form-control form-select form-control-sm"
                          />
                        </td>
                        <td>
                          {item.bin_transfer.length > 0 ? (
                            <div>
                              <table className="table-light">
                                <thead>
                                  <tr className="table-light">
                                    <th>From Bin</th>
                                    <th>To Bin</th>
                                    <th>Quantity</th>
                                    <th
                                      style={{
                                        display: confirm ?
                                          'none' :
                                          'table-cell',
                                      }}
                                    ></th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {item.bin_transfer.map((row, index) => (
                                    <tr key={index} className="table-light">
                                      <td>
                                        <select
                                          value={row.from_bin_id}
                                          disabled={confirm}
                                          onChange={(e) =>
                                            binHandleChange(
                                              item.id,
                                              index,
                                              'from_bin_id',
                                              e.target.value,
                                            )
                                          }
                                          className="form-control form-select form-control-sm"
                                        >
                                          <option value=""></option>
                                          {item.bins.map((bin, idx) => (
                                            <option
                                              key={idx}
                                              value={bin.binnumber_id}
                                            >
                                              {bin.binnumber}
                                            </option>
                                          ))}
                                        </select>
                                      </td>
                                      <td>
                                        <select
                                          disabled={confirm}
                                          value={row.to_bin_id}
                                          onChange={(e) =>
                                            binHandleChange(
                                              item.id,
                                              index,
                                              'to_bin_id',
                                              e.target.value,
                                            )
                                          }
                                          className="form-control form-select form-control-sm"
                                        >
                                          <option value=""></option>
                                          {binDestBins.map((bin, idx) => (
                                            <option
                                              key={idx}
                                              value={bin.binnumber_id}
                                            >
                                              {bin.binnumber}
                                            </option>
                                          ))}
                                        </select>
                                      </td>
                                      <td>
                                        <input
                                          type="number"
                                          disabled={confirm}
                                          value={row.quantity}
                                          onChange={(e) =>
                                            binHandleChange(
                                              item.id,
                                              index,
                                              'quantity',
                                              e.target.value,
                                            )
                                          }
                                          className="form-control form-select form-control-sm"
                                        />
                                      </td>
                                      <td
                                        style={{
                                          display: confirm ?
                                            'none' :
                                            'table-cell',
                                        }}
                                      >
                                        <button
                                          onClick={() =>
                                            binHandleDeleteRow(item.id, index)
                                          }
                                          className="btn-sm"
                                        >
                                          Delete
                                        </button>
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                                <tfoot>
                                  {item.bin_transfer_error && (
                                    <tr className="table-light">
                                      <td colSpan={3}>
                                        <span style={{ color: 'red' }}>
                                          {item.bin_transfer_error}
                                        </span>
                                      </td>
                                    </tr>
                                  )}
                                  <tr className="table-light">
                                    <td colSpan={3}>
                                      <button
                                        onClick={() =>
                                          binHandleAddRow(item.id)
                                        }
                                        style={{
                                          display: confirm ?
                                            'none' :
                                            'table-cell',
                                        }}
                                        className="btn-sm"
                                      >
                                        Add Row
                                      </button>
                                    </td>
                                  </tr>
                                </tfoot>
                              </table>
                            </div>
                          ) : (
                            <div>
                              <p>--</p>
                            </div>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      ) : (
        ''
      )}
    </div>
  );
};
export default TransferTab;