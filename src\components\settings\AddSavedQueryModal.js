import React, { useState, useEffect } from 'react';
import { Modal, Input, Button, Typography, Select, Form, Spin, Divider } from 'antd';
import { PlusOutlined, SaveOutlined, UndoOutlined } from '@ant-design/icons';
import TextArea from 'antd/es/input/TextArea';
import { addDoc, collection, updateDoc, doc } from 'firebase/firestore';
import { db, api } from '../../pages/firebase';

const { Text } = Typography;

const AddSavedQueryModal = ({ showAddSavedQueryModal, setShowAddSavedQueryModal, newSavedQuery, setNewSavedQuery, fetchSavedQueries }) => {
  console.log('newSavedQuery', newSavedQuery);
  const [loadingTables, setLoadingTables] = useState(true);
  const [datasets, setDatasets] = useState([]);
  const [estimatedRowCount, setEstimatedRowCount] = useState(null);
  const [validatingQuery, setValidatingQuery] = useState(false);
  const [form] = Form.useForm();
  // useEffect(() => {
  //   if (showAddSavedQueryModal) {
  //     // get all datasets and tables from bigquery
  //     const getDatasetsAndTables = async () => {
  //       const resp = await api.bigQueryGetDatasetsAndTablesOnCall();
  //       const datasetsAndTables = resp.data;
  //       console.log(datasetsAndTables);
  //       setDatasets(datasetsAndTables);
  //       setLoadingTables(false);
  //     };
  //     getDatasetsAndTables();
  //   }
  // }, []);
  return (
    <Modal
      title="Add Saved Query"
      open={showAddSavedQueryModal}
      onOk={async () => {
        // TODO fix this function
        console.log('form', form.getFieldsValue());
        const formValidated = await form.validateFields();
        console.log('formValidated', formValidated);
        // form.validateFields().then(async (values) => {
        try {
          console.log('creating saved query');
          if (newSavedQuery.id) {
            await updateDoc(doc(db, 'savedQueries', newSavedQuery.id), {
              ...newSavedQuery,
              // lastRefreshed: new Date().toISOString(),
              modifiedAt: new Date().toISOString()
            }, { merge: true });
          } else {
            await addDoc(collection(db, 'savedQueries'), {
              ...newSavedQuery,
              // lastRefreshed: new Date().toISOString(),
              createdAt: new Date().toISOString()
            });
          }
          // await api.createDocOnCall({
          //   collectionName: 'savedQueries',
          //   info: {
          //     ...newSavedQuery,
          //     lastRefreshed: new Date().toISOString(),
          //     createdAt: new Date().toISOString()
          //   }
          // });
          console.log('saved query created');
          setShowAddSavedQueryModal(false);
          form.resetFields();
          await fetchSavedQueries();
        } catch (error) {
          console.error("Error saving query:", error);
        }
        // }).catch((info) => {
        //   console.log('Validate Failed:', info);
        // });
      }}
      onCancel={() => {
        setShowAddSavedQueryModal(false);
        setEstimatedRowCount(null);
        setValidatingQuery(false);
      }}
    >
      {/* {loadingTables ? <Spin /> : ( */}
      <Form layout="vertical" form={form} >
        <Form.Item label="Dataset" name="datasetId" rules={[{ required: true, message: 'Please select a dataset' }]}>
          <Input
            placeholder="Dataset"
            defaultValue={newSavedQuery.datasetId}
            value={newSavedQuery.datasetId}
            onChange={(e) => setNewSavedQuery({ ...newSavedQuery, datasetId: e.target.value })}
            allowClear
          />
        </Form.Item>
        <Form.Item label="Table Name" name="tableId" rules={[{ required: true, message: 'Please select a table' }]}>
          <Input placeholder="Table" defaultValue={newSavedQuery.tableId} value={newSavedQuery.tableId} onChange={(e) => setNewSavedQuery({ ...newSavedQuery, tableId: e.target.value })} />
        </Form.Item>
        <Form.Item label="Description" name="description" rules={[{ required: true, message: 'Please enter a description' }]}>
          <Input placeholder="Description" defaultValue={newSavedQuery.description} value={newSavedQuery.description} onChange={(e) => setNewSavedQuery({ ...newSavedQuery, description: e.target.value })} />
        </Form.Item>
        <Form.Item label="Query" name="query" rules={[{ required: true, message: 'Please enter a query' }]}>
          <TextArea placeholder="Query" defaultValue={newSavedQuery.query} value={newSavedQuery.query} onChange={(e) => {
            setNewSavedQuery({ ...newSavedQuery, query: e.target.value });
            setEstimatedRowCount(null);
          }}
          />
        </Form.Item>
        <Form.Item label="Refresh Schedule" name="refreshSchedule" rules={[{ required: true, message: 'Please enter a refresh schedule' }]}>
          <Select placeholder="Refresh Schedule" defaultValue={newSavedQuery.refreshSchedule} value={newSavedQuery.refreshSchedule} onChange={(value) => setNewSavedQuery({ ...newSavedQuery, refreshSchedule: value })}>
            <Select.Option value="every10minutes">Every 10 Minutes</Select.Option>
            <Select.Option value="every30minutes">Every 30 Minutes</Select.Option>
            <Select.Option value="everyhour">Every Hour</Select.Option>
            <Select.Option value="everyday">Every Day</Select.Option>
            <Select.Option value="everyweek">Every Week</Select.Option>
            <Select.Option value="monthly">Monthly</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="Estimated Row Count" name="estimatedRowCount" >
          {validatingQuery && <Spin />}
          {estimatedRowCount && <Text>{estimatedRowCount}</Text>}
          {estimatedRowCount === null && !validatingQuery && <Button type="primary" onClick={async () => {
            setValidatingQuery(true);
            const q = newSavedQuery.query;
            const countQuery = `SELECT COUNT(*) as count FROM (${q})`;
            console.log('countQuery', countQuery);
            const results = await api.makeSuiteQlQuery({ query: countQuery, paginate: false });
            console.log('results', results);
            setEstimatedRowCount(results.data?.[0]?.count);
            setValidatingQuery(false);
          }}>Validate Query</Button>}
        </Form.Item>
      </Form>
      {/* )} */}
    </Modal>
  );
};

export default AddSavedQueryModal;