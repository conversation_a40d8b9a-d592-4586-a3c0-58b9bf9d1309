import { SyncOutlined, TableOutlined, DatabaseOutlined } from '@ant-design/icons';
import React from 'react';
import { api } from './pages/firebase';


const SYSTEMS = {
  bigquery: {
    label: 'BigQuery',
    color: 'purple',
    fields: [
      { id: 'projectId', label: 'Project ID', defaultValue: 'hj-reporting', rules: [{ required: true, message: 'Please enter project ID' }] },
      { id: 'datasetId', label: 'Dataset ID', rules: [{ required: true, message: 'Please enter dataset ID' }] },
      { id: 'tableId', label: 'Table ID', rules: [{ required: true, message: 'Please enter table ID' }] },
    ],
  },
  netsuite: {
    label: 'Netsuite',
    color: 'blue',
    connectionKeys: [
      'realm',
      'consumerKey',
      'consumerSecret',
      'tokenKey',
      'tokenSecret',
    ],
    datasetFields: [
      'datasetId',
      'tableId',
      'description',
      'searchId',
      'refreshInterval',
      // 'lastRefreshed',
      // 'status',
      // 'errors',
    ],
    operations: [
      {
        id: 'netsuiteGetSavedSearch',
        label: 'Get Saved Search',
        icon: <SyncOutlined />,
        fields: [
          { id: 'savedSearchId', label: 'Saved Search ID', rules: [{ required: true, message: 'Please enter saved search ID' }] },
          { id: 'filterField', label: 'Filter Field', rules: [{ required: false, message: 'Please enter filter field' }] },
          { id: 'filterValue', label: 'Filter Value', rules: [{ required: false, message: 'Please enter filter value' }] },
          { id: 'maxResults', label: 'Maximum Result', rules: [{ required: true, message: 'Please enter maximum result' }] },
        ]
      },
      {
        id: 'netsuiteSuiteQL',
        label: 'SuiteQL Query',
        icon: <DatabaseOutlined />,
        fields: [
          { id: 'query', label: 'SuiteQL Query', rules: [{ required: true, message: 'Please enter SuiteQL query' }] },
          { id: 'limit', label: 'Limit', defaultValue: '5000', rules: [{ required: true, message: 'Please enter limit' }] },
        ],
        firebaseFunction: 'syncNetSuiteDataset',
      },
      {
        id: 'netsuiteTransactions',
        label: 'Netsuite Transactions',
        icon: <DatabaseOutlined />,
        fields: [
          // { id: 'searchId', label: 'Search ID', rules: [{ required: true, message: 'Please enter search ID' }] },
        ],
      },
      {
        id: 'easypostPackages',
        label: 'Easypost Packages',
        icon: <DatabaseOutlined />,
        fields: [
          // { id: 'searchId', label: 'Search ID', rules: [{ required: true, message: 'Please enter search ID' }] },
        ],
      },
    ],
  },
  shopify: {
    label: 'Shopify',
    color: 'green',
    connectionKeys: [
      'storeName',
      'apiKey',
    ],
    operations: [
      {
        id: 'shopifyGetOrders',
        label: 'Get Orders',
        icon: <SyncOutlined />,
        firebaseFunction: 'refreshShopifyOrdersOnCall',
      },
      {
        id: 'shopifyGetProducts',
        label: 'Get Products',
        icon: <SyncOutlined />,
        firebaseFunction: 'refreshShopifyProductsOnCall',
      },
      {
        id: 'shopifyGetCustomers',
        label: 'Get Customers',
        icon: <SyncOutlined />,
        firebaseFunction: 'refreshShopifyCustomersOnCall',
      },
      {
        id: 'shopifyGetDiscounts',
        label: 'Get Discounts',
        icon: <SyncOutlined />,
        firebaseFunction: 'refreshShopifyDiscountsOnCall',
      },
      {
        id: 'shopifyGetShopAds',
        label: 'Get Shop Ads',
        icon: <SyncOutlined />,
        firebaseFunction: 'refreshShopifyShopAdsOnCall',
      },
      {
        id: 'shopifyGetTraffic',
        label: 'Get Traffic',
        icon: <SyncOutlined />,
        firebaseFunction: 'refreshShopifyTrafficOnCall',
      },
      {
        id: 'shopifyGetProducts',
        label: 'Get Products',
        icon: <SyncOutlined />,
        firebaseFunction: 'refreshShopifyProductsOnCall',
      },
      {
        id: 'shopifyGetVariants',
        label: 'Get Variants',
        icon: <SyncOutlined />,
        firebaseFunction: 'refreshShopifyVariantsOnCall',
      },
    ],
  },
  amazon: {
    label: 'Amazon', color: 'red', connectionKeys: [
      'clientId',
      'clientSecret',
      'refreshToken',
      'marketplaceId'
    ],
    operations: [
      {
        id: 'amazonGetOrders',
        label: 'Get Orders',
        icon: <SyncOutlined />
      },
      {
        id: 'amazonGetLiveInventory',
        label: 'Get Live Inventory',
        icon: <SyncOutlined />
      },
    ],
    datasetFields: [
      'datasetId',
      'tableId',
      'description',
      'reportId',
      'lookback',
      'refreshInterval',
      'lastRefreshed',
      'status',
      'errors',
    ]
  },
  tiktok: {
    label: 'Tiktok', color: 'red', connectionKeys: [
      'accessToken',
      'shopCipher',
      'shopId',
      'appKey',
      'appSecret',
      'appId'
    ],
    operations: [
      {
        id: 'tiktokGetOrders',
        label: 'Get Orders',
        icon: <SyncOutlined />
      },
    ],
    datasetFields: [
      'datasetId',
      'tableId',
      'description',
      'reportId',
      'lookback',
      'refreshInterval',
      'lastRefreshed',
      'status',
      'errors',
    ]
  },
};

const averageSoldQuery = ({ upcList, forecastNodeList, lookbackDays }) => {
  const upcListStr = upcList.map(u => `R'''${u}'''`).join(', ');
  const forecastNodeListStr = forecastNodeList.map(n => `R'''${n}'''`).join(', ');

  return `
    DECLARE lookbackDays INT64 DEFAULT ${lookbackDays};
    DECLARE upcList ARRAY<STRING>;
    DECLARE forecastNodeList ARRAY<STRING>;
    
    SET upcList = [${upcListStr}];
    SET forecastNodeList = [${forecastNodeListStr}];

    WITH calendar AS (
      SELECT
        d AS date,
        upc,
        forecast_node
      FROM
        UNNEST(GENERATE_DATE_ARRAY(DATE_SUB(CURRENT_DATE(), INTERVAL lookbackDays DAY), CURRENT_DATE())) AS d,
        UNNEST(upcList) AS upc,
        UNNEST(forecastNodeList) AS forecast_node
    ),
    sales_raw AS (
      SELECT
        t.upc,
        t.forecast_node,
        t.date AS sale_date,
        -1 * t.quantity AS units_sold
      FROM
        \`hj-reporting.transactions.transactions_netsuite\` t
      WHERE
        t.date >= DATE_SUB(CURRENT_DATE(), INTERVAL lookbackDays DAY)
        AND t.upc IN UNNEST(upcList)
        AND t.forecast_node IN UNNEST(forecastNodeList)
        AND t.account = '40100 Gross Revenue'
        
        AND (t.type IN ('CashSale', 'SalesOrd') OR (t.type = 'CustInvc' AND t.created_from IS NULL))
    ),
    sales_daily AS (
      SELECT
        upc,
        forecast_node,
        sale_date AS date,
        SUM(units_sold) AS total_units_sold
      FROM
        sales_raw
      GROUP BY
        upc, forecast_node, sale_date
    ),
    inventory AS (
      SELECT
        upc,
        date,
        qty_available
      FROM
        \`hj-reporting.inventory.historical_inventory_netsuite\`
      WHERE
        date >= DATE_SUB(CURRENT_DATE(), INTERVAL lookbackDays DAY)
    ),
    calendar_with_data AS (
      SELECT
        cal.date,
        cal.upc,
        cal.forecast_node,
        COALESCE(s.total_units_sold, 0) AS units_sold,
        IFNULL(CAST(i.qty_available AS INT64) > 0, FALSE) AS in_stock
      FROM
        calendar cal
      LEFT JOIN sales_daily s
        ON cal.upc = s.upc AND cal.forecast_node = s.forecast_node AND cal.date = s.date
      LEFT JOIN inventory i
        ON cal.upc = i.upc AND cal.date = i.date
    )
    SELECT
      upc,
      forecast_node,
      COUNTIF(in_stock) AS instock_days,
      SUM(units_sold) AS total_units_sold,
      SUM(CASE WHEN in_stock THEN units_sold ELSE 0 END) AS units_sold_when_in_stock,
      ROUND(AVG(CASE WHEN in_stock THEN units_sold ELSE NULL END) * 30.33, 2) AS avg_daily_sales_run_rate
    FROM
      calendar_with_data
    GROUP BY
      upc, forecast_node
    HAVING
      avg_daily_sales_run_rate > 0;
  `;
};
const averageBookedQuery = ({ upcList, forecastNodeList, lookbackDays }) => {
  const upcListStr = upcList.map(u => `R'''${u}'''`).join(', ');
  const forecastNodeListStr = forecastNodeList.map(n => `R'''${n}'''`).join(', ');

  return `
    DECLARE lookbackDays INT64 DEFAULT ${lookbackDays};
    DECLARE upcList ARRAY<STRING>;
    DECLARE forecastNodeList ARRAY<STRING>;
    
    SET upcList = [${upcListStr}];
    SET forecastNodeList = [${forecastNodeListStr}];

    WITH
      calendar AS (
        SELECT
          d AS date,
          upc,
          forecast_node
        FROM
          UNNEST(GENERATE_DATE_ARRAY(DATE_SUB(CURRENT_DATE(), INTERVAL lookbackDays DAY), CURRENT_DATE())) AS d,
          UNNEST(upcList) AS upc,
          UNNEST(forecastNodeList) AS forecast_node
      ),
      sales_raw AS (
        SELECT
          t.upc,
          t.forecast_node,
          t.Date AS sale_date,
          t.quantity * -1 AS units_sold
        FROM
          \`hj-reporting.transactions.transactions_netsuite\` t
        WHERE
          t.Date >= DATE_SUB(CURRENT_DATE(), INTERVAL lookbackDays DAY)
          AND t.upc IN UNNEST(upcList)
          AND t.forecast_node IN UNNEST(forecastNodeList)
          AND t.account = '40100 Gross Revenue'
          AND t.is_posting = true 
          AND t.type NOT IN ('InvAdjst','InvTrnfr','ItemRcpt','InvWksht') 
      ),
      sales_daily AS (
        SELECT
          upc,
          forecast_node,
          sale_date AS date,
          SUM(units_sold) AS total_units_sold
        FROM
          sales_raw
        GROUP BY
          upc,
          forecast_node,
          sale_date
      ),
      calendar_with_data AS (
        SELECT
          cal.date,
          cal.upc,
          cal.forecast_node,
          COALESCE(s.total_units_sold, 0) AS units_sold
        FROM
          calendar cal
        LEFT JOIN
          sales_daily s
        ON
          cal.upc = s.upc
          AND cal.forecast_node = s.forecast_node
          AND cal.date = s.date
      )
    SELECT
      upc,
      forecast_node,
      COUNT(*) AS active_days,
      SUM(units_sold) AS total_units_sold,
      ROUND(AVG(units_sold) * 30.33, 2) AS avg_daily_sales_run_rate
    FROM
      calendar_with_data
    GROUP BY
      upc,
      forecast_node
    HAVING
      avg_daily_sales_run_rate > 0;
  `;
};

const FORECAST_METHODS = {
  'original': { label: 'Original', value: 'original' },
  'external': { label: 'External Demand', value: 'external' },
  'seasonalTas7': { label: 'Seasonal (TAS7)', value: 'seasonalTas7' },
  'seasonalTas30': { label: 'Seasonal (TAS30)', value: 'seasonalTas30' },
  'seasonalTas90': { label: 'Seasonal (TAS90)', value: 'seasonalTas90' }
};
const lifeStatusColors = {
  'Active': '#222', // black
  'Launching': '#1890ff', // blue
  'Phasing Out': '#aaa', // grey
  'Obsolete': '#a8071a', // dark red
};
const locObj = {
  '3': 'HQ',
  '9': 'Amazon FBA',
  '17': 'Consignment',
  '24': 'Dropship',
  '25': 'Mercado Libre',
  '23': 'WFS',
};
const safeString = v => (typeof v === 'object' && v !== null ? v.name : v) || '';

const buildForecastNode = (row) => {
  const parts = [
    safeString(row.region),
    safeString(row.division),
    safeString(row.class),
    safeString(row.channel)
  ];
  const customer = safeString(row.customer);
  if (customer) {
    parts.push(customer);
  }
  return parts.filter(Boolean).join('_');
};
const itemClassificationColors = {
  'A': '#4CAF50', // Green
  'B': '#FFC107', // Yellow
  'C': '#F44336',  // Red
  'No Class': '#9E9E9E', // Grey
  '': 'transparent' // Transparent for empty strings
};
const PRODUCT_SPECIFICATION_COLORS = {
  'core': '#222', // Black
  'exclusive': '#A0522D', // Brown
  'limited': '#800080', // Dark purple
  'seasonal': '#FF9800', // Dark orange
};

const SALES_PREDICTOR_METHODS = {
  'seasonal': 'Seasonal TAS',
  'open_orders': 'Open Orders',
};
const SO_STATUS_COLORS = {
  'Pending Approval': 'orange', // blue
  'Pending Fulfillment': '#1890ff', // blue
  'Partially Fulfilled': '#1890ff', // blue
  'Pending Billing': 'red', // dark orange
  'Pending Billing/Partially Fulfilled': 'red', // dark orange
  'Billed': 'darkgrey', // dark green
  'Cancelled': 'darkgrey', // Grey
  'Closed': 'darkgrey', // Grey,
};

const ORDER_ALLOCATION_STRATEGIES = {
  '0': '- Do Not Allocate -',
  '2': 'Latest Possible Supply',
  '-2': 'Allocate Available Qty',
  '-3': 'Allocate Complete Qty',
};
function getValidDaysForMonth(row, monthField) {
  // monthField is like "Jul 2024"
  const [monthStr, yearStr] = monthField.split(' ');
  const monthIndex = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].indexOf(monthStr);
  const year = parseInt(yearStr, 10);
  const daysInMonth = new Date(year, monthIndex + 1, 0).getDate();

  // Parse launch and end dates
  let launchDate = row.launchdate;
  let endDate = row.enddate;
  // Accept both string and object with .value
  if (launchDate && typeof launchDate === 'object' && launchDate.value) launchDate = launchDate.value;
  if (endDate && typeof endDate === 'object' && endDate.value) endDate = endDate.value;
  const launch = launchDate ? new Date(launchDate) : null;
  const end = endDate ? new Date(endDate) : null;

  // Start and end of this month
  const monthStart = new Date(year, monthIndex, 1);
  const monthEnd = new Date(year, monthIndex, daysInMonth);

  // Valid start is max(launch, monthStart)
  let validStart = monthStart;
  if (launch && launch > monthStart) validStart = launch;
  // Valid end is min(end, monthEnd)
  let validEnd = monthEnd;
  if (end && end < monthEnd) validEnd = end;

  // If validEnd < validStart, no valid days
  if (validEnd < validStart) return [];

  // Build array of valid days (YYYY-MM-DD)
  const validDays = [];
  let d = new Date(validStart);
  while (d <= validEnd) {
    // Only include days in this month
    if (d.getMonth() === monthIndex && d.getFullYear() === year) {
      validDays.push(d.toISOString().split('T')[0]);
    }
    d.setDate(d.getDate() + 1);
  }
  return validDays;
}

const INVENTORY_EXCEPTION_REPORT_URL = "gs://hj-reporting.firebasestorage.app/forecast/inventory_exception_report/inventory_exception_report.json";
const DEMAND_PLAN_URL = "gs://hj-reporting.firebasestorage.app/forecast/demand_plan/demand_plan.json";

export {
  SYSTEMS,
  FORECAST_METHODS,
  averageSoldQuery,
  lifeStatusColors,
  averageBookedQuery,
  locObj,
  buildForecastNode,
  itemClassificationColors,
  SALES_PREDICTOR_METHODS,
  SO_STATUS_COLORS,
  ORDER_ALLOCATION_STRATEGIES,
  getValidDaysForMonth,
  PRODUCT_SPECIFICATION_COLORS,
  INVENTORY_EXCEPTION_REPORT_URL,
  DEMAND_PLAN_URL,
};