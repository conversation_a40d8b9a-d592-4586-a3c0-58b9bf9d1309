import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Button, DatePicker } from 'antd';
import { collection, onSnapshot } from 'firebase/firestore';
import { db } from '../pages/firebase';
import { SYSTEMS } from '../constants';
import Title from 'antd/es/typography/Title';
import dayjs from 'dayjs';

const { TextArea } = Input;

const DatasetModal = ({ visible, onCancel, dataset, onSave }) => {
  const [form] = Form.useForm();
  const [connectors, setConnectors] = useState([]);
  const [selectedConnector, setSelectedConnector] = useState(null);
  const [operations, setOperations] = useState([]);
  const [selectedOperation, setSelectedOperation] = useState(null);
  
  // Fetch connectors when component mounts
  useEffect(() => {
    fetchConnectors();
  }, []);
  
  // Set form values and initialize connector/operation when dataset changes
  useEffect(() => {
    if (dataset) {
      const formValues = {
        ...dataset,
        startTime: dataset.startTime ? dayjs(dataset.startTime) : null,
        endTime: dataset.endTime ? dayjs(dataset.endTime) : null,
      };
      form.setFieldsValue(formValues);
      
      // Find the connector from the dataset's connectorId
      const connector = connectors.find(c => c.id === dataset.connectorId);
      if (connector) {
        setSelectedConnector(connector);
        
        // Set operations based on the connector's system
        const systemOperations = SYSTEMS[connector.system]?.operations || [];
        setOperations(systemOperations);
        
        // Find the operation from the dataset's operationId
        const operation = systemOperations.find(o => o.id === dataset.operationId);
        if (operation) {
          setSelectedOperation(operation);
        }
      }
    } else {
      // Reset form and states when adding a new dataset
      form.setFieldsValue({
        startTime: null,
        endTime: null
      });
      form.resetFields();
      setSelectedConnector(null);
      setSelectedOperation(null);
      setOperations([]);
    }
  }, [dataset, connectors, form]);
  
  const fetchConnectors = async () => {
    const connectorsRef = collection(db, 'connectors');
    const unsubscribe = onSnapshot(connectorsRef, (connectorsSnapshot) => {
      const connectorsData = connectorsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data()
      }));
      setConnectors(connectorsData);
    });
    return unsubscribe;
  };
  
  // Update operations when selected connector changes
  useEffect(() => {
    if (selectedConnector) {
      setOperations(SYSTEMS[selectedConnector.system]?.operations || []);
    }
  }, [selectedConnector]);

  const handleSubmit = () => {
    form.validateFields()
      .then(values => {
        // Convert dayjs objects to ISO strings for date fields
        const submitValues = {
          ...values,
          startTime: values.startTime ? values.startTime.toISOString() : null,
          endTime: values.endTime ? values.endTime.toISOString() : null,
        };
        onSave(submitValues);
        handleCancel();
      })
      .catch(info => {
        console.log('Validate Failed:', info);
      });
  };

  const handleCancel = () => {
    // Reset form with empty values for date fields
    form.setFieldsValue({
      startTime: null,
      endTime: null
    });
    form.resetFields();
    setSelectedConnector(null);
    setSelectedOperation(null);
    setOperations([]);
    onCancel();
  };
  
  return (
    <Modal
      title={dataset ? "Edit Dataset" : "Add Dataset"}
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      width={800}
      destroyOnClose={true}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Cancel
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          Save
        </Button>,
      ]}
    >
      <Form 
        form={form} 
        layout="vertical" 
        initialValues={{
          ...dataset,
          startTime: dataset?.startTime ? dayjs(dataset.startTime) : null,
          endTime: dataset?.endTime ? dayjs(dataset.endTime) : null,
        }}
      >
        <Form.Item name="connectorId" label="Connector" rules={[{ required: true, message: 'Please select connector' }]}>
          <Select onChange={(value) => setSelectedConnector(connectors.find((connector) => connector.id === value))}>
            {connectors.map((connector) => (
              <Select.Option key={connector.id} value={connector.id}>{connector.name}</Select.Option>
            ))}
          </Select>
        </Form.Item>
        {selectedConnector && (
          <Form.Item name="operationId" label="Operation" rules={[{ required: true, message: 'Please select operation' }]}>
            <Select onChange={(value) => setSelectedOperation(operations.find((operation) => operation.id === value))}>
              {operations.map((operation) => (
                <Select.Option key={operation.id} value={operation.id}>{operation.label}</Select.Option>
              ))}
            </Select>
          </Form.Item>)
        }
        <Title level={5}>BigQuery Fields:</Title>
        {SYSTEMS['bigquery']?.fields.map((field) => (
          <Form.Item key={field.id} name={field.id} label={field.label} rules={[{ required: true, message: 'Please enter ' + field.label }]}>
            <Input placeholder={field.defaultValue} />
          </Form.Item>
        ))}
        
        <Form.Item name="updateType" label="Update Type" rules={[{ required: true, message: 'Please select update type' }]}>
          <Select>
            <Select.Option value="append">Append</Select.Option>
            <Select.Option value="replace">Replace</Select.Option>
            <Select.Option value="upsert">Upsert</Select.Option>
          </Select>
        </Form.Item>

        {(selectedOperation?.id === 'netsuiteSuiteQL' || selectedOperation?.id === 'netsuiteGetSavedSearch') && (
          <>
            <Title level={5}>Date Configuration:</Title>
            <Form.Item 
              name="dateInterval" 
              label="Date Interval (hours)" 
              rules={[{ required: true, message: 'Please enter date interval' }]}
              tooltip="The interval in hours to advance the date range for each batch"
            >
              <Input type="number" min={1} placeholder="e.g. 24 for daily intervals" />
            </Form.Item>
            <Form.Item 
              name="startTime" 
              label="Start Time" 
              rules={[{ required: true, message: 'Please select start time' }]}
              tooltip="The initial start time for the date range"
            >
              <DatePicker showTime format="YYYY-MM-DD HH:mm:ss" />
            </Form.Item>
            <Form.Item 
              name="endTime" 
              label="End Time" 
              rules={[{ required: true, message: 'Please select end time' }]}
              tooltip="The initial end time for the date range"
            >
              <DatePicker showTime format="YYYY-MM-DD HH:mm:ss" />
            </Form.Item>
          </>
        )}
        
        {selectedConnector && (
          <>
            <Title level={5}>{SYSTEMS[selectedConnector.system]?.label} Fields:</Title>
            {selectedOperation && selectedOperation?.fields?.map((field) => (
              <Form.Item key={field.id} name={field.id} label={field.label} defaultValue={field.defaultValue} rules={field.rules}  >
                {field.id === 'query' ? (
                  <TextArea 
                    rows={12} 
                    placeholder="Enter your SuiteQL query here. Use {{startTime}} and {{endTime}} as placeholders for date filtering"
                    style={{ 
                      fontFamily: 'monospace',
                      minHeight: '300px',
                      resize: 'vertical'
                    }}
                  />
                ) : (
                  <Input />
                )}
              </Form.Item>
            ))}
          </>
        )}

        <Form.Item shouldUpdate={(prevValues, currentValues) => prevValues.updateType !== currentValues.updateType}>
          {({ getFieldValue }) => {
            const updateType = getFieldValue('updateType');
            return updateType === 'upsert' ? (
              <Form.Item
                name="key"
                label="Matching Column"
                rules={[{ required: true, message: 'Please enter matching column for upsert' }]}
              >
                <Input placeholder="Column name to match for upsert" />
              </Form.Item>
            ) : null;
          }}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default DatasetModal;