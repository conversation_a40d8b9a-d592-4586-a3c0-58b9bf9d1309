import React, { useMemo } from 'react';
import { ReferenceLine } from 'recharts';

/**
 * Custom hook for rendering KPI goal reference lines in recharts
 * @param {Object} params
 * @param {Object} params.goalConfig - The goal configuration object
 * @param {string} params.yAxisId - The ID of the Y-axis to attach the reference line to
 * @param {Object} params.styles - Custom styling options
 * @param {string} params.styles.stroke - Color of the reference line
 * @param {string} params.styles.strokeDasharray - Pattern of the dashed line
 * @param {Object} params.styles.label - Label styling options
 * @param {string} params.styles.label.fill - Color of the label text
 * @param {number} params.styles.label.fontSize - Font size of the label
 * @param {string} params.styles.label.position - Position of the label ('right', 'left', 'top', 'bottom')
 * @param {string} params.labelPrefix - Prefix for the label text (e.g., "Target:", "Min:", "Max:")
 * @return {Array|null} Array of ReferenceLine components or null
 */
const useKPIReferenceLines = ({
    goalConfig,
    yAxisId = 'left',
    styles = {
        stroke: '#ff9800',
        strokeDasharray: '3 3',
        label: {
            fill: '#ff9800',
            fontSize: 12,
            position: 'right'
        }
    },
    labelPrefix = {
        single: 'Target',
        min: 'Min',
        max: 'Max'
    }
}) => {
    return useMemo(() => {
        if (!goalConfig) return null;

        const {
            goalCondition,
            value,
            min,
            max,
            unit = ''
        } = goalConfig;

        const formatValue = (val) => {
            const numVal = parseFloat(val);
            return `${labelPrefix.single}: ${numVal}${unit}`;
        };

        const formatRangeValue = (val, type) => {
            const numVal = parseFloat(val);
            return `${labelPrefix[type]}: ${numVal}${unit}`;
        };

        const createReferenceLine = (yValue, label) => (
            <ReferenceLine
                key={`goal-${yValue}`}
                yAxisId={yAxisId}
                y={parseFloat(yValue)}
                stroke={styles.stroke}
                strokeDasharray={styles.strokeDasharray}
                label={{
                    value: label,
                    position: styles.label.position,
                    fill: styles.label.fill,
                    fontSize: styles.label.fontSize
                }}
            />
        );

        // Handle different goal conditions
        switch (goalCondition) {
            case 'lower':
            case 'higher':
                return createReferenceLine(value, formatValue(value));

            case 'inRange':
            case 'higherThanMin':
            case 'lowerThanMax':
                const lines = [];

                if (min !== undefined) {
                    lines.push(createReferenceLine(min, formatRangeValue(min, 'min')));
                }

                if (max !== undefined) {
                    lines.push(createReferenceLine(max, formatRangeValue(max, 'max')));
                }

                return lines;

            default:
                return null;
        }
    }, [goalConfig, yAxisId, styles, labelPrefix]);
};

export default useKPIReferenceLines;