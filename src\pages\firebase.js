// Import the functions you need from the SDKs you need
import { initializeApp } from 'firebase/app';
import { getStorage, connectStorageEmulator } from 'firebase/storage';
import {
  GoogleAuthProvider,
  getAuth,
  signInWithPopup,
  signOut,
  connectAuthEmulator,
} from 'firebase/auth';
import { httpsCallable, getFunctions, connectFunctionsEmulator } from 'firebase/functions';
import {
  defaultVariantViews,
  defaultProductViews,
  defaultPlatforms,
  defaultFieldTypes,
  defaultDisplayTypes,
  defaultStageList,
  defaultPlatformGroups,
  defaultProductFields,
  defaultVariantFields,
  PRODUCT_TABLE,
  VARIANTS_TABLE
} from '../components/pim/template';

import {
  getFirestore,
  doc,
  connectFirestoreEmulator,
  getDoc,
  collection,
  setDoc,
  getDocs,
  query,
  where,
  limit,
  addDoc,
} from 'firebase/firestore';

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: 'AIzaSyDTUKExnzy4FcgJ9S9ZegM3lpxE1UHGrRI',
  authDomain: 'hj-reporting.firebaseapp.com',
  projectId: 'hj-reporting',
  storageBucket: 'hj-reporting.appspot.com',
  messagingSenderId: '825215331266',
  appId: '1:825215331266:web:2cc8f619ce09b527e7045b',
};

const getBigQueryType = (type) => {
  switch (type) {
    case 'text':
    case 'select':
    case 'longtext':
    case 'image':
    case 'url':
      return 'STRING';
    case 'date':
      return 'TIMESTAMP';
    case 'checkbox':
      return 'BOOLEAN';
    case 'multiimage':
      return 'ARRAY<STRING>';
    case 'percent':
    case 'decimal':
    case 'currency':
      return 'FLOAT64';
    case 'integer':
      return 'INT64';
  }
  console.error('Unknown type', type);
  return 'STRING';
};

const logout = async () => {
  try {
    console.log('Logging out user...');
    await signOut(auth);
    console.log('User logged out successfully');
  } catch (error) {
    console.error('Error during logout:', error);
    // Don't show alert for logout errors - just log them
  }
};
// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

const functions = getFunctions(app, "us-central1");
const googleProvider = new GoogleAuthProvider();
const db = getFirestore(app);
const storage = getStorage(app);

// console.log('window.location.hostname', window.location.hostname);
if (window.location.hostname === 'localhost') {
  connectAuthEmulator(auth, 'http://localhost:9099');
  connectFirestoreEmulator(db, 'localhost', 8081);
  connectFunctionsEmulator(functions, 'localhost', 5001);
  connectStorageEmulator(storage, 'localhost', 9199);
  console.log('Connected to Firebase emulators - Auth:9099, Firestore:8081, Functions:5001, Storage:9199');
}


const signInWithGoogle = async () => {
  try {
    // Configure Google Auth provider for better UX
    googleProvider.setCustomParameters({
      // hd: 'thehydrojug.com', // TODO change on dev for brapps
      prompt: 'select_account' // Always show account selection for better UX
    });
    
    console.log('Setting up Google sign-in with optimized popup behavior');
    
    // Firebase handles popup positioning, but we can ensure the main window stays focused
    // and the popup appears properly centered by the browser
    const res = await signInWithPopup(auth, googleProvider);
    const user = res.user;

    // Double-check that we have a valid user
    if (!user || !user.uid) {
      throw new Error('User authentication failed - no valid user object');
    }

    console.log('Google sign-in successful, user:', {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      emailVerified: user.emailVerified
    });

    // Check if user document exists, create if it doesn't
    const userRef = doc(db, `users/${user.uid}`);
    const userDoc = await getDoc(userRef);

    // Check if user is still authenticated before creating document
    if (!auth.currentUser || auth.currentUser.uid !== user.uid) {
      console.log('User no longer authenticated, skipping document creation');
      return;
    }

    if (!userDoc.exists()) {
      console.log('Creating new user document');
      try {
        // Double-check auth state before setDoc
        if (!auth.currentUser || auth.currentUser.uid !== user.uid) {
          console.log('User logged out during document creation, aborting');
          return;
        }
        
        await setDoc(userRef, {
          email: user.email,
          id: user.uid,
          role: 'admin',
          permissions: {
            defaultView: 'operations',
            itemReportViews: ['operations', 'marketing'],
            editItemReportFields: [],
            wholesale: true,
          },
          userPermissions: [
            {
              technicalName: 'read:inventoryreport',
              label: 'View Inventory Report',
              description: 'View the Inventory Report',
              url: '/',
              hasAccess: true,
            },
            {
              technicalName: 'read:products',
              label: 'View Products',
              description: 'View the Products',
              url: '/pim/products',
              hasAccess: true,
            },
            {
              technicalName: 'edit:products',
              label: 'Edit Products',
              description: 'Edit the Products',
              url: '/pim/products',
              hasAccess: true,
            },
            {
              technicalName: 'read:variants',
              label: 'View Variants',
              description: 'View the variants',
              url: '/pim/variants',
              hasAccess: true,
            },
            {
              technicalName: 'edit:variants',
              label: 'Edit Variants',
              description: 'Edit the variants',
              url: '/pim/variants',
              hasAccess: true,
            },
            {
              technicalName: 'read:items',
              label: 'Items',
              description: 'View the Items',
              url: '/items',
              hasAccess: false,
            },
            {
              technicalName: 'read:wholesale',
              label: 'View Wholesale',
              description: 'View the Wholesale',
              url: '/wholesale',
              hasAccess: false,
            },
            {
              technicalName: 'read:forecasts:marketing',
              label: 'View Marketing Forecasts',
              description: 'View the Marketing Forecasts',
              url: '/forecasts/marketing',
              hasAccess: false,
            },
            {
              technicalName: 'read:settings',
              label: 'View Settings',
              description: 'View the Settings',
              url: '/settings',
              hasAccess: true,
            },
            {
              technicalName: 'read:dashboard',
              label: 'View KPI Dashboard',
              description: 'Access the KPI Dashboard',
              url: '/dashboard',
              hasAccess: true,
            },
            {
              technicalName: 'read:upload',
              label: 'Upload Data',
              description: 'Upload Data',
              url: '/uploadData',
              hasAccess: false,
            },
            {
              technicalName: 'read:data',
              label: 'Data',
              description: 'Data',
              url: '/data',
              hasAccess: false,
            }
          ],
          currentProductView: defaultProductViews[0],
          currentVariantView: defaultVariantViews[0],
        });
        console.log('User document created successfully');
      } catch (setDocError) {
        console.error('Error creating user document:', setDocError);
        throw setDocError;
      }
    }

    if (window.location.hostname === 'localhost') {
      // Check auth state before localhost setup
      if (!auth.currentUser || auth.currentUser.uid !== user.uid) {
        console.log('User logged out during localhost setup, skipping');
        return;
      }

      const pimProductFields = await getDocs(query(collection(db, 'pimProductFields'), limit(1)));
      const pimVariantFields = await getDocs(query(collection(db, 'pimVariantFields'), limit(1)));
      const pimProductViews = await getDocs(query(collection(db, 'pimProductViews'), limit(1)));
      const pimVariantViews = await getDocs(query(collection(db, 'pimVariantViews'), limit(1)));
      const pimPlatforms = await getDocs(query(collection(db, 'pimPlatforms'), limit(1)));
      const pimFieldTypes = await getDocs(query(collection(db, 'pimFieldTypes'), limit(1)));
      const pimDisplayTypes = await getDocs(query(collection(db, 'pimDisplayTypes'), limit(1)));
      const pimStageList = await getDocs(query(collection(db, 'pimStageList'), limit(1)));
      const pimPlatformGroups = await getDocs(query(collection(db, 'pimPlatformGroups'), limit(1)));
      let proms = [];
      if (pimProductFields.empty || pimProductFields.docs.length === 0) {
        for (const field of defaultProductFields) {
          proms.push(addDoc(collection(db, 'pimProductFields'), field));
        }
      }
      if (pimVariantFields.empty || pimVariantFields.docs.length === 0) {
        for (const field of defaultVariantFields) {
          proms.push(addDoc(collection(db, 'pimVariantFields'), field));
        }
      }
      if (pimProductViews.empty || pimProductViews.docs.length === 0) {
        for (const view of defaultProductViews) {
          proms.push(addDoc(collection(db, 'pimProductViews'), view));
        }
      }
      if (pimVariantViews.empty || pimVariantViews.docs.length === 0) {
        for (const view of defaultVariantViews) {
          proms.push(addDoc(collection(db, 'pimVariantViews'), view));
        }
      }
      if (pimPlatforms.empty || pimPlatforms.docs.length === 0) {
        for (const platform of defaultPlatforms) {
          proms.push(addDoc(collection(db, 'pimPlatforms'), platform));
        }
      }
      if (pimFieldTypes.empty || pimFieldTypes.docs.length === 0) {
        for (const fieldType of defaultFieldTypes) {
          proms.push(addDoc(collection(db, 'pimFieldTypes'), fieldType));
        }
      }
      if (pimDisplayTypes.empty || pimDisplayTypes.docs.length === 0) {
        for (const displayType of defaultDisplayTypes) {
          proms.push(addDoc(collection(db, 'pimDisplayTypes'), displayType));
        }
      }
      if (pimStageList.empty || pimStageList.docs.length === 0) {
        for (const stage of defaultStageList) {
          proms.push(addDoc(collection(db, 'pimStages'), stage));
        }
      }
      if (pimPlatformGroups.empty || pimPlatformGroups.docs.length === 0) {
        for (const platform in defaultPlatformGroups) {
          if (Object.hasOwn(defaultPlatformGroups, platform)) {
            const platformQuery = query(collection(db, 'pimPlatforms'), where('id', '==', platform));
            const platformSnapshot = await getDocs(platformQuery);
            platformSnapshot.forEach(async (platformDoc) => {
              if (platformDoc.exists()) {
                const fields = platformDoc.data().fields || [];
                for (const item of defaultPlatformGroups[platform]) {
                  fields.push(item);
                }
                proms.push(setDoc(platformDoc.ref, { fields }, { merge: true }));
              }
            });
          }
        }
      }

      // Execute Firestore document operations
      if (proms.length > 0) {
        // Final auth check before executing Firestore operations
        if (!auth.currentUser || auth.currentUser.uid !== user.uid) {
          console.log('User logged out before executing Firestore operations, skipping');
          return;
        }
        
        try {
          await Promise.all(proms);
          console.log('Firestore setup completed successfully');
        } catch (firestoreError) {
          console.error('Firestore setup failed:', firestoreError);
          // Check if it's a permission error due to logout
          if (firestoreError.code === 'permission-denied') {
            console.log('Permission denied - user likely logged out during setup');
            return; // Don't throw, just return
          }
          // For other errors, we can continue
        }
      }

      // ADD TESTING DATA (wrapped in try-catch to prevent login failures):
      try {
        const getProductColumnsQuery = `SELECT column_name FROM hj-reporting.items.INFORMATION_SCHEMA.COLUMNS WHERE table_name = '${PRODUCT_TABLE.split('.').pop()}'`;
        const getVariantColumnsQuery = `SELECT column_name FROM hj-reporting.items.INFORMATION_SCHEMA.COLUMNS WHERE table_name = '${VARIANTS_TABLE.split('.').pop()}'`;
        const productColumns = await api.bigQueryRunQueryOnCall({ options: { query: getProductColumnsQuery } });
        const variantColumns = await api.bigQueryRunQueryOnCall({ options: { query: getVariantColumnsQuery } });

        const addProductColumnStatements = defaultProductFields
          .filter(field => !productColumns.data.find(x => x.column_name === field.fieldId))
          .map(field => `ADD COLUMN ${field.fieldId} ${getBigQueryType(field.fieldType)}`)
          .join(', ');
        if (addProductColumnStatements) {
          console.log('Adding product columns:', addProductColumnStatements);
          const setUpProductTableQuery = `ALTER TABLE ${PRODUCT_TABLE} ${addProductColumnStatements}`;
          proms.push(api.bigQueryRunQueryOnCall({ options: { query: setUpProductTableQuery } }));
        }

        const addVariantColumnStatements = defaultVariantFields
          .filter(field =>
            !variantColumns.data.find(x => x.column_name === field.fieldId))
          .map(field => `ADD COLUMN ${field.fieldId} ${getBigQueryType(field.fieldType)}`)
          .join(', ');
        if (addVariantColumnStatements) {
          console.log('Adding variant columns:', addVariantColumnStatements);
          const setUpVariantTableQuery = `ALTER TABLE ${VARIANTS_TABLE} ${addVariantColumnStatements}`;
          proms.push(api.bigQueryRunQueryOnCall({ options: { query: setUpVariantTableQuery } }));
        }

        await Promise.all(proms);
        console.log('BigQuery setup completed successfully');
      } catch (bigQueryError) {
        console.error('BigQuery setup failed, but continuing with login:', bigQueryError);
        // Don't throw - allow login to continue even if BigQuery setup fails
      }
      // console.log('Done loading default data!');
    }
  } catch (err) {
    console.error('Error in signInWithGoogle:', err);
    console.error('Error details:', {
      code: err.code,
      message: err.message,
      stack: err.stack
    });
    
    // Don't show alerts for certain expected errors
    const isUserCancellation = err.code === 'auth/popup-closed-by-user' || err.code === 'auth/cancelled-popup-request';
    const isPermissionDenied = err.code === 'permission-denied' || (err.message && err.message.includes('PERMISSION_DENIED'));
    
    if (isPermissionDenied) {
      console.log('Suppressing permission denied error (likely due to logout):', err.code || err.message);
    } else if (isUserCancellation) {
      console.log('Suppressing user cancellation:', err.code || err.message);
    } else {
      alert(err.message);
    }
  }
};

const api = {
  makeSuiteQlQuery: httpsCallable(functions, 'makeSuiteQlQuery'),
  getNsInventoryOnCall: httpsCallable(functions, 'getNsInventoryOnCall'),
  updateItemMapOnCall: httpsCallable(functions, 'updateItemMapOnCall'),
  createNetSuiteRecordOnCall: httpsCallable(functions, 'createNetSuiteRecordOnCall'),
  // shopify
  refreshShopifyOrdersOnCall: httpsCallable(functions, 'refreshShopifyOrdersOnCall'),

  insertRowsOnCall: httpsCallable(functions, 'insertRowsOnCall'),
  runQueryOnCall: httpsCallable(functions, 'runQueryOnCall'),
  generateForecastDataOnCall: httpsCallable(functions, 'generateForecastDataOnCall'),
  updateInboundsOnCall: httpsCallable(functions, 'updateInboundsOnCall'),
  updateOrderTableOnCall: httpsCallable(functions, 'updateOrderTableOnCall'),
  updateNetSuiteRecordOnCall: httpsCallable(functions, 'updateNetSuiteRecordOnCall'),
  updateSalesHistoryOnCall: httpsCallable(functions, 'updateSalesHistoryOnCall'), // not quite working
  updateBigQueryReportsOnCall: httpsCallable(functions, 'updateBigQueryReportsOnCall'),
  deleteDocOnCall: httpsCallable(functions, 'deleteDocOnCall'),
  createDocOnCall: httpsCallable(functions, 'createDocOnCall'),
  // createDocs: httpsCallable(functions, 'createDocs'),
  // createUpdateThing: httpsCallable(functions, 'createUpdateThing'),
  modifyDocOnCall: httpsCallable(functions, 'modifyDocOnCall'),
  // modifyDocs: httpsCallable(functions, 'modifyDocs'),
  // getDocRef: httpsCallable(functions, 'getDocRef'),
  // getDocData: httpsCallable(functions, 'getDocData'),
  getFirebaseQueryDataOnCall: httpsCallable(functions, 'getFirebaseQueryDataOnCall'),
  // queryNetSuiteCount: httpsCallable(functions, 'queryNetSuiteCount'),
  bigQueryGetDatasetsAndTablesOnCall: httpsCallable(functions, 'bigQueryGetDatasetsAndTablesOnCall'),
  bigQueryAddColumnOnCall: httpsCallable(functions, 'bigQueryAddColumnOnCall'),
  bigQueryEditColumnOnCall: httpsCallable(functions, 'bigQueryEditColumnOnCall'),
  bigQueryDeleteColumnOnCall: httpsCallable(functions, 'bigQueryDeleteColumnOnCall'),
  bigQueryRunQueryOnCall: httpsCallable(functions, 'bigQueryRunQueryOnCall'),
  bigQueryRunQueriesOnCall: httpsCallable(functions, 'bigQueryRunQueriesOnCall'),
  addBigQueryTableOnCall: httpsCallable(functions, 'addBigQueryTableOnCall', { timeout: 540000 }), // 9 minutes timeout
  upsertOnCall: httpsCallable(functions, 'upsertOnCall'),
  updateDatasetOnCall: httpsCallable(functions, 'updateDatasetOnCall'),
  netsuiteTaskOnCall: httpsCallable(functions, 'netsuiteTaskOnCall'),
  applyDemandPlanAddsOnCall: httpsCallable(functions, 'applyDemandPlanAddsOnCall'),

  syncShippingExceptionsOnCall: httpsCallable(functions, 'syncShippingExceptionsOnCall'),
  bigQueryReplaceTableOnCall: httpsCallable(functions, 'bigQueryReplaceTableOnCall'),
  bigQueryLoadToTableOnCall: httpsCallable(functions, 'bigQueryLoadToTableOnCall'),
  generateDemandPlanOnCall: httpsCallable(functions, 'generateDemandPlanOnCall'),
  recalculateItemClassificationOnCall: httpsCallable(functions, 'recalculateItemClassificationOnCall'),
  recalculateInventoryExceptionsOnCall: httpsCallable(functions, 'recalculateInventoryExceptionsOnCall'),
  getInventoryExceptionsOnCall: httpsCallable(functions, 'getInventoryExceptionsOnCall'),
  // Fetch open orders for an item
  refreshOpenOrdersOnCall: httpsCallable(functions, 'refreshOpenOrdersOnCall'),
  updateSalesOrderOnCall: httpsCallable(functions, 'updateSalesOrderOnCall'),
  refreshDemandPlanOnCall: httpsCallable(functions, 'refreshDemandPlanOnCall'),
  fetchDemandPlanOnCall: httpsCallable(functions, 'fetchDemandPlanOnCall'),
  saveDemandPlanOnCall: httpsCallable(functions, 'saveDemandPlanOnCall'),
  stageDemandPlanChunkOnCall: httpsCallable(functions, 'stageDemandPlanChunkOnCall'),
  commitDemandPlanFromGcsOnCall: httpsCallable(functions, 'commitDemandPlanFromGcsOnCall'),
  getItemNodeMatrixDataOnCall: httpsCallable(functions, 'getItemNodeMatrixDataOnCall'),
  getDemandPlanDataOnCall: httpsCallable(functions, 'getDemandPlanDataOnCall'),
};

export { auth, app, db, storage, logout, signInWithGoogle, api };
