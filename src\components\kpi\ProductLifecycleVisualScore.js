import React, { useState, useEffect } from 'react';
import {
    Box,
    Grid,
    Paper,
    CircularProgress,
    Tabs,
    Tab
} from '@mui/material';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer } from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import ChartExportWrapper from './ChartExportWrapper';
// Using softer background colors for both cards and chart
const COLORS = {
    Active: '#e8f5e9',        // soft green
    'Phasing Out': '#ffebee', // soft red
    'Launching': '#fff8e1'    // soft yellow
};

const TEXT_COLORS = {
    Active: '#4caf50',
    'Phasing Out': '#ff6b6b',
    'Launching': '#ffd54f',
    Total: '#5c6bc0'  // blue for total
};

const ProductLifecycleVisualScore = () => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [viewMode, setViewMode] = useState('division');

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        try {
            const functions = getFunctions();
            const getProductLifecycleScore = httpsCallable(functions, 'getProductLifecycleScore');
            const result = await getProductLifecycleScore();
            setData(result.data);
        } catch (error) {
            console.error('Error fetching data:', error);
        } finally {
            setLoading(false);
        }
    };

    const prepareChartData = () => {
        if (!data) return [];

        const sourceData = data[`by${viewMode.charAt(0).toUpperCase() + viewMode.slice(1)}`];
        return Object.entries(sourceData)
            .map(([name, info]) => ({
                name,
                ...Object.entries(info.byStatus).reduce((acc, [status, count]) => {
                    acc[status] = count;
                    return acc;
                }, {}),
                total: info.total,
                activeCount: info.byStatus['Active'] || 0
            }))
            .sort((a, b) => b.activeCount - a.activeCount);
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            return (
                <Paper
                    elevation={3}
                    sx={{
                        p: 2,
                        bgcolor: 'background.paper',
                        border: '1px solid',
                        borderColor: 'divider'
                    }}
                >
                    <Box sx={{ fontWeight: 'medium', mb: 1 }}>
                        {label}
                    </Box>
                    {payload.map((entry) => (
                        <Box
                            key={entry.name}
                            sx={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                gap: 2,
                            }}
                        >
                            <span style={{ color: TEXT_COLORS[entry.name] }}>{entry.name}:</span>
                            <span>{entry.value}</span>
                        </Box>
                    ))}
                </Paper>
            );
        }
        return null;
    };

    if (loading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" height={400}>
                <CircularProgress />
            </Box>
        );
    }

    return (
        <ChartExportWrapper title={`Product_Lifecycle_${viewMode.charAt(0).toUpperCase() + viewMode.slice(1)}`}>
    
        <Box sx={{ p: 3, backgroundColor: '#fff' }}>
            {/* View Selector */}
            <Paper
                elevation={0}
                sx={{
                    mb: 3,
                    borderBottom: '1px solid',
                    borderColor: 'divider'
                }}
            >
                <Tabs
                    value={viewMode}
                    onChange={(e, newValue) => setViewMode(newValue)}
                    variant="scrollable"
                    scrollButtons="auto"
                    sx={{
                        '& .MuiTab-root': {
                            minHeight: 48,
                            textTransform: 'none',
                            fontSize: '0.875rem'
                        }
                    }}
                >
                    <Tab label="By Division" value="division" />
                    <Tab label="By Category" value="category" />
                    <Tab label="By Family" value="family" />
                    <Tab label="By Type" value="type" />
                    <Tab label="By Specification" value="specification" />
                </Tabs>
            </Paper>

            {/* Chart */}
            <Paper
                elevation={1}
                sx={{
                    p: 3,
                    height: 500,
                    bgcolor: 'background.paper',
                    borderRadius: 1
                }}
            >
                <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                        data={prepareChartData()}
                        margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                    >
                        <XAxis
                            dataKey="name"
                            angle={-45}
                            textAnchor="end"
                            height={70}
                            tick={{ fontSize: 12 }}
                        />
                        <YAxis
                            label={{
                                value: 'Number of Products',
                                angle: -90,
                                position: 'insideLeft',
                                style: { textAnchor: 'middle' }
                            }}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        {data && Object.keys(data.byStatus || {}).map((status) => (
                            <Bar
                                key={status}
                                dataKey={status}
                                name={status}
                                stackId="a"
                                fill={COLORS[status]}
                                stroke={TEXT_COLORS[status]}
                                strokeWidth={1}
                            />
                        ))}
                    </BarChart>
                </ResponsiveContainer>
            </Paper>

            {/* Summary Cards at the bottom */}
            <Grid container spacing={2} sx={{ mt: 3 }}>
                {/* Total Products Card */}
                <Grid item xs={12} sm={6} md={3}>
                    <KPICard
                        title="Total Products"
                        value={data?.total.toString() || "0"}
                        bgColor="#f3f6ff"
                        textColor={TEXT_COLORS.Total}
                    />
                </Grid>
                {/* Status Cards */}
                {data && Object.entries(data.byStatus || {}).map(([status, count]) => {
                    const percentage = ((count / data.total) * 100).toFixed(1);
                    return (
                        <Grid item xs={12} sm={6} md={3} key={status}>
                            <KPICard
                                title={`${status} Products`}
                                value={`${count} (${percentage}%)`}
                                bgColor={COLORS[status]}
                                textColor={TEXT_COLORS[status]}
                            />
                        </Grid>
                    );
                })}
            </Grid>
        </Box>
        </ChartExportWrapper>
    );
};

export default ProductLifecycleVisualScore;