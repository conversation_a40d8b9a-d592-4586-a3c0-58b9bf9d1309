import React, { useState, useEffect } from 'react';
import { Box, Grid, Paper, CircularProgress, Alert, Typography } from '@mui/material';
import {
    Composed<PERSON>hart, Line, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, ReferenceLine
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import { useFormatAxisTick } from '../../hooks/useFormatAxisTick';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import ChartExportWrapper from './ChartExportWrapper';

const InboundTransportationCost = () => {
    const [inboundData, setInboundData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const formatAxisTick = useFormatAxisTick();

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const functions = getFunctions();
            const getInboundTransportationCost = httpsCallable(functions, 'getInboundTransportationCost');
            const getKPIGoalsForReport = httpsCallable(functions, 'getKPIGoalsForReport');

            const [inboundResult, goalsResult] = await Promise.all([
                getInboundTransportationCost(),
                getKPIGoalsForReport({ reportName: 'inbound-transportation' })
            ]);

            setInboundData(inboundResult.data);

            if (goalsResult.data.success) {
                setKpiGoals(goalsResult.data.data);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            setError('Failed to fetch data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    const calculateInboundTrend = () => {
        if (!inboundData?.runningData || inboundData.runningData.length < 2) return 0;
        const lastTwo = inboundData.runningData.slice(-2);
        return lastTwo[1].inboundPercentage - lastTwo[0].inboundPercentage;
    };

    // Calculate domains including the goal values
    const calculateDomains = () => {
        if (!inboundData?.runningData) return { percentageDomain: [0, 100], amountDomain: [0, 1000] };

        const percentageValues = inboundData.runningData.map(item => item.inboundPercentage);
        const revenueValues = inboundData.runningData.map(item => item.revenue);
        const inboundValues = inboundData.runningData.map(item => item.inbound);

        // Include goal values in percentage domain calculation
        const goalValue = kpiGoals?.['Inbound Transportation Costs']?.value;
        const goalMin = kpiGoals?.['Inbound Transportation Costs']?.min;
        const goalMax = kpiGoals?.['Inbound Transportation Costs']?.max;

        const allPercentageValues = [
            ...percentageValues,
            goalValue && parseFloat(goalValue),
            goalMin && parseFloat(goalMin),
            goalMax && parseFloat(goalMax)
        ].filter(Boolean);

        const maxPercentage = Math.max(...allPercentageValues);
        const minPercentage = Math.min(...allPercentageValues);

        const maxAmount = Math.max(...revenueValues, ...inboundValues);
        const minAmount = Math.min(...revenueValues, ...inboundValues);

        return {
            percentageDomain: [
                Math.max(0, Math.floor(minPercentage * 0.9)),
                Math.ceil(maxPercentage * 1.1)
            ],
            amountDomain: [
                minAmount < 0 ? Math.floor(minAmount * 1.1) : 0,
                Math.ceil(maxAmount * 1.1)
            ]
        };
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            const { inboundPercentage, revenue, inbound } = payload[0].payload;
            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                    <Typography variant="body2">{`Date: ${label}`}</Typography>
                    <Typography variant="body1" color="error" fontWeight="bold">
                        {`Inbound %: ${inboundPercentage.toFixed(2)}%`}
                    </Typography>
                    <Typography variant="body2" color="primary">
                        {`Revenue: ${formatAxisTick(revenue)}`}
                    </Typography>
                    <Typography variant="body2" color="secondary">
                        {`Inbound Cost: ${formatAxisTick(inbound)}`}
                    </Typography>
                </Paper>
            );
        }
        return null;
    };

    // Use the hook
    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: kpiGoals?.['Inbound Transportation Costs'],
        yAxisId: "left",
        styles: {
            stroke: "#ff9800",
            strokeDasharray: "3 3",
            label: {
                fill: "#ff9800",
                fontSize: 12,
                position: "right"
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Min",
            max: "Max"
        }
    });

    if (isLoading) return <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}><CircularProgress /></Box>;
    if (error) return <Alert severity="error" sx={{ mt: 2 }}>{error}</Alert>;
    if (!inboundData || !kpiGoals) return null;

    const { percentageDomain, amountDomain } = calculateDomains();
    const adjustedData = inboundData.runningData.map(item => ({
        ...item,
        adjustedRevenue: item.revenue < 0 ? 0 : item.revenue
    }));

    const inboundConfig = kpiGoals['Inbound Transportation Costs'];

    return (
        <ChartExportWrapper title="Inbound_Transportation_Cost">
            <Box sx={{ p: { xs: 2, sm: 3 }, mb: 3 }}>
                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <ResponsiveContainer width="100%" height={500}>
                        <ComposedChart data={adjustedData}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                            <XAxis dataKey="date" />
                            <YAxis
                                yAxisId="left"
                                domain={percentageDomain}
                                label={{ value: 'Inbound %', angle: -90, position: 'insideLeft' }}
                                tickFormatter={(value) => `${value.toFixed(2)}%`}
                            />
                            <YAxis
                                yAxisId="right"
                                orientation="right"
                                domain={amountDomain}
                                label={{ value: 'Amount', angle: 90, position: 'insideRight' }}
                                tickFormatter={formatAxisTick}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            <ReferenceLine y={0} stroke="#000" yAxisId="right" />
                            {goalReferenceLines}
                            <Bar yAxisId="right" dataKey="inbound" fill="#66bb6a" name="Inbound Cost" />
                            <Bar yAxisId="right" dataKey="adjustedRevenue" fill="#4fc3f7" name="Revenue" />
                            <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="inboundPercentage"
                                stroke="#e53935"
                                strokeWidth={3}
                                dot={{ r: 4, fill: "#e53935" }}
                                activeDot={{ r: 8 }}
                                name="Inbound %"
                            />
                        </ComposedChart>
                    </ResponsiveContainer>
        
                    <Grid container spacing={2} sx={{ mt: 2 }}>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Overall Inbound Transportation Cost %"
                                value={`${inboundData.overallInboundPercentage.toFixed(2)}%`}
                                bgColor="#fff3e0"
                                textColor="error"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Total Revenue"
                                value={`$${inboundData.totalRevenue.toLocaleString()}`}
                                bgColor="#e1f5fe"
                                textColor="primary"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Total Inbound Cost"
                                value={`$${inboundData.totalInbound.toLocaleString()}`}
                                bgColor="#e8f5e9"
                                textColor="success.main"
                            />
                        </Grid>
                    </Grid>
                </Paper>

                <GoalStatusDisplay
                    currentValue={inboundData.overallInboundPercentage}
                    goalConfig={inboundConfig}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculateInboundTrend()}
                    size="medium"
                    title="Inbound Transportation Cost % Performance"
                />
            </Box>
        </ChartExportWrapper>
    );
};

export default InboundTransportationCost;