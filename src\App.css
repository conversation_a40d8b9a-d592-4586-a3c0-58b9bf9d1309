  .filters {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: space-around;
    position: sticky;
    top: 0;
    background-color: white;
    padding-bottom: 15px;
    /* Remove margin */
    padding-top: 15px;
    z-index: 5;
  }

  .filter-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-weight: 600;
  }

  .button-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
  }

  .table-container {
    width: 100%;
  }

  table {
    border-collapse: collapse;
    width: 100%;
    min-width: 100%;
  }

  th,
  td,
  tr {
    text-align: left;
    border-bottom: 1px solid #dee2e6;
    padding: 5px;
    min-height: 28px;
    height: 28px;
  }

  tr:hover {
    background-color: #f5f5f5;
  }

  :root {
    --filter-height: 85px;
  }

  .results table thead {
    position: sticky;
    top: var(--filter-height);
    /* Adjust this value to match the height of your filters */
    background-color: white;
    min-width: 95%;
    z-index: 1;
  }

  .popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    background-color: rgba(0, 0, 0, 0.5);
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .popup_inner {
    position: relative;
    background-color: white;
    padding: 20px;
    border-radius: 5px;
    max-width: 80%;
    max-height: 80%;
    overflow: auto;
  }

  .results table {
    table-layout: fixed;
    width: 100%;
    min-width: 100%;
  }

  .results table th,
  .results table td {
    overflow: hidden;
  }

  /* Note indicators for demand plan cells */
  .note-indicator {
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 8px 8px 0;
    z-index: 1;
  }

  .note-indicator.todo {
    border-color: transparent #ff0004 transparent transparent;
  }

  .note-indicator.review {
    border-color: transparent #ff992b transparent transparent;
  }

  .note-indicator.general {
    border-color: transparent #1890ff transparent transparent;
  }

  a .btn .tableHeaders th {
    font-weight: bold;
    cursor: pointer;
    background-color: black;
    color: white;
  }

  .itemReportTableHeaders th {
    font-weight: bold;
    cursor: pointer;
    background-color: darkblue;
    color: white;
  }

  button {
    background-color: black;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    margin: 0 5px;
  }

  .navbar {
    display: flex;
    justify-content: space-around;
    padding: 10px;
  }

  .tab {
    color: white;
    text-decoration: none;
    font-size: 18px;
    background-color: #333;
  }

  .navbar.tab:hover {
    color: #ddd;
  }

  .navbar.tab.active {
    background-color: #ddd;
    color: black;
  }

  th {
    cursor: pointer;
  }

  th.sortAsc::after {
    content: " ▲";
  }

  th.sortDesc::after {
    content: " ▼";
  }

  .column {
    display: flex;
    flex-direction: column;
    align-content: center;
  }

  .modal {
    display: flex;
    position: fixed;
    z-index: 1;
    background-color: rgba(0, 0, 0, 0.5);
  }

  .modal-content {
    padding: 10px 20px;
  }

  .modal-lg {
    width: 1000px;
  }

  .close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
  }

  .close:hover,
  .close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
  }

  .filter-container {
    margin-bottom: 20px;
  }

  .filter-container label {
    margin-right: 10px;
  }

  .filter-container select {
    padding: 5px;
  }

  .search-container {
    margin-bottom: 20px;
  }

  .search-container label {
    margin-right: 10px;
  }

  .search-container input {
    padding: 5px;
    width: 200px;
  }

  .field-selection {
    width: 400px;
    margin: 0 auto;
    text-align: left;
  }

  .cell .visible {
    display: none;
  }

  .cell td {
    padding: 5px 10px;
  }

  .cell:hover .hidden {
    display: none;
  }

  .cell:hover .visible {
    display: inline;
  }

  .flex-container {
    display: flex;
    flex-wrap: wrap;
  }

  .flex-item {
    flex: 1 1 50%;
    /* Adjust this to control the width */
    display: flex;
  }

  .flex-item td {
    border: none;
  }

  .flex-item .center-checkbox {
    text-align: center;
    /* Horizontally center */
    vertical-align: middle;
    /* Vertically center */
    padding: 0;
    /* Optional: remove any padding */
    margin: 19px 2px;
  }

  .flex-item .center-checkbox input[type="checkbox"] {
    display: inline-block;
    vertical-align: middle;
    margin: 10px;
  }

  .scroll-to-top {
    display: block;
  }

  .btn {
    margin: 0 2px;
  }

  .react-select__control {
    border: 1px solid #d1d3e2 !important;
  }

  .form-control-sm {
    height: 38px;
  }

  .table th {
    border-top: none;
  }

  .modal-dialog {
    overflow-y: initial !important
  }

  .modal-body {
    max-height: 80vh;
    overflow-y: auto;
  }

  .hiddenTable {
    display: none;
    font-size: 12px;
    min-height: fit-content;
    background-color: white;
    border: 1px solid #ccc;
    z-index: 10000;
  }

  .hiddenTable tr {
    display: table-row;
    /* Ensure rows are always displayed */
  }

  .hiddenTableContainer:hover .hiddenTable {
    display: block;
  }

  .highlight-row {
    background-color: lightblue;
  }

  .highlight-row:hover {
    background-color: lightblue;
  }

  .demandplan-toolbar-right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 8px;
    /* margin-bottom: 12px; */
  }

  .invalid-date-range {
    color: darkred;
    font-weight: bold;
  }

  .future-launchdate {
    font-style: italic;
    font-weight: bold;
    color: #4099ec;
  }

  .past-enddate {
    font-style: italic;
    font-weight: bold;
    color: #d32f2f;
  }

  .lifestatus-active {
    color: #222;
  }

  .lifestatus-launching {
    color: #1890ff;
  }

  .lifestatus-phasingout {
    color: #aaa;
  }

  .lifestatus-obsolete {
    color: #a8071a;
  }

  .row-ship-past .ag-cell,
  .row-ship-past .ag-group-value {
    color: #d32f2f !important;
    font-weight: bold;
  }

  .row-ship-soon .ag-cell,
  .row-ship-soon .ag-group-value {
    color: #ff8c1a !important;
    font-weight: bold;
  }

  /* Material Design Header Styles */
  .modern-header {
    display: flex;
    align-items: center;
    padding: 0 24px;
    background: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    border-bottom: 1px solid #e0e0e0;
    min-height: 64px;
    transition: background-color 0.3s ease;
  }

  /* Test mode styling */
  .modern-header.test-mode {
    background: #ff9800 !important;
    box-shadow: 0 1px 3px rgba(255, 152, 0, 0.3), 0 1px 2px rgba(255, 152, 0, 0.2);
    border-bottom: 1px solid #e65100;
  }

  .modern-header.test-mode .ant-menu-horizontal {
    background: transparent !important;
  }

  .modern-header.test-mode .ant-menu-item:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  .header-brand {
    margin-right: 32px;
    white-space: nowrap;
  }

  .modern-menu {
    flex: 1 !important;
    display: flex !important;
    justify-content: flex-start !important;
    background: transparent !important;
    border: none !important;
  }

  .modern-menu .ant-menu-item {
    margin: 0 4px !important;
    padding: 0 16px !important;
    border-radius: 4px !important;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1) !important;
    border-bottom: none !important;
    height: 48px !important;
    line-height: 48px !important;
    display: flex !important;
    align-items: center !important;
    color: #616161 !important;
    font-weight: 500 !important;
  }

  .modern-menu .ant-menu-item:hover {
    background-color: rgba(0, 0, 0, 0.04) !important;
    color: #1976d2 !important;
  }

  .modern-menu .ant-menu-item-selected {
    background-color: #e3f2fd !important;
    color: #1976d2 !important;
    font-weight: 600 !important;
  }

  .modern-menu .ant-menu-item-selected:hover {
    background-color: #bbdefb !important;
    color: #1976d2 !important;
  }

  .modern-menu .logout-item {
    margin-left: auto !important;
    background-color: transparent !important;
    color: #d32f2f !important;
    font-weight: 500 !important;
  }

  .modern-menu .logout-item:hover {
    background-color: rgba(211, 47, 47, 0.04) !important;
    color: #d32f2f !important;
  }

  .modern-menu .ant-menu-item .anticon {
    margin-right: 8px !important;
    font-size: 18px !important;
  }

  /* Responsive design for smaller screens */
  @media (max-width: 768px) {
    .modern-header {
      padding: 0 16px;
      min-height: 56px;
    }

    .header-brand {
      margin-right: 16px;
    }

    .modern-menu .ant-menu-item {
      margin: 0 2px !important;
      padding: 0 12px !important;
      font-size: 14px !important;
      height: 40px !important;
      line-height: 40px !important;
    }

    .modern-menu .ant-menu-item .anticon {
      font-size: 16px !important;
      margin-right: 4px !important;
    }
  }

  @media (max-width: 480px) {
    .modern-header {
      padding: 0 12px;
    }

    .header-brand h4 {
      font-size: 18px !important;
    }

    .modern-menu .ant-menu-item {
      font-size: 12px !important;
      padding: 0 8px !important;
      margin: 0 1px !important;
    }

    .modern-menu .ant-menu-item .anticon {
      display: none !important;
    }
  }

  /* Material Design elevation for better depth */
  .modern-header:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.16), 0 2px 4px rgba(0, 0, 0, 0.23);
    transition: box-shadow 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
  }

  /* Ensure popup windows are properly handled */
  html, body {
    position: relative;
  }

  /* Improve popup centering by ensuring main window is properly positioned */
  .login-container {
    position: relative;
    z-index: 1;
  }

  .ag-row-even {
    background-color: #f0f0f0;
  }

  .stockout-row .ag-cell,
  .stockout-row .ag-group-value {
    background-color: #fff2f0;
  }

  .ag-grid-row-picked .ag-cell {
    cursor: not-allowed;
    background-color: #f0f0f0;
    opacity: 0.6;
  }

  .ag-grid-syncing .ag-cell {
    cursor: not-allowed;
    background-color: #f0f0f0;
    opacity: 0.6;
  }

  .forecast-row {
    font-style: italic;
  }

  .sales-row {
    font-weight: bold;
  }

  .shared {
    color: #1890ff;
  }

  .not-shared {
    color: #cecece;
  }
.imgSpan {
  display: flex;
  height: 100%;
  width: 100%;
  align-items: center;
}

.imgSpanLogo {
  display: flex;
  height: 100%;
  width: 100%;
  align-items: center;
  justify-content: center;
}