---
description: Describes how the app should fetch data from the different sources.
alwaysApply: false
---
If the data is being fetched from firestore, use onSnapshot to listen for changes in realtime.
If the data is being fetched from bigquery, use an oncall function to fetch the data and protect the sql query in the functions directory.
Always ensure the data is fetched in a secure way, and that sensitive data is not exposed to the client.
Suggest moving the data fetching logic to the backend if possible.
When fetching data on component mount, use useEffect to fetch the data and set the state.
Try and consolidate the useEffect hooks for fetching data unless there is a specific reason to have multiple, like different dependencies to run the useEffect.