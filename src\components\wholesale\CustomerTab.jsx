import React, { useState, useEffect } from 'react';
import { Input, Table } from 'antd';
import { api } from '../../pages/firebase';
import { SYSTEMS } from '../../constants';
import { lifeStatusColors } from '../../constants';
import { TableOutlined, SearchOutlined, PlusOutlined } from '@ant-design/icons';
import axios from 'axios';

const CustomerTab = () => {
  const [orderData, setOrderData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('');
  useEffect(() => {
    const fetchOrderData = async () => {
      try {
        const { data: results } = await api.makeSuiteQlQuery({
          query: `SELECT t.id,
              t.tranid AS document_number,
              t.trandate AS date,
              t.status,
              e.companyName AS customer,
              t.otherrefnum AS po_num,
              t.dispatch_date AS dispatch_date,
              e.category,
              SUM(tl.quantity) AS total_units,
              SUM(t.foreigntotal) AS total_dollars
          FROM 
              transaction t
          LEFT JOIN 
              customer e ON e.id = t.entity
          LEFT JOIN 
              transactionline tl ON tl.transaction = t.id
          WHERE 
              t.type = 'SalesOrd'
              AND e.category IS NOT NULL
              AND t.status NOT IN ('G','H','F','C')
          GROUP BY 
              t.id, t.tranid, t.trandate, t.status, e.companyName, t.otherrefnum, t.dispatch_date, e.category`,
          paginate: false,
        });
        // console.log(results);
        setOrderData(results);
        setLoading(false);
      } catch (err) {
        // console.log(err);
      }
    };
    fetchOrderData();

    return;
  }, []);

  return loading ? (
    <div className="spinner-container">
      <div className="spinner-border text-primary" role="status">
        <span className="sr-only">Loading...</span>
      </div>
    </div>
  ) : (
    <div className="card shadow mt-3 mb-4">
      <div className="card-head">
        <h4 style={{ padding: '20px 20px 0px 20px' }}>Customers</h4>
      </div>
      <div className="card-body">
        <div className="table-responsive">
          <Input value={filter} onChange={(e) => setFilter(e.target.value)} />
          <Table
            dataSource={orderData.filter(
              (x) =>
                !filter ||
                JSON.stringify(x)
                  .toLowerCase()
                  .includes(filter.toLowerCase()),
            )}
            columns={[
              {
                title: 'Customer',
                dataIndex: 'customer',
                key: 'customer',
                sorter: (a, b) => a.customer - b.customer,
              },
              {
                title: 'SO#',
                dataIndex: 'document_number',
                key: 'document_number',
                sorter: (a, b) =>
                  a.document_number.toString().toLowerCase() -
                  b.document_number.toString().toLowerCase(),
                render: (text, record) => (
                  <a
                    href={`https://6810379.app.netsuite.com/app/accounting/transactions/salesord.nl?id=${record.id}`}
                  >
                    {text}
                  </a>
                ),
              },
              {
                title: 'Status',
                dataIndex: 'status',
                key: 'status',
                render: (text) => statusMap[text],
              },
              {
                title: 'PO #',
                dataIndex: 'po_num',
                key: 'po_num',
              },
              {
                title: 'Date',
                dataIndex: 'date',
                key: 'date',
              },
              {
                title: 'Ship Date',
                dataIndex: 'dispatch_date',
                key: 'dispatch_date',
              },
              {
                title: 'Total Units',
                dataIndex: 'total_units',
                key: 'total_units',
              },
              {
                title: 'Back Ordered',
                dataIndex: 'back_ordered',
                key: 'back_ordered',
              },
              {
                title: 'Total Dollars',
                dataIndex: 'total_dollars',
                key: 'total_dollars',
              },
            ]}
            rowKey="id"
            onRow={(record) => ({
              onClick: () => setModalItem(record.id),
            })}
            pagination={false}
          />
        </div>
      </div>
    </div>
  );
};

export default CustomerTab;
