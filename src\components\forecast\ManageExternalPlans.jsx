  setRows(formattedData || []);
  
  // Only pivot the data if there are no pending changes, otherwise preserve existing pivoted rows
  if (Object.keys(pendingRows).length === 0 || reset) {
    // Pivot the data for display
    const pivoted = pivotData(formattedData || []);
    setPivotedRows(pivoted);
  } else {
    // Apply pending changes to existing pivoted rows instead of recreating them
    setPivotedRows(prevPivotedRows => {
      if (prevPivotedRows.length === 0) {
        // If no existing pivoted rows, create them from the data
        return pivotData(formattedData || []);
      }
      
      // Apply pending changes to existing pivoted rows
      const updatedPivotedRows = prevPivotedRows.map(row => {
        const pendingData = pendingRows[row.key];
        if (pendingData && !pendingData._deleted) {
          // Apply pending changes to this row
          const updatedRow = { ...row };
          
          // Update values that have pending changes
          Object.keys(pendingData.pendingChanges || {}).forEach(field => {
            if (field !== '_deleted') {
              updatedRow[field] = pendingData[field] || 0;
            }
          });
          
          // Preserve original values and pending changes
          updatedRow.originalValues = pendingData.originalValues || row.originalValues || {};
          updatedRow.pendingChanges = pendingData.pendingChanges || {};
          updatedRow.changeType = pendingData.changeType || 'modified';
          
          return updatedRow;
        }
        return row;
      });
      
      return updatedPivotedRows;
    });
  } 