import React, { useEffect, useState } from 'react';
import { Input, InputNumber, Select, DatePicker, Checkbox, Image, Typography } from 'antd';
const { Text } = Typography;
import dayjs from 'dayjs';

const FormItem = ({ field, val, record, index, productsAsOptions, dataIndex, setImportData }) => {
  const handleChange = (index, fieldId, value) => {
    setImportData(prevItems => {
      const updatedItems = [...prevItems];
      updatedItems[index] = { ...updatedItems[index], [fieldId]: value };
      return updatedItems;
    });
  };

  if (dataIndex === 'id') {
    return (
      <Text>{val || ''}</Text>
    );
  } else if (field.fieldType === 'text') {
    return (
      <Input
        defaultValue={val || ''}
        name={field.fieldId}
        onChange={(e) => {
          handleChange(index, field.fieldId, e.target.value);
        }}
        style={{ minWidth: '100px' }}
      />
    );
  } else if (field.fieldType === 'url') {
    return (
      <Input
        defaultValue={val || ''}
        name={field.fieldId}
        onChange={(e) => {
          handleChange(index, field.fieldId, e.target.value);
        }}
        style={{ minWidth: '100px' }}
        rules={[{ type: 'url', warningOnly: true }]}
      />
    );
  } else if (field.fieldType === 'currency' || field.fieldType === 'integer' || field.fieldType === 'decimal' || field.fieldType === 'percent') {
    return (
      <Input
        defaultValue={val || ''}
        name={field.fieldId}
        prefix={field.fieldType === 'currency' ? '$' : ''}
        suffix={(field.fieldType === 'percent') ? '%' : ''}
        onChange={(e) => {
          const value = e.target.value;
          if (value === '' || /^\d*\.?\d*$/.test(value)) {
            handleChange(index, field.fieldId, value);
          }
        }}
        onBlur={(e) => {
          const value = e.target.value;
          if (!value) return;
          if (field.fieldType !== 'integer') {
            const parsed = parseFloat(value);
            console.log(typeof parsed);
            console.log(parsed.toFixed(2));
            if (!isNaN(parsed)) {
              handleChange(index, field.fieldId, parsed.toFixed(2));
            }
          }
        }}
        style={{ minWidth: '100px' }}
      />
    );
  } else if (field.fieldType === 'longtext') {
    return (
      <Input.TextArea
        defaultValue={val || ''}
        name={field.fieldId}
        onChange={(e) => {
          handleChange(index, field.fieldId, e.target.value);
        }}
        style={{ minWidth: '100px' }}
      />
    );
  } else if (field.fieldType === 'date') {
    return (
      <DatePicker
        defaultValue={(val) ? dayjs(val) : ''}
        name={field.fieldId}
        onChange={(dateVal) => {
          handleChange(index, field.fieldId, (dateVal) ? dateVal.$d : '');
        }}
        format="MM/DD/YYYY"
        style={{ minWidth: '100px' }}
      />
    );
  } else if (field.fieldType === 'select') {
    const fieldOptions = (field.fieldId === 'productId' || field.relatedField === 'productId' ? productsAsOptions : field.options);
    return (
      <Select
        name={field.fieldId}
        placeholder="Select..."
        allowClear
        defaultValue={val}
        onChange={(val) => {
          handleChange(index, field.fieldId, val);
        }}
        style={{ minWidth: '100px' }}
        options={fieldOptions.map(option => ({ label: option.label, value: option.id }))}
      />
    );
  } else if (field.fieldType === 'checkbox') {
    return (
      <Checkbox checked={val || false} onChange={(e) => {
        handleChange(index, field.fieldId, e.target.checked);
      }}>{field.label}</Checkbox>
    );
  } else if (field.fieldType === 'image') {
    return (
      <Image
        height={40}
        src={val.startsWith('http') ? val : `https://6810379.app.netsuite.com${val}`}
      />
    );
  } else if (field.fieldType === 'multiimage') {
    return (
      <Image.PreviewGroup>
        {val.map((image, index) => (
          <Image key={index} src={image} />
        ))}
      </Image.PreviewGroup>
    );
  }
};
export default FormItem;