{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "echo 'Skipping lint'", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "index.js", "dependencies": {"@google-cloud/bigquery": "^7.5.0", "@google-cloud/storage": "^7.9.0", "@google-cloud/tasks": "^5.5.2", "axios": "^1.6.2", "csv-parse": "^5.6.0", "csv-parser": "^3.0.0", "dayjs": "^1.11.13", "firebase-admin": "^11.11.0", "firebase-functions": "^6.4.0", "netsuite-rest": "^1.1.0", "oauth-1.0a": "^2.2.6", "papaparse": "^5.5.2", "suiteql": "^1.0.8"}, "devDependencies": {"eslint": "^8.57.1", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.1.0"}, "private": true}