import React from 'react';
import { useUser } from '../contexts/UserContext';
import { Card, Typography, Tag } from 'antd';

const { Title, Text } = Typography;

function UserInfo() {
  const { userData, isAuthenticated, loading } = useUser();

  if (loading) {
    return <div>Loading user info...</div>;
  }

  if (!isAuthenticated || !userData) {
    return <div>Not authenticated</div>;
  }

  return (
    <Card title="User Information" style={{ margin: '16px' }}>
      <div>
        <Title level={4}>User Details</Title>
        <p><strong>Email:</strong> {userData.email}</p>
        <p><strong>Role:</strong> {userData.role}</p>
        <p><strong>ID:</strong> {userData.id}</p>
        
        <Title level={4} style={{ marginTop: '16px' }}>Permissions</Title>
        <div>
          {userData.userPermissions?.map((permission, index) => (
            <Tag 
              key={index} 
              color={permission.hasAccess ? 'green' : 'red'}
              style={{ margin: '4px' }}
            >
              {permission.label} ({permission.hasAccess ? 'Access' : 'No Access'})
            </Tag>
          ))}
        </div>
      </div>
    </Card>
  );
}

export default UserInfo; 