import React, { useState, useEffect } from 'react';
import { Modal, Spin, Typography, Table, Card } from 'antd';
import axios from 'axios';

const { Title, Text } = Typography;

const OrderDetailsModal = ({ modalItem, setModalItem }) => {
  const [orderModalData, setOrderModalData] = useState(null);
  const [modalLoading, setModalLoading] = useState(true);
  const closeModal = () => {
    setModalItem('');
  };

  useEffect(() => {
    if (!modalItem) return;
    const controller = new AbortController();
    const signal = controller.signal;
    const fetchData = async () => {
      setModalLoading(true);
      try {
        const orderId = typeof modalItem === 'object' ? modalItem.id : modalItem;
        const orderDataResponse = await axios.get(
          'https://6810379.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=3661&deploy=1' +
          '&action=getsalesorderdata&param_so_id=' +
          orderId +
          '&compid=6810379&ns-at=AAEJ7tMQMYrAVRguSFLy9cAiJ_3qWbbMYyvGMu1ULt198gs3NcQ',
          {
            signal: signal,
          },
        );
        const responseData = JSON.parse(
          orderDataResponse.data.replace(/<!--[\s\S]*?-->/g, '').trim(),
        );
        setOrderModalData(responseData);
      } catch (err) {
        console.log(err.message);
      } finally {
        setModalLoading(false);
      }
    };
    fetchData();
    return () => {
      controller.abort();
    };
  }, [modalItem]);

  const columns = [
    {
      title: 'Image',
      dataIndex: 'item_image',
      key: 'item_image',
      render: (img, record) => (
        <img
          src={img ? img.replaceAll('&amp;', '&') : ''}
          alt={record.display_name}
          width={50}
          height={50}
        />
      ),
    },
    {
      title: 'Display Name',
      dataIndex: 'display_name',
      key: 'display_name',
      filter: true,
    },
    {
      title: 'UPC Code',
      dataIndex: 'upc_code',
      key: 'upc_code',
      filter: true,
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: 'Committed',
      dataIndex: 'quantity_committed',
      key: 'quantity_committed',
    },
    {
      title: 'Picked',
      dataIndex: 'quantity_picked',
      key: 'quantity_picked',
    },
    {
      title: 'Packed',
      dataIndex: 'quantity_packed',
      key: 'quantity_packed',
    },
    {
      title: 'Shipped',
      dataIndex: 'quantity_shipped',
      key: 'quantity_shipped',
    },

  ];

  return (
    <Modal
      open={!!modalItem}
      onCancel={closeModal}
      footer={null}
      title={modalLoading ? <Spin size="small" tip="Loading..." /> : <Title level={4}>{orderModalData?.customer} - {orderModalData?.sales_order} ({modalItem?.po_num})</Title>}
      width={900}
      destroyOnClose
      loading={modalLoading}
    >
      {modalLoading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" tip="Loading..." />
        </div>
      ) : orderModalData ? (
        <>
          <Table
            columns={columns}
            dataSource={orderModalData.items}
            rowKey={record => record.item_id}
            pagination={false}
            scroll={{ x: true }}
          />
        </>
      ) : null}
    </Modal>
  );
};

export default OrderDetailsModal;