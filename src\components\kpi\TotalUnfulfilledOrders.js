import React, { useState, useEffect } from 'react';
import { Box, Typography, Grid, Paper, CircularProgress, Alert } from '@mui/material';
import {
    ComposedChart, Line, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import { useFormatAxisTick } from '../../hooks/useFormatAxisTick';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import ChartExportWrapper from './ChartExportWrapper';

const TotalUnfulfilledOrders = () => {
    const [unfulfilledData, setUnfulfilledData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const formatAxisTick = useFormatAxisTick();

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const functions = getFunctions();
            const getUnfulfilledOrderRate = httpsCallable(functions, 'getUnfulfilledOrderRate');
            const getKPIGoalsForReport = httpsCallable(functions, 'getKPIGoalsForReport');

            const [unfulfilledResult, goalsResult] = await Promise.all([
                getUnfulfilledOrderRate(),
                getKPIGoalsForReport({ reportName: 'unfulfilled-orders' })
            ]);

            setUnfulfilledData(unfulfilledResult.data);

            if (goalsResult.data.success) {
                setKpiGoals(goalsResult.data.data);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            setError('Failed to fetch data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    const calculateUnfulfilledTrend = () => {
        if (!unfulfilledData?.monthlyData || unfulfilledData.monthlyData.length < 2) return 0;
        const lastTwo = unfulfilledData.monthlyData.slice(-2);
        return lastTwo[1].unfulfilledOrderRate - lastTwo[0].unfulfilledOrderRate;
    };

    // Calculate domains including the goal values
    const calculateDomains = () => {
        if (!unfulfilledData?.monthlyData) return { rateDomain: [0, 100], ordersDomain: [0, 1000] };

        const rateValues = unfulfilledData.monthlyData.map(item => item.unfulfilledOrderRate);
        const orderValues = unfulfilledData.monthlyData.map(item =>
            Math.max(item.totalOrders || 0, item.unfulfilledOrders || 0)
        );

        // Include goal values in rate domain calculation
        const goalValue = kpiGoals?.['Total Unfulfilled Orders']?.value;
        const maxRate = Math.max(...rateValues, goalValue ? parseFloat(goalValue) : 0);

        return {
            rateDomain: [0, Math.ceil(maxRate * 1.2)], // Add 20% padding for visibility
            ordersDomain: [0, Math.ceil(Math.max(...orderValues) * 1.1)]
        };
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                    <Typography variant="body2">{`Month-Year: ${label}`}</Typography>
                    <Typography variant="body2" color="#ff9800">
                        {`Unfulfilled Order Rate: ${data.unfulfilledOrderRate.toFixed(2)}%`}
                    </Typography>
                    <Typography variant="body2" color="#66bb6a">
                        {`Total Orders: ${formatAxisTick(data.totalOrders)}`}
                    </Typography>
                    <Typography variant="body2" color="#f44336">
                        {`Unfulfilled Orders: ${formatAxisTick(data.unfulfilledOrders)}`}
                    </Typography>
                </Paper>
            );
        }
        return null;
    };

    // Use the KPI reference lines hook
    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: kpiGoals?.['Total Unfulfilled Orders'],
        yAxisId: "left",
        styles: {
            stroke: "#ff9800",
            strokeDasharray: "3 3",
            label: {
                fill: "#ff9800",
                fontSize: 12,
                position: "right"
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Min",
            max: "Max"
        }
    });

    if (isLoading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Alert severity="error" sx={{ mt: 2 }}>
                {error}
            </Alert>
        );
    }

    if (!unfulfilledData || !kpiGoals) return null;

    const { rateDomain, ordersDomain } = calculateDomains();
    const unfulfilledConfig = kpiGoals['Total Unfulfilled Orders'];

    return (
        <ChartExportWrapper title="Total_Unfulfilled_Orders">
            <Box sx={{ p: { xs: 2, sm: 3 }, mb: 3 }}>
                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <ResponsiveContainer width="100%" height={400}>
                        <ComposedChart
                            data={unfulfilledData.monthlyData}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="monthYear" />
                            <YAxis
                                yAxisId="left"
                                domain={rateDomain}
                                label={{
                                    value: 'Unfulfilled Order Rate (%)',
                                    angle: -90,
                                    position: 'insideLeft',
                                    offset: -10,
                                    style: { textAnchor: 'middle' }
                                }}
                            />
                            <YAxis
                                yAxisId="right"
                                orientation="right"
                                domain={ordersDomain}
                                label={{
                                    value: 'Orders',
                                    angle: 90,
                                    position: 'outside',
                                    offset: 25,
                                    style: { textAnchor: 'middle' }
                                }}
                                tickFormatter={formatAxisTick}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            {goalReferenceLines}
                            <Bar yAxisId="right" dataKey="totalOrders" fill="#66bb6a" name="Total Orders" />
                            <Bar yAxisId="right" dataKey="unfulfilledOrders" fill="#f44336" name="Unfulfilled Orders" />
                            <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="unfulfilledOrderRate"
                                stroke="#ff9800"
                                strokeWidth={3}
                                dot={{ r: 4, fill: "#ff9800" }}
                                activeDot={{ r: 8 }}
                                name="Unfulfilled Order Rate (%)"
                            />
                        </ComposedChart>
                    </ResponsiveContainer>

                    <Grid container spacing={2} sx={{ mt: 2 }}>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Overall Unfulfilled Order Rate"
                                value={`${unfulfilledData.overallUnfulfilledOrderRate.toFixed(2)}%`}
                                bgColor="#fff8e1"
                                textColor="#ff9800"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Total Unfulfilled Orders"
                                value={unfulfilledData.totalUnfulfilledOrders.toLocaleString()}
                                bgColor="#fff0f0"
                                textColor="#f44336"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Total Orders"
                                value={unfulfilledData.totalOrders.toLocaleString()}
                                bgColor="#e8f5e9"
                                textColor="#4caf50"
                            />
                        </Grid>
                    </Grid>
                </Paper>

                <GoalStatusDisplay
                    currentValue={unfulfilledData.overallUnfulfilledOrderRate}
                    goalConfig={unfulfilledConfig}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculateUnfulfilledTrend()}
                    size="medium"
                    title="Unfulfilled Orders Performance"
                />
            </Box>
        </ChartExportWrapper>
    );
};

export default TotalUnfulfilledOrders;