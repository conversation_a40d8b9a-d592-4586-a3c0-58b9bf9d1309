const {onCall} = require("firebase-functions/v2/https");
const logger = require("firebase-functions/logger");
const {executeNSSuiteQLQuery} = require("../helpers/netsuite");

exports.getProductLifecycleScore = onCall(async (data, context) => {
  try {
    logger.info("Fetching product lifecycle data...");

    const query = `
            SELECT
              item.id AS internal_id,
              item.itemId AS item_id,
              item.fullname AS display_name,
              item.custitem24 AS launch_date,
              item.custitem25 AS end_date,
              item.custitem20 AS life_status,
              CUSTOMLIST2818.name AS product_division,  
              CUSTOMLIST2819.name AS product_category,  
              CUSTOMLIST2820.name AS product_family, 
              item.upccode AS upc_code,
              item.custitem_color AS variant_color,
              item.custitem2 AS product_specification,
              CUSTOMRECORD_PRODUCT_TYPE.name AS product_type,
              item.custitem29 AS front_loaded_launch_quantity
            FROM
              item
            LEFT JOIN
              CUSTOMRECORD_PRODUCT_TYPE
              ON item.custitem_product_type = CUSTOMRECORD_PRODUCT_TYPE.id
            LEFT JOIN
              CUSTOMLIST2818
              ON item.custitem59 = CUSTOMLIST2818.id 
            LEFT JOIN
              CUSTOMLIST2819
              ON item.custitem60 = CUSTOMLIST2819.id  
            LEFT JOIN
              CUSTOMLIST2820
              ON item.custitem61 = CUSTOMLIST2820.id 
            WHERE
              item.isinactive = 'F'
              AND item.upccode IS NOT NULL
              AND item.custitem20 IS NOT NULL
            ORDER BY
              item.id
        `;

    const result = await executeNSSuiteQLQuery(query);

    if (!result || !result.items || !Array.isArray(result.items)) {
      logger.warn("Empty or invalid response from SuiteQL query");
      return {error: "No data available"};
    }

    logger.info(`Fetched ${result.items.length} product records`);

    const processedData = processProductLifecycleData(result.items);

    logger.info("Product lifecycle data processed successfully");
    return processedData;
  } catch (error) {
    logger.error("Error fetching or processing product lifecycle data:", error);
    throw new Error(`Failed to calculate Product Lifecycle Score: ${error.message}`);
  }
});
/**
 * Process product lifecycle data to generate statistics
 * @param {Array} data - Array of product lifecycle data
 * @return {Object} - Processed product lifecycle statistics

 */
function processProductLifecycleData(data) {
  const lifecycleStats = {
    total: data.length,
    byStatus: {},
    byDivision: {},
    byCategory: {},
    byFamily: {},
    byType: {},
    bySpecification: {},
    statusTimeline: [],
    productMatrix: [],
  };

  const currentDate = new Date();

  data.forEach((item) => {
    const status = item.life_status || "Undefined";
    lifecycleStats.byStatus[status] = (lifecycleStats.byStatus[status] || 0) + 1;

    const division = item.product_division || "Undefined";
    if (!lifecycleStats.byDivision[division]) {
      lifecycleStats.byDivision[division] = {total: 0, byStatus: {}};
    }
    lifecycleStats.byDivision[division].total++;
    lifecycleStats.byDivision[division].byStatus[status] =
            (lifecycleStats.byDivision[division].byStatus[status] || 0) + 1;

    const category = item.product_category || "Undefined";
    if (!lifecycleStats.byCategory[category]) {
      lifecycleStats.byCategory[category] = {total: 0, byStatus: {}};
    }
    lifecycleStats.byCategory[category].total++;
    lifecycleStats.byCategory[category].byStatus[status] =
            (lifecycleStats.byCategory[category].byStatus[status] || 0) + 1;

    const family = item.product_family || "Undefined";
    if (!lifecycleStats.byFamily[family]) {
      lifecycleStats.byFamily[family] = {total: 0, byStatus: {}};
    }
    lifecycleStats.byFamily[family].total++;
    lifecycleStats.byFamily[family].byStatus[status] =
            (lifecycleStats.byFamily[family].byStatus[status] || 0) + 1;

    const type = item.product_type || "Undefined";
    if (!lifecycleStats.byType[type]) {
      lifecycleStats.byType[type] = {total: 0, byStatus: {}};
    }
    lifecycleStats.byType[type].total++;
    lifecycleStats.byType[type].byStatus[status] =
            (lifecycleStats.byType[type].byStatus[status] || 0) + 1;

    const spec = item.product_specification || "Undefined";
    if (!lifecycleStats.bySpecification[spec]) {
      lifecycleStats.bySpecification[spec] = {total: 0, byStatus: {}};
    }
    lifecycleStats.bySpecification[spec].total++;
    lifecycleStats.bySpecification[spec].byStatus[status] =
            (lifecycleStats.bySpecification[spec].byStatus[status] || 0) + 1;

    if (item.launch_date) {
      const launchDate = new Date(item.launch_date);
      const endDate = item.end_date ? new Date(item.end_date) : null;

      lifecycleStats.statusTimeline.push({
        id: item.internal_id,
        name: item.display_name,
        status: status,
        launchDate: launchDate.toISOString(),
        endDate: endDate ? endDate.toISOString() : null,
        daysActive: endDate ?
                    Math.floor((endDate - launchDate) / (1000 * 60 * 60 * 24)) :
                    Math.floor((currentDate - launchDate) / (1000 * 60 * 60 * 24)),
        type: type,
        division: division,
        category: category,
        family: family,
        specification: spec,
      });
    }

    lifecycleStats.productMatrix.push({
      id: item.internal_id,
      name: item.display_name,
      status: status,
      division: division,
      category: category,
      family: family,
      type: type,
      specification: spec,
      color: item.variant_color,
      upcCode: item.upc_code,
      launchQuantity: item.front_loaded_launch_quantity,
    });
  });

  const calculatePercentages = (statusCounts, total) => {
    const percentages = {};
    for (const [status, count] of Object.entries(statusCounts)) {
      percentages[status] = ((count / total) * 100).toFixed(2);
    }
    return percentages;
  };

  lifecycleStats.statusPercentages = calculatePercentages(lifecycleStats.byStatus, lifecycleStats.total);
  lifecycleStats.statusTimeline.sort((a, b) => new Date(a.launchDate) - new Date(b.launchDate));

  return lifecycleStats;
}

exports.getProductPerformanceMetrics = onCall(async (data, context) => {
  try {
    logger.info("Fetching product performance data with monthly breakdown...");

    const currentDate = new Date();
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(currentDate.getFullYear() - 1);

    const formattedCurrentDate = currentDate.toISOString().split("T")[0];
    const formattedOneYearAgo = oneYearAgo.toISOString().split("T")[0];

    const salesQuery = `
            SELECT 
                tl.item AS item_id,
                EXTRACT(YEAR FROM t.trandate) || '-' || 
                LPAD(EXTRACT(MONTH FROM t.trandate), 2, '0') AS month,
                item.custitem_product_type AS product_type_id,
                item.custitem_color AS variant_color_id,
                item.custitem59 AS division_id,
                item.custitem60 AS category_id,
                item.custitem61 AS family_id,
                item.custitem2 AS specification,
                item.custitem_shopify_sales_price AS sales_price,
                SUM(ABS(tl.quantity)) AS total_sales,
                SUM(ABS(tl.quantity * item.custitem_shopify_sales_price)) AS total_revenue
            FROM 
                transaction t
            JOIN 
                transactionline tl ON t.id = tl.transaction
            JOIN 
                item ON tl.item = item.id
            WHERE 
                t.type = 'CustInvc'
                AND t.trandate BETWEEN TO_DATE('${formattedOneYearAgo}', 'YYYY-MM-DD') 
                AND TO_DATE('${formattedCurrentDate}', 'YYYY-MM-DD')
            GROUP BY 
                tl.item,
                EXTRACT(YEAR FROM t.trandate),
                EXTRACT(MONTH FROM t.trandate),
                item.custitem_product_type,
                item.custitem_color,
                item.custitem59,
                item.custitem60,
                item.custitem61,
                item.custitem2,
                item.custitem_shopify_sales_price
            ORDER BY
                month ASC
        `;

    const productTypeQuery = `
            SELECT id, name FROM CUSTOMRECORD_PRODUCT_TYPE 
            WHERE isinactive = 'F'
        `;

    const colorQuery = `
            SELECT id, name FROM CUSTOMLIST562 
            WHERE isinactive = 'F'
        `;

    const divisionQuery = `
            SELECT id, name FROM CUSTOMLIST2818 
            WHERE isinactive = 'F'
        `;

    const categoryQuery = `
            SELECT id, name FROM CUSTOMLIST2819 
            WHERE isinactive = 'F'
        `;

    const familyQuery = `
            SELECT id, name FROM CUSTOMLIST2820 
            WHERE isinactive = 'F'
        `;

    const [salesResult, productTypeResult, colorResult, divisionResult, categoryResult, familyResult] =
            await Promise.all([
              executeNSSuiteQLQuery(salesQuery),
              executeNSSuiteQLQuery(productTypeQuery),
              executeNSSuiteQLQuery(colorQuery),
              executeNSSuiteQLQuery(divisionQuery),
              executeNSSuiteQLQuery(categoryQuery),
              executeNSSuiteQLQuery(familyQuery),
            ]);

    const createLookup = (items) => items.reduce((map, item) => {
      map[item.id] = item.name;
      return map;
    }, {});

    const lookups = {
      productType: createLookup(productTypeResult.items),
      color: createLookup(colorResult.items),
      division: createLookup(divisionResult.items),
      category: createLookup(categoryResult.items),
      family: createLookup(familyResult.items),
    };

    const processedData = {
      byProductType: {},
      byColor: {},
      byDivision: {},
      byCategory: {},
      byFamily: {},
      bySpecification: {},
      totalStats: {
        totalProducts: new Set(),
        totalSales: 0,
        totalRevenue: 0,
      },
    };

    const updateGroupData = (group, key, sale) => {
      if (!group[key]) {
        group[key] = {
          totalSales: 0,
          totalRevenue: 0,
          monthlyData: {},
          items: new Set(),
        };
      }

      const sales = parseInt(sale.total_sales) || 0;
      const revenue = parseFloat(sale.total_revenue) || 0;

      group[key].totalSales += sales;
      group[key].totalRevenue += revenue;
      group[key].items.add(sale.item_id);

      if (!group[key].monthlyData[sale.month]) {
        group[key].monthlyData[sale.month] = {
          sales: 0,
          revenue: 0,
        };
      }
      group[key].monthlyData[sale.month].sales += sales;
      group[key].monthlyData[sale.month].revenue += revenue;
    };

    salesResult.items.forEach((sale) => {
      const groupings = {
        productType: lookups.productType[sale.product_type_id] || "Unknown Type",
        color: lookups.color[sale.variant_color_id] || "Unknown Color",
        division: lookups.division[sale.division_id] || "Undefined",
        category: lookups.category[sale.category_id] || "Undefined",
        family: lookups.family[sale.family_id] || "Undefined",
        specification: sale.specification || "Undefined",
      };

      updateGroupData(processedData.byProductType, groupings.productType, sale);
      updateGroupData(processedData.byColor, groupings.color, sale);
      updateGroupData(processedData.byDivision, groupings.division, sale);
      updateGroupData(processedData.byCategory, groupings.category, sale);
      updateGroupData(processedData.byFamily, groupings.family, sale);
      updateGroupData(processedData.bySpecification, groupings.specification, sale);

      processedData.totalStats.totalProducts.add(sale.item_id);
      processedData.totalStats.totalSales += parseInt(sale.total_sales) || 0;
      processedData.totalStats.totalRevenue += parseFloat(sale.total_revenue) || 0;
    });

    const finalizeGroupData = (group) => {
      Object.keys(group).forEach((key) => {
        group[key].monthlyData = Object.entries(group[key].monthlyData)
            .map(([month, data]) => ({
              month,
              ...data,
            }))
            .sort((a, b) => a.month.localeCompare(b.month));
        group[key].items = group[key].items.size;
      });
    };

    Object.keys(processedData).forEach((key) => {
      if (key !== "totalStats") {
        finalizeGroupData(processedData[key]);
      }
    });

    processedData.totalStats.totalProducts = processedData.totalStats.totalProducts.size;

    logger.info("Product performance data processed successfully");
    return processedData;
  } catch (error) {
    logger.error("Error processing product performance data:", error);
    throw new Error(`Failed to calculate Product Performance Metrics: ${error.message}`);
  }
});

exports.getTimelineHitScore = onCall(async (data, context) => {
  try {
    logger.info("Calculating Timeline Hit Score...");

    const gracePeriodDays = (data && data.data && data.data.gracePeriodDays) ? data.data.gracePeriodDays : 30;
    logger.info(`Using grace period of ${gracePeriodDays} days`);

    const query = `
            WITH FirstSales AS (
                SELECT 
                    tl.item,
                    MIN(t.trandate) as first_sale_date
                FROM 
                    transaction t
                JOIN 
                    transactionline tl ON t.id = tl.transaction
                WHERE 
                    t.type = 'CustInvc'
                GROUP BY 
                    tl.item
            )
       SELECT
                item.id AS internal_id,
                item.itemId AS item_id,
                item.fullname AS display_name,
                item.custitem24 AS launch_date,
                item.custitem20 AS life_status,
                CUSTOMLIST2818.name AS product_division,
                CUSTOMLIST2819.name AS product_category,
                CUSTOMLIST2820.name AS product_family,
                CUSTOMRECORD_PRODUCT_TYPE.name AS product_type,
                item.custitem2 AS product_specification,
                FirstSales.first_sale_date
            FROM
                item
            LEFT JOIN
                FirstSales ON item.id = FirstSales.item
            LEFT JOIN
                CUSTOMRECORD_PRODUCT_TYPE ON item.custitem_product_type = CUSTOMRECORD_PRODUCT_TYPE.id
            LEFT JOIN
                CUSTOMLIST2818 ON item.custitem59 = CUSTOMLIST2818.id
            LEFT JOIN
                CUSTOMLIST2819 ON item.custitem60 = CUSTOMLIST2819.id
            LEFT JOIN
                CUSTOMLIST2820 ON item.custitem61 = CUSTOMLIST2820.id
            WHERE
                item.isinactive = 'F'
                AND item.custitem24 IS NOT NULL
                AND item.custitem20 = 'Active'
                AND item.custitem24 >= ADD_MONTHS(CURRENT_DATE, -12)
            ORDER BY
                item.custitem24 DESC
        `;

    const result = await executeNSSuiteQLQuery(query);

    if (!result || !result.items || !Array.isArray(result.items)) {
      logger.warn("Empty or invalid response from SuiteQL query");
      return {error: "No data available"};
    }

    const timelineStats = processTimelineData(result.items, gracePeriodDays);

    logger.info("Timeline hit score calculated successfully");
    return timelineStats;
  } catch (error) {
    logger.error("Error calculating timeline hit score:", error);
    throw new Error(`Failed to calculate Timeline Hit Score: ${error.message}`);
  }
});
/**
 * Process timeline data to generate statistics
 * @param {Array} items - Array of timeline data
 * @param {number} gracePeriodDays - Grace period in days for on-time projects
 * @return {Object} - Processed timeline statistics
 */
function processTimelineData(items, gracePeriodDays) {
  const stats = {
    overall: {
      totalProjects: 0,
      onTimeProjects: 0,
      score: 0,
      avgDaysToFirstSale: 0,
      totalDaysToFirstSale: 0,
    },
    byDivision: {},
    byCategory: {},
    byFamily: {},
    byType: {},
    bySpecification: {},
  };

  const initializeGroupStats = () => ({
    totalProjects: 0,
    onTimeProjects: 0,
    score: 0,
    avgDaysToFirstSale: 0,
    totalDaysToFirstSale: 0,
    details: [],
  });

  items.forEach((item) => {
    const launchDate = new Date(item.launch_date);
    const firstSaleDate = item.first_sale_date ? new Date(item.first_sale_date) : null;

    if (!launchDate || !firstSaleDate) return;

    const daysToFirstSale = Math.floor((firstSaleDate - launchDate) / (1000 * 60 * 60 * 24));
    const isOnTime = daysToFirstSale <= gracePeriodDays;

    stats.overall.totalProjects++;
    stats.overall.totalDaysToFirstSale += daysToFirstSale;
    if (isOnTime) stats.overall.onTimeProjects++;

    const updateGroupStats = (collection, key, itemDetails) => {
      if (!collection[key]) {
        collection[key] = initializeGroupStats();
      }

      const group = collection[key];
      group.totalProjects++;
      group.totalDaysToFirstSale += daysToFirstSale;
      if (isOnTime) group.onTimeProjects++;

      group.details.push({
        id: itemDetails.internal_id,
        name: itemDetails.display_name,
        launchDate: launchDate.toISOString(),
        firstSaleDate: firstSaleDate.toISOString(),
        daysToFirstSale,
        isOnTime,
      });

      group.score = (group.onTimeProjects / group.totalProjects) * 100;
      group.avgDaysToFirstSale = group.totalDaysToFirstSale / group.totalProjects;
    };

    updateGroupStats(stats.byDivision, item.product_division || "Undefined", item);
    updateGroupStats(stats.byCategory, item.product_category || "Undefined", item);
    updateGroupStats(stats.byFamily, item.product_family || "Undefined", item);
    updateGroupStats(stats.byType, item.product_type || "Undefined", item);
    updateGroupStats(stats.bySpecification, item.product_specification || "Undefined", item);
  });

  if (stats.overall.totalProjects > 0) {
    stats.overall.score = (stats.overall.onTimeProjects / stats.overall.totalProjects) * 100;
    stats.overall.avgDaysToFirstSale = stats.overall.totalDaysToFirstSale / stats.overall.totalProjects;
  }

  const sortGroups = (groups) => {
    return Object.entries(groups)
        .sort((a, b) => b[1].score - a[1].score)
        .reduce((acc, [key, value]) => {
          acc[key] = value;
          return acc;
        }, {});
  };

  stats.byDivision = sortGroups(stats.byDivision);
  stats.byCategory = sortGroups(stats.byCategory);
  stats.byFamily = sortGroups(stats.byFamily);
  stats.byType = sortGroups(stats.byType);
  stats.bySpecification = sortGroups(stats.bySpecification);

  stats.metadata = {
    gracePeriodDays,
    generatedAt: new Date().toISOString(),
    totalRecordsProcessed: items.length,
    validRecordsProcessed: stats.overall.totalProjects,
  };

  return stats;
}
