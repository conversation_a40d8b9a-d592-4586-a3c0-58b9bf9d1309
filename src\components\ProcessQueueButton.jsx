import React from 'react';
import { <PERSON><PERSON>, Badge } from 'antd';
import {
  UnorderedListOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { TASK_STATUS } from './ProcessQueue';

const ProcessQueueButton = ({
  tasks = [],
  onClick,
  visible = true,
  connectionStatus = 'connected'
}) => {
  if (!visible) return null;

  // Enhanced task counts with more statuses
  const activeTasks = tasks.filter(t =>
    t.status === TASK_STATUS.QUEUED ||
    t.status === TASK_STATUS.PROCESSING ||
    t.status === TASK_STATUS.WAITING_CONFIRMATION ||
    t.status === TASK_STATUS.RETRYING
  );
  const failedTasks = tasks.filter(t =>
    t.status === TASK_STATUS.FAILED ||
    t.status === TASK_STATUS.TIMEOUT ||
    t.status === TASK_STATUS.CANCELLED
  );
  const completedTasks = tasks.filter(t => t.status === TASK_STATUS.COMPLETED);

  const totalTasks = tasks.length;
  const activeCount = activeTasks.length;
  const failedCount = failedTasks.length;
  const completedCount = completedTasks.length;

  // Enhanced button state and appearance with connection status
  let buttonType = 'default';
  let buttonIcon = <UnorderedListOutlined />;
  let badgeColor = '#d9d9d9';

  // Connection status affects appearance
  if (connectionStatus === 'error') {
    buttonType = 'danger';
    buttonIcon = <ExclamationCircleOutlined />;
    badgeColor = '#ff4d4f';
  } else if (connectionStatus === 'reconnecting') {
    buttonType = 'default';
    buttonIcon = <LoadingOutlined spin />;
    badgeColor = '#fa8c16';
  } else if (activeCount > 0) {
    buttonType = 'primary';
    buttonIcon = <LoadingOutlined spin />;
    badgeColor = '#1890ff';
  } else if (failedCount > 0) {
    buttonType = 'danger';
    buttonIcon = <ExclamationCircleOutlined />;
    badgeColor = '#ff4d4f';
  } else if (completedCount > 0) {
    buttonType = 'default';
    buttonIcon = <CheckCircleOutlined />;
    badgeColor = '#52c41a';
  }

  // Show badge only if there are tasks
  const showBadge = totalTasks > 0;
  const badgeCount = activeCount > 0 ? activeCount : failedCount > 0 ? failedCount : completedCount;

  return (
    <div style={{ position: 'relative', display: 'inline-block' }}>
      {showBadge ? (
        <Badge
          count={badgeCount}
          size="small"
          style={{
            backgroundColor: badgeColor,
            fontSize: '10px',
            minWidth: '16px',
            height: '16px',
            lineHeight: '16px',
            borderRadius: '8px'
          }}
          offset={[-8, 8]}
        >
          <Button
            type={buttonType}
            icon={buttonIcon}
            onClick={onClick}
            style={{
              borderRadius: '8px',
              height: '36px',
              padding: '6px 16px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              transition: 'all 0.2s ease',
              fontWeight: 500,
              fontSize: '13px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              ...(activeCount > 0 && {
                boxShadow: '0 0 12px rgba(24, 144, 255, 0.4)',
                animation: 'pulse 2s infinite'
              }),
              ...(failedCount > 0 && {
                boxShadow: '0 0 12px rgba(255, 77, 79, 0.4)'
              }),
              ...(connectionStatus === 'error' && {
                boxShadow: '0 0 12px rgba(255, 77, 79, 0.4)'
              })
            }}
          >
            Process Queue
          </Button>
        </Badge>
      ) : (
        <Button
          type={buttonType}
          icon={buttonIcon}
          onClick={onClick}
          style={{
            borderRadius: '6px',
            height: '32px',
            padding: '4px 12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '6px',
            opacity: 0.6,
            transition: 'all 0.2s ease'
          }}
        >
          Process Queue
        </Button>
      )}
    </div>
  );
};

export default ProcessQueueButton;
