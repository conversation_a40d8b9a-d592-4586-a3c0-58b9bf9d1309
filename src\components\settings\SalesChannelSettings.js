import React, {useState, useEffect} from "react";
import {Modal, Form, Input, Button, Select, message, Table, Checkbox} from "antd";
import { api } from "../../pages/firebase";
import { render } from "@testing-library/react";

const SalesChannelSettings = () => {
  const [channels, setChannels] = useState([]);
  const [loading, setLoading] = useState(true);
  const fetchChannels = async () => {
    setLoading(true);
    const {data} = await api.makeSuiteQlQuery({ query: 'select s.id channelId, s.orderpriority, class.name class, s.name channelName, s.orderpriority, s.isinactive inactive  from saleschannel s left join classification class on custrecord_class = class.id', paginate: true});
    console.log(data);
    setChannels(data);
    setLoading(false);
  };
  useEffect(() => {
    fetchChannels();
  }, []);
  const columns = [
    {
      title: "Edit",
      dataIndex: "edit",
      key: "edit",
      render: (text, rec) => (<Button onClick={() => window.open(`https://6810379.app.netsuite.com/app/accounting/inventory/allocation/channel/saleschannel.nl?id=${rec.channelid}&e=T`, '_blank')}>Edit</Button>)
    },
    {
      title: "Channel Type",
      dataIndex: "class",
      key: "class",
    },
    {
      title: "Channel",
      dataIndex: "channelname",
      key: "channelname",
    },
    {
      title: "Order Priority",
      dataIndex: "orderpriority",
      key: "orderpriority",
    },
    {
      title: "Inactive",
      dataIndex: "inactive",
      key: "inactive",
      render: (text, rec) => (<Checkbox 
        disabled={true}
        checked={text=='T'} 
        onChange={(e)=>updateSalesChannel(e, rec) } />)
    },
    {
      title: "Channel ID",
      dataIndex: "channelid",
      key: "channelid",
      hidden: true
    },
  ];
  if (loading) return (
    <div>Loading...</div>
  );
  return (
    <>
    <Button onClick={fetchChannels}>Refresh</Button>
    <Button onClick={() =>{
      window.open('https://6810379.app.netsuite.com/app/accounting/inventory/allocation/channel/saleschannels.nl?whence=', '_blank');
    }}>Add Channel</Button>
    <Table 
      columns={columns} 
      dataSource={channels.sort((a, b) => a.orderpriority - b.orderpriority)} 
      pagination={false} 
      // scroll={{ y: 240 }}
    />
    </>
  );
};
export default SalesChannelSettings;