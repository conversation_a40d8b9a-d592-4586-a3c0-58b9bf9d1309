import React from 'react';
import { Modal } from 'antd';
import HistoricalSalesContent from './HistoricalSalesContent';

const HistoricalSalesModal = ({
  visible,
  onClose,
  initialSelection = { upcs: [], forecastNodes: [] },
  rowData = []
}) => {
  return (
    <Modal
      title="Historical Sales Analysis"
      open={visible}
      onCancel={onClose}
      footer={null}
      width="95%"
      style={{ top: 20, height: 'calc(100vh - 40px)', maxHeight: 'calc(100vh - 40px)' }}
      bodyStyle={{ height: 'calc(100vh - 120px)', overflow: 'auto' }}
      destroyOnClose
    >
      <HistoricalSalesContent 
        initialSelection={initialSelection}
        rowData={rowData}
        isModal={true}
        autoSearch={true}
      />
    </Modal>
  );
};

export default HistoricalSalesModal;
