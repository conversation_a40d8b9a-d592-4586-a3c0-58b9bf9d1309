import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Button, Popconfirm, message, Typography, Table, Spin, Checkbox } from 'antd';
import { collection, doc, deleteDoc, onSnapshot, addDoc, setDoc } from 'firebase/firestore';
import { db, api } from '../pages/firebase';
import { SYSTEMS } from '../constants';
import { TableOutlined, SearchOutlined, PlusOutlined } from '@ant-design/icons';
import DatasetModal from './DatasetModal';
const { Text } = Typography;
const DatasetsTab = () => {
  const [datasets, setDatasets] = useState([]);
  const [showDatasetModal, setShowDatasetModal] = useState(false);
  const [selectedDataset, setSelectedDataset] = useState(null);
  const [connectors, setConnectors] = useState([]);
  const [refreshingDatasets, setRefreshingDatasets] = useState({});

  useEffect(() => {
    fetchDatasets();
    fetchConnectors();
  }, []);
  const fetchDatasets = async () => {
    const datasetsRef = collection(db, 'datasets');
    const unsubscribe = onSnapshot(datasetsRef, (datasetsSnapshot) => {
      const datasetsData = datasetsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data()
      }));
      setDatasets(datasetsData);
    }, (error) => {
      console.error("Error fetching datasets:", error);
      message.error("Failed to load datasets");
    });
    return unsubscribe;
  };
  const fetchConnectors = async () => {
    const connectorsRef = collection(db, 'connectors');
    const unsubscribe = onSnapshot(connectorsRef, (connectorsSnapshot) => {
      const connectorsData = connectorsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data()
      }));
      setConnectors(connectorsData);
    });
    return unsubscribe;
  };
  

  const handleSaveDataset = async (values) => {
    let paramValues = values;
    for (const key in values) {
      if (values[key] === undefined) {
          delete paramValues[key];
      }
    }
    setShowDatasetModal(false);
    if (selectedDataset?.id) {
      await setDoc(doc(db, 'datasets', selectedDataset.id), paramValues, { merge: true });
      message.success("Dataset updated successfully");
    } else {
      await addDoc(collection(db, 'datasets'), paramValues);
      message.success("Dataset added successfully");
    }
    setSelectedDataset(null);
    // fetchGoldenDatasets();
  };

  const handleRefreshDataset = async (dataset) => {
    try {
      // Set refreshing state for this dataset
      setRefreshingDatasets(prev => ({ ...prev, [dataset.id]: true }));
      // Call the updateDatasetOnCall function for all datasets
      await api.updateDatasetOnCall({ datasetId: dataset.id });
      message.success("Data refresh initiated");
    } catch (error) {
      console.error("Error refreshing dataset:", error);
      message.error(`Failed to refresh dataset: ${error.message}`);
    } finally {
      // Clear refreshing state after a delay to allow for status updates
      setTimeout(() => {
        setRefreshingDatasets(prev => ({ ...prev, [dataset.id]: false }));
      }, 5000);
    }
  };
  const handleDeleteDataset = async (dataset) => {
    await deleteDoc(doc(db, 'datasets', dataset.id.toString()));
    message.success("Dataset deleted successfully");
  };


  return (
    <div style={{ padding: '16px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
        <Typography.Title level={4}>Datasets</Typography.Title>
        <Button type="primary" icon={<PlusOutlined />} onClick={() => setShowDatasetModal(true)}>Add Dataset</Button>
      </div>
      <Table
        dataSource={datasets}
        columns={[
          {
            title: 'Connector',
            dataIndex: 'connectorId',
            key: 'connectorId',
            sorter: (a, b) => a.connectorId.localeCompare(b.connectorId),
            render: (_, record) => (
              <>
                <Text>{connectors.find((connector) => connector.id === record.connectorId)?.name}</Text>
                {(record.isRefreshing || refreshingDatasets[record.id]) && <Spin />}
              </>
            ),
          },
          {
            title: 'Operation',
            dataIndex: 'operationId',
            key: 'operationId',
            sorter: (a, b) => a.operationId.localeCompare(b.operationId),
          },
          {
            title: "Project ID",
            dataIndex: "projectId",
            key: "projectId",
            sorter: (a, b)=>a.projectId.localeCompare(b.projectId),
          },
          {
            title: "Dataset ID",
            dataIndex: "datasetId",
            key: "datasetId",
            sorter: (a, b)=>a.datasetId.localeCompare(b.datasetId),
          },
          {
            title: "Table ID",
            dataIndex: "tableId",
            key: "tableId",
            sorter: (a, b)=>a.tableId.localeCompare(b.tableId),
          },
          {
            title: "Update Type",
            dataIndex: "updateType",
            key: "updateType",
            sorter: (a, b) => a.updateType.localeCompare(b.updateType),
          },
          {
            title: "Key",
            dataIndex: "key",
            key: "key",
            sorter: (a, b) => a.key.localeCompare(b.key),
          },
          {
            title: "Is Active",
            dataIndex: "isActive",
            key: "isActive",
            sorter: (a, b) => a.isActive.localeCompare(b.isActive),
            render: (_, record) => (
              <Checkbox checked={record.isActive} onChange={(e) => {
                setDoc(doc(db, 'datasets', record.id.toString()), { isActive: e.target.checked }, { merge: true });
              }} />
            ),
          },
          {
            title: "Last Refreshed",
            dataIndex: "lastRefreshed",
            key: "lastRefreshed",
            render: (_, record) => (record.lastRefreshed ? new Date(record.lastRefreshed?.seconds*1000).toLocaleString() : ''),
            sorter: (a, b) => new Date(b.lastRefreshed?.seconds*1000) - new Date(a.lastRefreshed?.seconds*1000),
          },
          {
            title: 'Actions',
            key: 'actions',
            render: (_, record) => (
              <div>
                <Button type="link" onClick={() => {
                  setSelectedDataset(record);
                  setShowDatasetModal(true);
                }}>Edit</Button>
                <Popconfirm
                  title="Are you sure you want to refresh this table?"
                  okText="Yes"
                  cancelText="No"
                  onConfirm={() => handleRefreshDataset(record)}
                >
                  <Button type="link" loading={refreshingDatasets[record.id]}>Refresh</Button>
                </Popconfirm>
                <Popconfirm
                  title="Are you sure you want to delete this table?"
                  okText="Yes"
                  cancelText="No"
                  onConfirm={() => handleDeleteDataset(record)}
                >
                  <Button type="link" loading={refreshingDatasets[record.id]}>Delete</Button>
                </Popconfirm>
                {((window.location.hostname === 'localhost')) ?
                    <Button type="link" onClick={() => {
                      const payload = prompt('Enter Payload:');
                      const parsedPayload = JSON.parse(payload);
                      console.log(parsedPayload);

                      api.netsuiteTaskOnCall(parsedPayload);
                    }}>Manual Run</Button> : ''
                }
              </div>
            ),
          },
        ]}
        pagination={{ pageSize: 10 }}
        rowKey="id"
      />
      <DatasetModal visible={showDatasetModal} onCancel={() => {
        setShowDatasetModal(false);
        setSelectedDataset(null);
      }} dataset={selectedDataset} onSave={handleSaveDataset} />
    </div>
  );
};

export default DatasetsTab;