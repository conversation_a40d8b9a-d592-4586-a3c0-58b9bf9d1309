import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Form, 
  Select, 
  Checkbox, 
  Card, 
  Row, 
  Col, 
  Tag, 
  Typography, 
  Space, 
  Button, 
  Divider,
  Tooltip,
  Alert
} from 'antd';
import { 
  UserOutlined, 
  LockOutlined, 
  CheckCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;

// Permission categories (same as in Settings.js)
const PERMISSION_CATEGORIES = {
  inventory: {
    label: 'Inventory Management',
    color: 'blue',
    permissions: [
      { technicalName: 'read:inventoryreport', label: 'View Inventory Report', description: 'Access to inventory reports' },
      { technicalName: 'read:items', label: 'View Items', description: 'Access to items data' },
      { technicalName: 'read:inventoryExceptions', label: 'View Inventory Exceptions', description: 'Access to inventory exceptions' },
    ]
  },
  products: {
    label: 'Product Management',
    color: 'green',
    permissions: [
      { technicalName: 'read:products', label: 'View Products', description: 'Access to product data' },
      { technicalName: 'edit:products', label: 'Edit Products', description: 'Modify product information' },
      { technicalName: 'read:variants', label: 'View Variants', description: 'Access to variant data' },
      { technicalName: 'edit:variants', label: 'Edit Variants', description: 'Modify variant information' },
    ]
  },
  orders: {
    label: 'Order Management',
    color: 'orange',
    permissions: [
      { technicalName: 'read:wholesale', label: 'View Wholesale', description: 'Access to wholesale orders' },
      { technicalName: 'read:shippingExceptions', label: 'View Shipping Exceptions', description: 'Access to shipping exceptions' },
      { technicalName: 'edit:shippingExceptions', label: 'Edit Shipping Exceptions', description: 'Modify shipping exceptions' },
      { technicalName: 'read:orderAllocation', label: 'View Order Allocation', description: 'Access to order allocation' },
    ]
  },
  forecasting: {
    label: 'Forecasting',
    color: 'purple',
    permissions: [
      { technicalName: 'read:forecastBeta', label: 'View Forecast Beta', description: 'Access to forecast beta features' },
    ]
  },
  analytics: {
    label: 'Analytics & Reports',
    color: 'cyan',
    permissions: [
      { technicalName: 'read:dashboard', label: 'View KPI Dashboard', description: 'Access to KPI dashboard' },
      { technicalName: 'read:data', label: 'View Data', description: 'Access to data management' },
      { technicalName: 'read:upload', label: 'Upload Data', description: 'Upload data files' },
      { technicalName: 'read:utilities', label: 'Access Utilities', description: 'Access to utility tools' },
      { technicalName: 'read:historicalSales', label: 'View Historical Sales', description: 'Access to historical sales' },
    ]
  },
  system: {
    label: 'System Administration',
    color: 'red',
    permissions: [
      { technicalName: 'read:settings', label: 'View Settings', description: 'Access to system settings' },
    ]
  }
};

// Predefined roles (same as in Settings.js)
const PREDEFINED_ROLES = {
  admin: {
    label: 'Administrator',
    description: 'Full access to all features and settings',
    color: 'red',
    permissions: Object.values(PERMISSION_CATEGORIES).flatMap(cat => cat.permissions.map(p => p.technicalName))
  },
  operations_manager: {
    label: 'Operations Manager',
    description: 'Access to inventory, orders, and operational data',
    color: 'blue',
    permissions: [
      ...PERMISSION_CATEGORIES.inventory.permissions.map(p => p.technicalName),
      ...PERMISSION_CATEGORIES.orders.permissions.map(p => p.technicalName),
      ...PERMISSION_CATEGORIES.forecasting.permissions.map(p => p.technicalName),
      'read:dashboard'
    ]
  },
  marketing: {
    label: 'Marketing',
    description: 'Access to products, variants, and marketing data',
    color: 'green',
    permissions: [
      ...PERMISSION_CATEGORIES.products.permissions.map(p => p.technicalName),
      'read:dashboard',
      'read:forecastBeta'
    ]
  },
  data_analyst: {
    label: 'Data Analyst',
    description: 'Access to reports, data, and analytics',
    color: 'cyan',
    permissions: [
      ...PERMISSION_CATEGORIES.analytics.permissions.map(p => p.technicalName),
      ...PERMISSION_CATEGORIES.inventory.permissions.map(p => p.technicalName),
      'read:forecastBeta'
    ]
  },
  viewer: {
    label: 'Viewer',
    description: 'Read-only access to assigned areas',
    color: 'default',
    permissions: [
      'read:inventoryreport',
      'read:items',
      'read:products',
      'read:variants',
      'read:dashboard'
    ]
  }
};

const UserEditModal = ({ 
  visible, 
  user, 
  onCancel, 
  onSave, 
  loading = false 
}) => {
  const [form] = Form.useForm();
  const [selectedRole, setSelectedRole] = useState(user?.role || '');
  const [customPermissions, setCustomPermissions] = useState({});
  const [showCustomPermissions, setShowCustomPermissions] = useState(false);

  useEffect(() => {
    if (user) {
      setSelectedRole(user.role || '');
      
      // Initialize custom permissions from user's current permissions
      const currentPermissions = {};
      if (user.userPermissions) {
        user.userPermissions.forEach(permission => {
          currentPermissions[permission.technicalName] = permission.hasAccess;
        });
      }
      setCustomPermissions(currentPermissions);
      
      // Check if user has custom permissions (different from their role)
      const rolePermissions = PREDEFINED_ROLES[user.role]?.permissions || [];
      const hasCustomPermissions = Object.values(PERMISSION_CATEGORIES)
        .flatMap(cat => cat.permissions)
        .some(permission => {
          const hasPermission = currentPermissions[permission.technicalName];
          const shouldHavePermission = rolePermissions.includes(permission.technicalName);
          return hasPermission !== shouldHavePermission;
        });
      
      setShowCustomPermissions(hasCustomPermissions);
      
      form.setFieldsValue({
        email: user.email,
        role: user.role || '',
        customPermissions: currentPermissions
      });
    }
  }, [user, form]);

  const handleRoleChange = (role) => {
    setSelectedRole(role);
    
    if (role && role !== 'custom') {
      const rolePermissions = PREDEFINED_ROLES[role]?.permissions || [];
      const newPermissions = {};
      
      Object.values(PERMISSION_CATEGORIES)
        .flatMap(cat => cat.permissions)
        .forEach(permission => {
          newPermissions[permission.technicalName] = rolePermissions.includes(permission.technicalName);
        });
      
      setCustomPermissions(newPermissions);
      form.setFieldsValue({ customPermissions: newPermissions });
    }
  };

  const handlePermissionChange = (permissionName, checked) => {
    const newPermissions = { ...customPermissions, [permissionName]: checked };
    setCustomPermissions(newPermissions);
    form.setFieldsValue({ customPermissions: newPermissions });
  };

  const handleCategoryPermissionChange = (categoryKey, checked) => {
    const category = PERMISSION_CATEGORIES[categoryKey];
    const newPermissions = { ...customPermissions };
    
    category.permissions.forEach(permission => {
      newPermissions[permission.technicalName] = checked;
    });
    
    setCustomPermissions(newPermissions);
    form.setFieldsValue({ customPermissions: newPermissions });
  };

  const getCategoryPermissionStatus = (categoryKey) => {
    const category = PERMISSION_CATEGORIES[categoryKey];
    const categoryPermissions = category.permissions.map(p => p.technicalName);
    const checkedPermissions = categoryPermissions.filter(p => customPermissions[p]);
    
    if (checkedPermissions.length === 0) return 'none';
    if (checkedPermissions.length === categoryPermissions.length) return 'all';
    return 'partial';
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const { email, role } = values;
      
      // Convert custom permissions to the format expected by the system
      const userPermissions = Object.values(PERMISSION_CATEGORIES)
        .flatMap(cat => cat.permissions)
        .map(permission => ({
          ...permission,
          hasAccess: customPermissions[permission.technicalName] || false
        }));

      const updatedUser = {
        ...user,
        email,
        role,
        userPermissions,
        updatedAt: new Date().toISOString()
      };

      await onSave(updatedUser);
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  const getPermissionStatus = (permissionName) => {
    const hasPermission = customPermissions[permissionName];
    const rolePermissions = PREDEFINED_ROLES[selectedRole]?.permissions || [];
    const shouldHavePermission = rolePermissions.includes(permissionName);
    
    if (hasPermission === shouldHavePermission) {
      return 'default'; // Matches role
    } else if (hasPermission && !shouldHavePermission) {
      return 'green'; // Custom granted
    } else {
      return 'red'; // Custom denied
    }
  };

  const renderPermissionCard = (categoryKey, category) => {
    const categoryStatus = getCategoryPermissionStatus(categoryKey);
    
    return (
      <Card 
        key={categoryKey}
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
            <Space>
              <Checkbox
                checked={categoryStatus === 'all'}
                indeterminate={categoryStatus === 'partial'}
                onChange={(e) => handleCategoryPermissionChange(categoryKey, e.target.checked)}
                style={{ marginRight: 8 }}
              />
              <Tag color={category.color}>{category.label}</Tag>
              <Text type="secondary">({category.permissions.length} permissions)</Text>
            </Space>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {categoryStatus === 'all' ? 'All enabled' : 
               categoryStatus === 'partial' ? 'Partially enabled' : 'All disabled'}
            </Text>
          </div>
        }
        size="small"
        style={{ marginBottom: 16 }}
      >
      <Row gutter={[16, 8]}>
        {category.permissions.map(permission => (
          <Col span={24} key={permission.technicalName}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div style={{ flex: 1 }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Checkbox
                    checked={customPermissions[permission.technicalName] || false}
                    onChange={(e) => handlePermissionChange(permission.technicalName, e.target.checked)}
                  />
                  <Text strong>{permission.label}</Text>
                  <Tag 
                    color={getPermissionStatus(permission.technicalName)}
                    size="small"
                  >
                    {getPermissionStatus(permission.technicalName) === 'default' ? 'Role' : 
                     getPermissionStatus(permission.technicalName) === 'green' ? 'Custom +' : 'Custom -'}
                  </Tag>
                </div>
                <Text type="secondary" style={{ marginLeft: 24, fontSize: '12px' }}>
                  {permission.description}
                </Text>
              </div>
            </div>
          </Col>
        ))}
      </Row>
    </Card>
    );
  };

  return (
    <Modal
      title={
        <Space>
          <UserOutlined />
          Edit User: {user?.email}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      onOk={handleSave}
      confirmLoading={loading}
      width={800}
      okText="Save Changes"
      cancelText="Cancel"
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          email: user?.email,
          role: user?.role || '',
          customPermissions: {}
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="email"
              label="Email Address"
              rules={[
                { required: true, message: 'Email is required' },
                { type: 'email', message: 'Please enter a valid email' }
              ]}
            >
              <Select disabled>
                <Option value={user?.email}>{user?.email}</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="role"
              label="Role"
              rules={[{ required: true, message: 'Please select a role' }]}
            >
              <Select 
                placeholder="Select a role"
                onChange={handleRoleChange}
                value={selectedRole}
              >
                {Object.entries(PREDEFINED_ROLES).map(([key, role]) => (
                  <Option key={key} value={key}>
                    <div>
                      <Tag color={role.color}>{role.label}</Tag>
                      <Text type="secondary" style={{ marginLeft: 8 }}>
                        {role.description}
                      </Text>
                    </div>
                  </Option>
                ))}
                <Option value="custom">
                  <div>
                    <Tag color="orange">Custom</Tag>
                    <Text type="secondary" style={{ marginLeft: 8 }}>
                      Custom permission set
                    </Text>
                  </div>
                </Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {selectedRole && (
          <Alert
            message={`${PREDEFINED_ROLES[selectedRole]?.label || 'Custom'} Role`}
            description={PREDEFINED_ROLES[selectedRole]?.description || 'Custom permission configuration'}
            type="info"
            showIcon
            icon={<InfoCircleOutlined />}
            style={{ marginBottom: 16 }}
          />
        )}

        <Divider>
          <Space>
            <LockOutlined />
            <Text strong>Permission Configuration</Text>
          </Space>
        </Divider>

        <div style={{ maxHeight: '400px', overflowY: 'auto', padding: '8px' }}>
          {Object.entries(PERMISSION_CATEGORIES).map(([key, category]) => 
            renderPermissionCard(key, category)
          )}
        </div>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Text type="secondary">
            <CheckCircleOutlined /> 
            {' '}Permissions marked with &quot;Role&quot; match the selected role. 
            &quot;Custom +&quot; indicates additional permissions, &quot;Custom -&quot; indicates removed permissions.
          </Text>
        </div>
      </Form>
    </Modal>
  );
};

export default UserEditModal; 