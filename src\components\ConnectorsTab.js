import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, Button, Popconfirm, message, Typography, Card, Tag } from 'antd';
import { collection, doc, deleteDoc, onSnapshot, addDoc, setDoc } from 'firebase/firestore';
import { db } from '../pages/firebase';
import { SYSTEMS } from '../constants';
import { TableOutlined, SearchOutlined, PlusOutlined } from '@ant-design/icons';
import { Tabs } from 'antd';

import ConnectorModal from './ConnectorModal';
const ConnectorsTab = () => {
  const [connectors, setConnectors] = useState([]);
  const [selectedConnector, setSelectedConnector] = useState(null);
  const [showConnectorModal, setShowConnectorModal] = useState(false);

  useEffect(() => {
    fetchConnectors();
  }, []);

  const fetchConnectors = async () => {
    try {
      const connectorsRef = collection(db, 'connectors');
      const unsubscribe = onSnapshot(connectorsRef, (connectorsSnapshot) => {
        const connectorsData = connectorsSnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data()
        }));
        setConnectors(connectorsData);
      }, (error) => {
        console.error("Error fetching connectors:", error);
        message.error("Failed to load connectors");
      });

      // Return the unsubscribe function for cleanup
      return unsubscribe;
    } catch (error) {
      console.error("Error setting up connectors listener:", error);
      message.error("Failed to set up connectors listener");
    }
  };

  const handleEditConnector = (connector) => {
    setSelectedConnector(connector);
    setShowConnectorModal(true);
  };

  const handleSaveConnector = async (values) => {
    try {
      setShowConnectorModal(false);
      console.log('values', values);
      // handle _delete
      if (values._delete) {
        await deleteDoc(doc(db, 'connectors', selectedConnector.id));
        setSelectedConnector(null);
        message.success("Connector deleted successfully");
        return;
      }
      if (selectedConnector?.id) {
        await setDoc(doc(db, 'connectors', selectedConnector.id), values, { merge: true });
        message.success("Connector updated successfully");
      } else {
        await addDoc(collection(db, 'connectors'), values);
        message.success("Connector added successfully");
      }
      setSelectedConnector(null);
      // fetchConnectors();
    } catch (error) {
      console.error("Error saving connector:", error);
      message.error("Failed to save connector");
    }
  };


  return (
    <div style={{ padding: '16px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
        <Button type="primary" icon={<PlusOutlined />} onClick={() => setShowConnectorModal(true)}>Add Connector</Button>
      </div>
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
        {connectors.map((connector) => (
          <Card
            key={connector.id}
            style={{ width: 300 }}
            title={connector?.name || 'Connector Name'}
            extra={<Button type="link" onClick={() => handleEditConnector(connector)}>Edit</Button>}
          >
            <div style={{ marginBottom: '8px' }}>
              <Typography.Text strong>System: </Typography.Text>
              <Typography.Text>{connector?.system ? connector.system.charAt(0).toUpperCase() + connector.system.slice(1) : 'N/A'}</Typography.Text>
            </div>
            <div style={{ marginBottom: '8px' }}>
              <Typography.Text strong>Status: </Typography.Text>
              <Tag color={connector?.status === 'active' ? 'green' : connector?.status === 'testing' ? 'blue' : 'default'}>
                {connector?.status ? connector.status.charAt(0).toUpperCase() + connector.status.slice(1) : 'N/A'}
              </Tag>
            </div>
            <Typography.Paragraph ellipsis={{ rows: 2, expandable: true, symbol: 'more' }}>
              {connector?.errors || 'No errors'}
            </Typography.Paragraph>
          </Card>
        ))}
      </div>
      <ConnectorModal visible={showConnectorModal} onCancel={() => {
        setSelectedConnector(null);
        setShowConnectorModal(false);
      }} connector={selectedConnector} onSave={handleSaveConnector} />
    </div>

  );
};

export default ConnectorsTab;