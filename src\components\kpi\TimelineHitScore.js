import React, { useState, useEffect } from 'react';
import {
    Box,
    Grid,
    Paper,
    Typography,
    CircularProgress,
    Tabs,
    Tab,
    useTheme,
    TextField,
    InputAdornment, Button
} from '@mui/material';
import {
    <PERSON><PERSON>hart,
    Bar,
    Line,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer,
    ComposedChart,
    ReferenceLine
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import ChartExportWrapper from './ChartExportWrapper';

const CHART_COLORS = {
    score: '#2196f3',    // blue
    total: '#ff9800',    // orange
    onTime: '#4caf50',   // green
    days: '#e91e63'      // pink
};

const KPI_COLORS = {
    score: { bg: '#e3f2fd', text: '#1976d2' },
    onTime: { bg: '#e8f5e9', text: '#2e7d32' },
    total: { bg: '#fff3e0', text: '#ed6c02' },
    days: { bg: '#fce4ec', text: '#c2185b' }
};

const VIEW_MODES = [
    { value: 'overall', label: 'Overall' },
    { value: 'division', label: 'Division' },
    { value: 'category', label: 'Category' },
    { value: 'family', label: 'Family' },
    { value: 'type', label: 'Product Type' },
    { value: 'specification', label: 'Specification' }
];

const TimelineHitScore = () => {
    const theme = useTheme();
    const [data, setData] = useState({
        overall: {
            totalProjects: 0,
            onTimeProjects: 0,
            score: 0,
            avgDaysToFirstSale: 0
        },
        byDivision: {},
        byCategory: {},
        byFamily: {},
        byType: {},
        bySpecification: {}
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [viewMode, setViewMode] = useState('overall');
    const [gracePeriod, setGracePeriod] = useState(30);
    const [tempGracePeriod, setTempGracePeriod] = useState(30);


    useEffect(() => {
        fetchData();
    }, [gracePeriod]);

    const fetchData = async () => {
        try {
            setLoading(true);
            const functions = getFunctions();
            const getTimelineHitScore = httpsCallable(functions, 'getTimelineHitScore');
            const result = await getTimelineHitScore({ gracePeriodDays: gracePeriod });
            if (result.data) {
                setData(result.data);
            }
        } catch (error) {
            console.error('Error fetching timeline hit score:', error);
            setError(error.message);
        } finally {
            setLoading(false);
        }
    };

    // Replace the old handleGracePeriodChange and add applyGracePeriod
    const handleGracePeriodChange = (event) => {
        const value = parseInt(event.target.value);
        if (!isNaN(value) && value > 0) {
            setTempGracePeriod(value);
        }
    };
    const applyGracePeriod = () => {
        setGracePeriod(tempGracePeriod);
    };

    const getSourceData = () => {
        if (!data) return {};

        switch (viewMode) {
            case 'overall':
                return data.overall;
            case 'division':
                return data.byDivision || {};
            case 'category':
                return data.byCategory || {};
            case 'family':
                return data.byFamily || {};
            case 'type':
                return data.byType || {};
            case 'specification':
                return data.bySpecification || {};
            default:
                return {};
        }
    };
    const transformChartData = () => {
        const sourceData = getSourceData();

        if (viewMode === 'overall') {
            return [{
                name: 'Overall',
                'Score (%)': parseFloat(sourceData.score.toFixed(1)),
                'Total Projects': sourceData.totalProjects,
                'On-Time Projects': sourceData.onTimeProjects,
                'Avg Days to First Sale': parseFloat(sourceData.avgDaysToFirstSale.toFixed(1))
            }];
        }

        return Object.entries(sourceData)
            .map(([name, stats]) => ({
                name,
                'Score (%)': parseFloat(stats.score.toFixed(1)),
                'Total Projects': stats.totalProjects,
                'On-Time Projects': stats.onTimeProjects,
                'Avg Days to First Sale': parseFloat(stats.avgDaysToFirstSale.toFixed(1))
            }))
            .sort((a, b) => b['Score (%)'] - a['Score (%)']);
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (!active || !payload) return null;

        return (
            <Paper
                elevation={3}
                sx={{
                    p: 2,
                    backgroundColor: 'background.paper',
                    border: '1px solid rgba(0, 0, 0, 0.12)'
                }}
            >
                <Typography variant="subtitle2" gutterBottom>
                    {label}
                </Typography>
                {payload.map((entry, index) => {
                    const isScore = entry.dataKey === 'Score (%)';
                    const value = isScore
                        ? `${entry.value}%`
                        : entry.value.toLocaleString();

                    return (
                        <Box
                            key={index}
                            sx={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                gap: 2,
                                alignItems: 'center',
                                my: 0.5
                            }}
                        >
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Box
                                    sx={{
                                        width: 8,
                                        height: 8,
                                        borderRadius: '50%',
                                        backgroundColor: entry.color
                                    }}
                                />
                                <Typography variant="body2">
                                    {entry.name}:
                                </Typography>
                            </Box>
                            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                {value}
                            </Typography>
                        </Box>
                    );
                })}
            </Paper>
        );
    };

    const renderChart = () => {
        const chartData = transformChartData();
        const isOverallView = viewMode === 'overall';
        const overallScore = isOverallView ? chartData[0]['Score (%)'] : null;

        return (
            <Box sx={{ height: 500, mt: 2 }}>
                <ResponsiveContainer>
                    <ComposedChart
                        data={chartData}
                        margin={{ top: 20, right: 50, left: 20, bottom: 70 }}
                    >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                            dataKey="name"
                            angle={-45}
                            textAnchor="end"
                            height={70}
                            tick={{ fontSize: 12 }}
                            interval={0}
                        />
                        <YAxis
                            yAxisId="left"
                            orientation="left"
                            domain={[0, 100]}
                            label={{
                                value: 'Score (%)',
                                angle: -90,
                                position: 'insideLeft',
                                style: { textAnchor: 'middle' }
                            }}
                        />
                        <YAxis
                            yAxisId="right"
                            orientation="right"
                            domain={[0, 'auto']}
                            label={{
                                value: 'Number of Projects',
                                angle: 90,
                                position: 'insideRight',
                                style: { textAnchor: 'middle' }
                            }}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend
                            verticalAlign="top"
                            height={36}
                            wrapperStyle={{ paddingBottom: '20px' }}
                        />
                        {isOverallView ? (
                            <ReferenceLine
                                y={overallScore}
                                yAxisId="left"
                                stroke={CHART_COLORS.score}
                                strokeWidth={2}
                            />
                        ) : (
                            <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="Score (%)"
                                stroke={CHART_COLORS.score}
                                strokeWidth={2}
                                dot={{ fill: CHART_COLORS.score, r: 4 }}
                                activeDot={{
                                    r: 6,
                                    stroke: CHART_COLORS.score,
                                    strokeWidth: 2,
                                    fill: '#fff'
                                }}
                            />
                        )}
                        <Bar
                            yAxisId="right"
                            dataKey="On-Time Projects"
                            fill={CHART_COLORS.onTime}
                            name="On-Time Projects"
                            radius={[4, 4, 0, 0]}
                        />
                        <Bar
                            yAxisId="right"
                            dataKey="Total Projects"
                            fill={CHART_COLORS.total}
                            name="Total Projects"
                            radius={[4, 4, 0, 0]}
                        />
                    </ComposedChart>
                </ResponsiveContainer>
            </Box>
        );
    };

    const formatValue = (value, type) => {
        switch (type) {
            case 'percentage':
                return `${value.toFixed(1)}%`;
            case 'days':
                return `${value.toFixed(1)} days`;
            case 'number':
                return value.toLocaleString();
            default:
                return value;
        }
    };

    const TableHeader = () => (
        <Box sx={{
            display: 'grid',
            gridTemplateColumns: '1fr repeat(4, auto)',
            gap: 2,
            p: 2,
            borderBottom: 1,
            borderColor: 'divider',
            bgcolor: 'background.default'
        }}>
            <Typography variant="subtitle2">Name</Typography>
            <Typography variant="subtitle2" align="right">Score</Typography>
            <Typography variant="subtitle2" align="right">Total Projects</Typography>
            <Typography variant="subtitle2" align="right">On-Time Projects</Typography>
            <Typography variant="subtitle2" align="right">Avg Days to Sale</Typography>
        </Box>
    );

    const TableRow = ({ item }) => (
        <Box sx={{
            display: 'grid',
            gridTemplateColumns: '1fr repeat(4, auto)',
            gap: 2,
            p: 2,
            '&:not(:last-child)': {
                borderBottom: 1,
                borderColor: 'divider'
            },
            '&:hover': {
                bgcolor: 'action.hover'
            }
        }}>
            <Typography variant="body2">{item.name}</Typography>
            <Typography variant="body2" align="right" sx={{ color: CHART_COLORS.score }}>
                {formatValue(item['Score (%)'], 'percentage')}
            </Typography>
            <Typography variant="body2" align="right" sx={{ color: CHART_COLORS.total }}>
                {formatValue(item['Total Projects'], 'number')}
            </Typography>
            <Typography variant="body2" align="right" sx={{ color: CHART_COLORS.onTime }}>
                {formatValue(item['On-Time Projects'], 'number')}
            </Typography>
            <Typography variant="body2" align="right" sx={{ color: CHART_COLORS.days }}>
                {formatValue(item['Avg Days to First Sale'], 'days')}
            </Typography>
        </Box>
    );

    const renderTable = () => {
        const tableData = transformChartData();
        return (
            <Paper elevation={0} sx={{ mt: 3, border: 1, borderColor: 'divider', borderRadius: 1, overflow: 'hidden' }}>
                <TableHeader />
                {tableData.map((item, index) => (
                    <TableRow key={index} item={item} />
                ))}
            </Paper>
        );
    };

    if (loading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" height={400}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" height={400}>
                <Typography color="error">Error: {error}</Typography>
            </Box>
        );
    }

    // Replace the entire return section with this updated version
    return (
        <ChartExportWrapper title={`Timeline_Hit_Score_${viewMode}`}>
            <Box sx={{ p: 3 }}>
                {/* KPI Cards */}
                <Grid container spacing={3} sx={{ mb: 4 }}>
                    <Grid item xs={12} sm={6} md={4}>
                        <KPICard
                            title="Timeline Hit Score"
                            value={`${(data.overall?.score || 0).toFixed(1)}%`}
                            bgColor={KPI_COLORS.score.bg}
                            textColor={KPI_COLORS.score.text}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                        <KPICard
                            title="On-Time Projects"
                            value={`${data.overall?.onTimeProjects || 0}`}
                            bgColor={KPI_COLORS.onTime.bg}
                            textColor={KPI_COLORS.onTime.text}
                        />
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                        <KPICard
                            title="Total Projects"
                            value={data.overall?.totalProjects?.toLocaleString() || 0}
                            bgColor={KPI_COLORS.total.bg}
                            textColor={KPI_COLORS.total.text}
                        />
                    </Grid>
                </Grid>

                {/* View Mode Selector */}
                <Box sx={{ mb: 3 }}>
                    <Tabs
                        value={viewMode}
                        onChange={(e, newValue) => setViewMode(newValue)}
                        aria-label="view mode tabs"
                        variant="scrollable"
                        scrollButtons="auto"
                    >
                        {VIEW_MODES.map((mode) => (
                            <Tab key={mode.value} label={mode.label} value={mode.value} />
                        ))}
                    </Tabs>
                </Box>

                {/* Chart Section */}
                <Paper elevation={3} sx={{ p: 3 }}>
                    {renderChart()}
                </Paper>

                {/* Footer with Grace Period */}
                <Box sx={{ mt: 2 }}>
                    <Box sx={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        alignItems: 'center',
                        gap: 1,
                        mb: 1
                    }}>
                        <Typography component="span" color="text.secondary">
                            Grace Period
                        </Typography>
                        <TextField
                            type="number"
                            value={tempGracePeriod}
                            onChange={handleGracePeriodChange}
                            variant="outlined"
                            size="small"
                            inputProps={{
                                min: 1,
                                style: { textAlign: 'right', paddingRight: '8px' }
                            }}
                            sx={{
                                width: '100px',
                                '& .MuiOutlinedInput-root': {
                                    height: '32px'
                                },
                                '& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button': {
                                    display: 'none'
                                },
                                '& input[type=number]': {
                                    MozAppearance: 'textfield'
                                }
                            }}
                        />
                        <Typography component="span" color="text.secondary">
                            days
                        </Typography>
                        <Button
                            variant="contained"
                            onClick={applyGracePeriod}
                            sx={{
                                height: '32px',
                                minWidth: '80px',
                                bgcolor: CHART_COLORS.score,
                                '&:hover': { bgcolor: CHART_COLORS.score }
                            }}
                        >
                            Apply
                        </Button>
                    </Box>
                    <Typography variant="caption" sx={{ display: 'block', textAlign: 'right', color: 'text.secondary' }}>
                        * Projects are considered on-time if first sale occurs within {gracePeriod} days of launch
                    </Typography>
                    <Typography variant="caption" sx={{ display: 'block', textAlign: 'right', color: 'text.secondary' }}>
                        Showing data for the last 12 months
                    </Typography>
                </Box>
            </Box>
        </ChartExportWrapper>
    );
};

export default TimelineHitScore;