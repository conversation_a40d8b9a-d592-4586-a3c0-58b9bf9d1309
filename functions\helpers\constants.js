if (process.env.FUNCTIONS_EMULATOR) {
  exports.TASK_URLS = {
    updateBigQueryTable: "http://localhost:8080/updateBigQueryTableOnRequest"
  };
} else {
  exports.TASK_URLS = {
    updateBigQueryTable: "https://us-central1-slickjoy-hj.cloudfunctions.net/updateBigQueryTableOnRequest"
  };
}

exports.flattenListOfObjects = (l) => {
  return l.map((item) => flattenObject(item));
};

/**
 * Recursively flattens a nested object structure.
 *
 * @param {Object} obj - The object to flatten
 * @param {string} [prefix=''] - The prefix to use for nested keys
 * @return {Object} - A flattened object
 */
const flattenObject = (obj, prefix = "") => {
  return Object.keys(obj).reduce((acc, key) => {
    const prefixedKey = prefix ? `${prefix}.${key}` : key;

    if (typeof obj[key] === "object" && obj[key] !== null && !Array.isArray(obj[key])) {
      // Recursively flatten nested objects
      Object.assign(acc, flattenObject(obj[key], prefixedKey));
    } else if (Array.isArray(obj[key])) {
      // Handle arrays by joining values or flattening array of objects
      if (obj[key].length > 0 && typeof obj[key][0] === "object") {
        // If array contains objects, flatten each object
        acc[prefixedKey] = obj[key].map((item) =>
          typeof item === "object" ? flattenObject(item) : item,
        );
      } else {
        // For simple arrays, just assign the array
        acc[prefixedKey] = obj[key];
      }
    } else {
      // For primitive values, assign directly
      acc[prefixedKey] = obj[key];
    }

    return acc;
  }, {});
};

/**
 * Converts a JSON Lines object into a flattened JavaScript object.
 * Each line in the JSON Lines format is parsed and flattened to remove nested structures.
 *
 * @param {string} jsonLines - The JSON Lines string where each line is a valid JSON object
 * @return {Array} - Array of flattened JavaScript objects
 */
exports.jsonLinesToFlatObject = (jsonLines) => {
  if (!jsonLines) return [];

  // Split the input by newlines and filter out empty lines
  const lines = jsonLines.split("\n").filter((line) => line.trim());

  // Parse each line and flatten the objects
  return lines.map((line) => {
    try {
      const obj = JSON.parse(line);
      return flattenObject(obj);
    } catch (error) {
      console.error(`Error parsing JSON line: ${error.message}`);
      return null;
    }
  }).filter(Boolean); // Remove any null entries from parsing errors
};

