---
description: This rule describes the structure of the pages and components, and how they should be organized.
alwaysApply: false
---
# Page Structure
- Each page should have a corresponding file in the `src/pages` directory.
- Each page should end with the suffix `Page.tsx`.

# Components
- Components for the page should be in the `src/components/{PageName}` folder, especially all modals, forms, tables, charts, etc.
- Whenever possible create components that can be reused across pages and place it in the `src/components/AppComponents` folder.
- If a component is only used on a single page, place it in the `src/components/{PageName}` folder.
