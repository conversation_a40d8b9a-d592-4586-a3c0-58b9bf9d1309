const { onCall } = require("firebase-functions/v2/https");
const functions = require("firebase-functions");
const logger = require("firebase-functions/logger");
const { makeNSSavedSearchRequest, executeNSSuiteQLQuery } = require("../helpers/netsuite");
const admin = require("firebase-admin");
const { runQuery } = require("../helpers/bigQuery");

// Helper function to calculate overall average cycle time
const calculateOverallAverageCycleTime = (monthlyData) => {
  let totalCycleTime = 0;
  const totalMonths = monthlyData.length;

  monthlyData.forEach((month) => {
    totalCycleTime += month.cycleTime;
  });

  return totalCycleTime / totalMonths;
};

// Cloud Function to return Order Fulfillment Cycle Time data
exports.getOrderFulfillmentCycleTime = onCall(async (data, context) => {
  try {
    logger.info("Fetching data from NetSuite saved search");
    const parsedData = await makeNSSavedSearchRequest("103583");

    if (!parsedData || parsedData.length === 0) {
      logger.warn("No data returned from saved search");
      return {
        monthlyData: [],
        totalOrders: null,
        averageCycleTime: 0,
        lastCompleteMonth: null,
        message: "No data available for the specified time range",
      };
    }

    const monthlyData = parsedData
      .filter((row) => row["Ship Date"] !== "Total")
      .map((row) => ({
        date: row["Ship Date"],
        cycleTime: parseFloat(row["Average of Difference"]) || 0,
        orders: null,
      }))
      .sort((a, b) => b.date.localeCompare(a.date));

    if (monthlyData.length === 0) {
      logger.warn("No valid monthly data after processing");
      return {
        monthlyData: [],
        totalOrders: null,
        averageCycleTime: 0,
        lastCompleteMonth: null,
        message: "No valid data available after processing",
      };
    }

    const averageCycleTime = calculateOverallAverageCycleTime(monthlyData);
    const lastCompleteMonth = monthlyData[0].date;

    logger.info("Processed monthly data:", JSON.stringify(monthlyData));
    logger.info(`Calculated average cycle time: ${averageCycleTime}`);
    logger.info(`Last complete month: ${lastCompleteMonth}`);

    return {
      monthlyData,
      totalOrders: null,
      averageCycleTime: +averageCycleTime.toFixed(2),
      lastCompleteMonth,
    };
  } catch (error) {
    logger.error("Error calculating Order Fulfillment Cycle Time:", error);
    throw new Error(`Failed to calculate Order Fulfillment Cycle Time: ${error.message}`);
  }
});

exports.getReturnRateV2 = onCall(async (data, context) => {
  try {
    logger.info("Fetching return rate data...");

    const returnedQtyQuery = `
      SELECT
        TO_CHAR(ra.trandate, 'YYYY-MM') AS month,
        CUSTOMRECORD_PRODUCT_TYPE.name AS product_type,
        SUM(raLine.quantity) AS returned_quantity
      FROM
        transaction ra
      JOIN
        transactionline raLine ON ra.id = raLine.transaction
      JOIN
        item ON raLine.item = item.id
      LEFT JOIN
        CUSTOMRECORD_PRODUCT_TYPE ON item.custitem_product_type = CUSTOMRECORD_PRODUCT_TYPE.id
      WHERE
        ra.type = 'RtnAuth'
        AND item.custitem_product_type IS NOT NULL
        AND ra.trandate BETWEEN ADD_MONTHS(CURRENT_DATE, -12) AND CURRENT_DATE
      GROUP BY
        TO_CHAR(ra.trandate, 'YYYY-MM'),
        CUSTOMRECORD_PRODUCT_TYPE.name
      ORDER BY
        TO_CHAR(ra.trandate, 'YYYY-MM')
    `;

    const soldQtyQuery = `
      SELECT 
        TO_CHAR(tran.trandate, 'YYYY-MM') AS month, 
        CUSTOMRECORD_PRODUCT_TYPE.name AS product_type, 
        SUM(ABS(tranLine.quantity)) AS quantity
      FROM 
        transaction tran
      JOIN 
        transactionline tranLine ON tran.id = tranLine.transaction
      JOIN 
        item ON tranLine.item = item.id
      LEFT JOIN 
        CUSTOMRECORD_PRODUCT_TYPE ON item.custitem_product_type = CUSTOMRECORD_PRODUCT_TYPE.id
      WHERE 
        tran.type = 'CustInvc'
        AND item.isinactive = 'F'
        AND tran.trandate BETWEEN ADD_MONTHS(CURRENT_DATE, -12) AND CURRENT_DATE
        AND item.custitem_product_type IS NOT NULL
      GROUP BY 
        TO_CHAR(tran.trandate, 'YYYY-MM'), 
        CUSTOMRECORD_PRODUCT_TYPE.name
      ORDER BY 
        TO_CHAR(tran.trandate, 'YYYY-MM')
    `;

    const returnedQtyResult = await executeNSSuiteQLQuery(returnedQtyQuery);
    const soldQtyResult = await executeNSSuiteQLQuery(soldQtyQuery);

    logger.info("Returned Quantity Result:", JSON.stringify(returnedQtyResult));
    logger.info("Sold Quantity Result:", JSON.stringify(soldQtyResult));

    if (!returnedQtyResult || !returnedQtyResult.items || !Array.isArray(returnedQtyResult.items)) {
      logger.warn("Empty or invalid response for returned quantity query");
      returnedQtyResult.items = [];
    }

    if (!soldQtyResult || !soldQtyResult.items || !Array.isArray(soldQtyResult.items)) {
      logger.warn("Empty or invalid response for sold quantity query");
      soldQtyResult.items = [];
    }

    const returnRateData = processReturnRateData(returnedQtyResult.items, soldQtyResult.items);

    logger.info("Return rate data processed successfully");
    return returnRateData;
  } catch (error) {
    logger.error("Error fetching or processing return rate data:", error);
    throw new Error(`Failed to calculate Return Rate: ${error.message}`);
  }
});
/**
 * Process the returned quantity and sold quantity data to calculate return rates
 * @param {Array} returnedQtyData - Returned quantity data
 * @param {Array} soldQtyData - Sold quantity data
 * @return {Object} - Processed return rate data
 */
function processReturnRateData(returnedQtyData, soldQtyData) {
  const monthlyData = {};
  const productTypeData = {};
  let totalReturns = 0;
  let totalSold = 0;

  soldQtyData.forEach((item) => {
    const month = item.month;
    const productType = item.product_type;
    const quantity = parseFloat(item.quantity) || 0;

    if (!monthlyData[month]) monthlyData[month] = { sold: 0, returns: 0, productTypes: {} };
    if (!monthlyData[month].productTypes[productType]) monthlyData[month].productTypes[productType] = { sold: 0, returns: 0 };
    if (!productTypeData[productType]) productTypeData[productType] = { sold: 0, returns: 0, monthlyData: {} };
    if (!productTypeData[productType].monthlyData[month]) productTypeData[productType].monthlyData[month] = { sold: 0, returns: 0 };

    monthlyData[month].sold += quantity;
    monthlyData[month].productTypes[productType].sold += quantity;
    productTypeData[productType].sold += quantity;
    productTypeData[productType].monthlyData[month].sold += quantity;
    totalSold += quantity;
  });

  returnedQtyData.forEach((item) => {
    const month = item.month;
    const productType = item.product_type;
    const quantity = Math.abs(parseFloat(item.returned_quantity)) || 0;

    if (!monthlyData[month]) monthlyData[month] = { sold: 0, returns: 0, productTypes: {} };
    if (!monthlyData[month].productTypes[productType]) monthlyData[month].productTypes[productType] = { sold: 0, returns: 0 };
    if (!productTypeData[productType]) productTypeData[productType] = { sold: 0, returns: 0, monthlyData: {} };
    if (!productTypeData[productType].monthlyData[month]) productTypeData[productType].monthlyData[month] = { sold: 0, returns: 0 };

    monthlyData[month].returns += quantity;
    monthlyData[month].productTypes[productType].returns += quantity;
    productTypeData[productType].returns += quantity;
    productTypeData[productType].monthlyData[month].returns += quantity;
    totalReturns += quantity;
  });

  Object.keys(monthlyData).forEach((month) => {
    const data = monthlyData[month];
    data.returnRate = data.sold > 0 ? (data.returns / data.sold) * 100 : 0;
    Object.keys(data.productTypes).forEach((productType) => {
      const ptData = data.productTypes[productType];
      ptData.returnRate = ptData.sold > 0 ? (ptData.returns / ptData.sold) * 100 : 0;
    });
  });

  Object.keys(productTypeData).forEach((productType) => {
    const data = productTypeData[productType];
    data.returnRate = data.sold > 0 ? (data.returns / data.sold) * 100 : 0;
    Object.keys(data.monthlyData).forEach((month) => {
      const monthData = data.monthlyData[month];
      monthData.returnRate = monthData.sold > 0 ? (monthData.returns / monthData.sold) * 100 : 0;
    });
  });

  const overallReturnRate = totalSold > 0 ? (totalReturns / totalSold) * 100 : 0;

  return {
    overallReturnRate: overallReturnRate.toFixed(2),
    monthlyData: Object.entries(monthlyData).map(([month, data]) => ({
      month,
      returnRate: parseFloat(data.returnRate.toFixed(2)),
      returns: data.returns,
      sold: data.sold,
      productTypes: Object.entries(data.productTypes).map(([pt, ptData]) => ({
        productType: pt,
        returnRate: parseFloat(ptData.returnRate.toFixed(2)),
        returns: ptData.returns,
        sold: ptData.sold,
      })),
    })).sort((a, b) => a.month.localeCompare(b.month)),
    productTypeReturnRates: Object.entries(productTypeData).map(([productType, data]) => ({
      productType,
      returnRate: parseFloat(data.returnRate.toFixed(2)),
      returns: data.returns,
      sold: data.sold,
      monthlyData: Object.entries(data.monthlyData).map(([month, monthData]) => ({
        month,
        returnRate: parseFloat(monthData.returnRate.toFixed(2)),
        returns: monthData.returns,
        sold: monthData.sold,
      })).sort((a, b) => a.month.localeCompare(b.month)),
    })).sort((a, b) => b.returnRate - a.returnRate),
    totalReturns,
    totalSold,
  };
}

exports.getUnfulfilledOrderRate = onCall(async (data, context) => {
  try {
    // console.log("Fetching unfulfilled order rate data...");

    const unfulfilledOrdersQuery = `
      SELECT
          TO_CHAR(trandate, 'YYYY-MM') AS month_year,
          COUNT(*) AS total_unfulfilled_orders
      FROM
          transaction
      WHERE
          type = 'SalesOrd'
          AND status IN ('A', 'B')
          AND trandate BETWEEN ADD_MONTHS(CURRENT_DATE, -12) AND CURRENT_DATE
      GROUP BY
          TO_CHAR(trandate, 'YYYY-MM')
      ORDER BY
          TO_DATE(TO_CHAR(trandate, 'YYYY-MM'), 'YYYY-MM') ASC
    `;

    const totalOrdersQuery = `
      SELECT 
        TO_CHAR(trandate, 'YYYY-MM') AS month_year, 
        COUNT(*) AS total_orders
      FROM 
        transaction
      WHERE 
        type = 'SalesOrd'
        AND trandate BETWEEN ADD_MONTHS(CURRENT_DATE, -12) AND CURRENT_DATE
      GROUP BY 
        TO_CHAR(trandate, 'YYYY-MM')
      ORDER BY 
        TO_DATE(TO_CHAR(trandate, 'YYYY-MM'), 'YYYY-MM') ASC
    `;

    // console.log("Executing unfulfilled orders query...");
    const unfulfilledOrdersResult = await executeNSSuiteQLQuery(unfulfilledOrdersQuery);
    // console.log("Unfulfilled Orders Result:", JSON.stringify(unfulfilledOrdersResult));

    // console.log("Executing total orders query...");
    const totalOrdersResult = await executeNSSuiteQLQuery(totalOrdersQuery);
    // console.log("Total Orders Result:", JSON.stringify(totalOrdersResult));

    const unfulfilledOrderRateData = processUnfulfilledOrderRateData(
      unfulfilledOrdersResult.items || [],
      totalOrdersResult.items || [],
    );

    // console.log("Unfulfilled order rate data processed successfully");
    // console.log("Processed data:", JSON.stringify(unfulfilledOrderRateData));
    return unfulfilledOrderRateData;
  } catch (error) {
    // console.error("Error fetching or processing unfulfilled order rate data:", error);
    throw new functions.https.HttpsError("internal", "Failed to calculate Unfulfilled Order Rate", error.message);
  }
});
/**
 * Process the unfulfilled order rate data
 * @param {Array} unfulfilledOrdersData - Unfulfilled orders data
 * @param {Array} totalOrdersData - Total orders data
 * @return {Object} - Processed unfulfilled order rate data
 */
function processUnfulfilledOrderRateData(unfulfilledOrdersData, totalOrdersData) {
  // console.log("Processing unfulfilled order rate data...");
  // console.log("Unfulfilled orders data:", JSON.stringify(unfulfilledOrdersData));
  // console.log("Total orders data:", JSON.stringify(totalOrdersData));

  const monthlyData = {};
  let totalUnfulfilledOrders = 0;
  let totalOrders = 0;

  totalOrdersData.forEach((item) => {
    const monthYear = item.month_year;
    const totalOrdersCount = parseInt(item.total_orders);

    monthlyData[monthYear] = { totalOrders: totalOrdersCount, unfulfilledOrders: 0, unfulfilledOrderRate: 0 };
    totalOrders += totalOrdersCount;
  });

  unfulfilledOrdersData.forEach((item) => {
    const monthYear = item.month_year;
    const unfulfilledOrdersCount = parseInt(item.total_unfulfilled_orders);

    if (monthlyData[monthYear]) {
      monthlyData[monthYear].unfulfilledOrders = unfulfilledOrdersCount;
      totalUnfulfilledOrders += unfulfilledOrdersCount;
    } else {
      // console.log(`Unexpected month-year in unfulfilled orders: ${monthYear}`);
    }
  });

  const processedData = Object.entries(monthlyData).map(([monthYear, data]) => {
    const unfulfilledOrderRate = data.totalOrders > 0 ? (data.unfulfilledOrders / data.totalOrders) * 100 : 0;
    return {
      monthYear,
      totalOrders: data.totalOrders,
      unfulfilledOrders: data.unfulfilledOrders,
      unfulfilledOrderRate: parseFloat(unfulfilledOrderRate.toFixed(2)),
    };
  }).sort((a, b) => a.monthYear.localeCompare(b.monthYear));

  const overallUnfulfilledOrderRate = totalOrders > 0 ? (totalUnfulfilledOrders / totalOrders) * 100 : 0;

  return {
    overallUnfulfilledOrderRate: parseFloat(overallUnfulfilledOrderRate.toFixed(2)),
    monthlyData: processedData,
    totalUnfulfilledOrders,
    totalOrders,
  };
}

exports.getOrderAccuracyRate = onCall(async (data, context) => {
  try {
    logger.info("Fetching order accuracy rate data...");

    // Get accuracy tags from Firestore
    const tagsDoc = await admin
      .firestore()
      .collection("settings")
      .doc("gorgiasTagMappings")
      .get();

    if (!tagsDoc.exists) {
      throw new Error("Tag mappings not found in Firestore");
    }

    const tagMappings = tagsDoc.data();
    const accuracyTags = tagMappings["Order Accuracy Rate"].tags.accurateOrders;

    logger.info("Accuracy Tags:", accuracyTags);

    // Query for total orders from NetSuite
    const orderQuery = `
      SELECT 
        TO_CHAR(trandate, 'YYYY-MM') AS month_year, 
        COUNT(*) AS total_orders
      FROM 
        transaction
      WHERE 
        type = 'SalesOrd'
        AND trandate BETWEEN ADD_MONTHS(CURRENT_DATE, -12) AND CURRENT_DATE
      GROUP BY 
        TO_CHAR(trandate, 'YYYY-MM')
      ORDER BY 
        TO_DATE(TO_CHAR(trandate, 'YYYY-MM'), 'YYYY-MM') ASC
    `;

    // BigQuery query for inaccurate orders
    const inaccurateOrdersQuery = `
    WITH TaggedTickets AS (
      SELECT 
        t.id,
        t.createdAt,
        tag.name as tag_name
      FROM 
        customerService.gorgiasTickets t
        CROSS JOIN UNNEST(t.tags) as tag
      WHERE 
        t.createdAt >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
        AND tag.name IN (${accuracyTags.map(tag => `'${tag}'`).join(",")})
    ),
    MonthlyStats AS (
      SELECT 
        FORMAT_TIMESTAMP('%Y-%m', createdAt) as month_year,
        COUNT(DISTINCT id) as inaccurate_orders
      FROM 
        TaggedTickets
      GROUP BY 
        month_year
    ),
    IssueTypes AS (
      SELECT 
        tag_name as type,
        COUNT(DISTINCT id) as count
      FROM 
        TaggedTickets
      GROUP BY 
        tag_name
      ORDER BY 
        count DESC
    )
    SELECT 
      m.*,
      ARRAY_AGG(STRUCT(type, count)) as issue_types
    FROM 
      MonthlyStats m
    CROSS JOIN IssueTypes
    GROUP BY 
      month_year,
      inaccurate_orders
    ORDER BY 
      month_year ASC`;

    logger.info("Generated BigQuery Query:", inaccurateOrdersQuery);

    const [ordersResult, inaccurateOrdersResult] = await Promise.all([
      executeNSSuiteQLQuery(orderQuery),
      runQuery({ query: inaccurateOrdersQuery })
    ]);

    logger.info("Orders Result:", JSON.stringify(ordersResult));
    logger.info("Inaccurate Orders Result:", JSON.stringify(inaccurateOrdersResult));

    if (!ordersResult?.items || !Array.isArray(ordersResult.items)) {
      throw new Error("Invalid response structure from NetSuite query");
    }

    // Process and combine the data
    const monthlyData = {};
    let totalOrders = 0;
    let totalInaccurateOrders = 0;

    // Initialize monthly data structure from NetSuite orders
    ordersResult.items.forEach(row => {
      const monthYear = row.month_year;
      const monthOrders = parseInt(row.total_orders) || 0;

      monthlyData[monthYear] = {
        monthYear,
        totalOrders: monthOrders,
        inaccurateOrders: 0,
        accurateOrders: monthOrders,
        accuracyRate: 100
      };
      totalOrders += monthOrders;
    });

    // Process inaccurate orders from BigQuery result
    const issueTypes = [];

    inaccurateOrdersResult.forEach(row => {
      const monthYear = row.month_year;
      if (monthlyData[monthYear]) {
        const inaccurateOrderCount = parseInt(row.inaccurate_orders) || 0;
        monthlyData[monthYear].inaccurateOrders = inaccurateOrderCount;
        monthlyData[monthYear].accurateOrders = monthlyData[monthYear].totalOrders - inaccurateOrderCount;

        // New accuracy rate calculation with 99.99% max if not perfect
        let accuracyRate = (monthlyData[monthYear].accurateOrders / monthlyData[monthYear].totalOrders) * 100;
        if (inaccurateOrderCount > 0) {
          accuracyRate = Math.min(accuracyRate, 99.99);
        }
        monthlyData[monthYear].accuracyRate = accuracyRate.toFixed(2);

        totalInaccurateOrders += inaccurateOrderCount;
      }

      if (row.issue_types && Array.isArray(row.issue_types)) {
        row.issue_types.forEach(issue => {
          if (!issueTypes.find(i => i.type === issue.type)) {
            issueTypes.push({
              type: issue.type,
              count: parseInt(issue.count) || 0
            });
          }
        });
      }
    });

    // Sort issue types by count in descending order
    // Sort issue types by count in descending order
    issueTypes.sort((a, b) => b.count - a.count);

    const totalAccurateOrders = totalOrders - totalInaccurateOrders;
    // New overall accuracy rate calculation with 99.99% max if not perfect
    let overallAccuracyRate = ((totalAccurateOrders / totalOrders) * 100);
    if (totalInaccurateOrders > 0) {
      overallAccuracyRate = Math.min(overallAccuracyRate, 99.99);
    }
    overallAccuracyRate = overallAccuracyRate.toFixed(2);

    logger.info("Order accuracy rate data processed successfully");
    logger.info(`Total orders: ${totalOrders}, Accurate orders: ${totalAccurateOrders}`);
    logger.info(`Overall accuracy rate: ${overallAccuracyRate}%`);

    return {
      overallAccuracyRate,
      monthlyData: Object.values(monthlyData)
        .sort((a, b) => a.monthYear.localeCompare(b.monthYear)),
      summary: {
        totalOrders,
        accurateOrders: totalAccurateOrders,
        inaccurateOrders: totalInaccurateOrders
      },
      qualityIssues: {
        types: issueTypes
      }
    };
  } catch (error) {
    logger.error("Error calculating order accuracy rate:", error);
    throw new Error(`Failed to calculate Order Accuracy Rate: ${error.message}`);
  }
});

exports.getPerfectOrderRate = onCall(async (data, context) => {
  try {
    logger.info("Fetching perfect order rate data...");

    // Get perfect order tags from Firestore
    const tagsDoc = await admin
      .firestore()
      .collection("settings")
      .doc("gorgiasTagMappings")
      .get();

    if (!tagsDoc.exists) {
      throw new Error("Tag mappings not found in Firestore");
    }

    const tagMappings = tagsDoc.data();
    const perfectOrderTags = tagMappings["Perfect Order Rate"].tags.perfectOrders;

    logger.info("Perfect Order Tags:", perfectOrderTags);

    // Query for total orders from NetSuite
    const orderQuery = `
      SELECT 
        TO_CHAR(trandate, 'YYYY-MM') AS month_year, 
        COUNT(*) AS total_orders
      FROM 
        transaction
      WHERE 
        type = 'SalesOrd'
        AND trandate BETWEEN ADD_MONTHS(CURRENT_DATE, -12) AND CURRENT_DATE
      GROUP BY 
        TO_CHAR(trandate, 'YYYY-MM')
      ORDER BY 
        TO_DATE(TO_CHAR(trandate, 'YYYY-MM'), 'YYYY-MM') ASC
    `;

    // BigQuery query for non-perfect orders
    const nonPerfectOrdersQuery = `
    WITH TaggedTickets AS (
      SELECT 
        t.id,
        t.createdAt,
        tag.name as tag_name
      FROM 
        customerService.gorgiasTickets t
        CROSS JOIN UNNEST(t.tags) as tag
      WHERE 
        t.createdAt >= DATE_SUB(CURRENT_DATE(), INTERVAL 12 MONTH)
        AND tag.name IN (${perfectOrderTags.map(tag => `'${tag}'`).join(",")})
    ),
    MonthlyStats AS (
      SELECT 
        FORMAT_TIMESTAMP('%Y-%m', createdAt) as month_year,
        COUNT(DISTINCT id) as non_perfect_orders
      FROM 
        TaggedTickets
      GROUP BY 
        month_year
    ),
    IssueTypes AS (
      SELECT 
        tag_name as type,
        COUNT(DISTINCT id) as count
      FROM 
        TaggedTickets
      GROUP BY 
        tag_name
      ORDER BY 
        count DESC
    )
    SELECT 
      m.*,
      ARRAY_AGG(STRUCT(type, count)) as issue_types
    FROM 
      MonthlyStats m
    CROSS JOIN IssueTypes
    GROUP BY 
      month_year,
      non_perfect_orders
    ORDER BY 
      month_year ASC`;

    logger.info("Generated BigQuery Query:", nonPerfectOrdersQuery);

    const [ordersResult, nonPerfectOrdersResult] = await Promise.all([
      executeNSSuiteQLQuery(orderQuery),
      runQuery({ query: nonPerfectOrdersQuery })
    ]);

    logger.info("Orders Result:", JSON.stringify(ordersResult));
    logger.info("Non-Perfect Orders Result:", JSON.stringify(nonPerfectOrdersResult));

    if (!ordersResult?.items || !Array.isArray(ordersResult.items)) {
      throw new Error("Invalid response structure from NetSuite query");
    }

    // Process and combine the data
    const monthlyData = {};
    let totalOrders = 0;
    let totalNonPerfectOrders = 0;

    // Initialize monthly data structure from NetSuite orders
    ordersResult.items.forEach(row => {
      const monthYear = row.month_year;
      const monthOrders = parseInt(row.total_orders) || 0;

      monthlyData[monthYear] = {
        monthYear,
        totalOrders: monthOrders,
        nonPerfectOrders: 0,
        perfectOrders: monthOrders,
        perfectOrderRate: 100
      };
      totalOrders += monthOrders;
    });

    // Process non-perfect orders from BigQuery result
    const issueTypes = [];

    nonPerfectOrdersResult.forEach(row => {
      const monthYear = row.month_year;
      if (monthlyData[monthYear]) {
        const nonPerfectOrderCount = parseInt(row.non_perfect_orders) || 0;
        monthlyData[monthYear].nonPerfectOrders = nonPerfectOrderCount;
        monthlyData[monthYear].perfectOrders = monthlyData[monthYear].totalOrders - nonPerfectOrderCount;

        // New perfect order rate calculation with 99.99% max if not perfect
        let perfectOrderRate = (monthlyData[monthYear].perfectOrders / monthlyData[monthYear].totalOrders) * 100;
        if (nonPerfectOrderCount > 0) {
          perfectOrderRate = Math.min(perfectOrderRate, 99.99);
        }
        monthlyData[monthYear].perfectOrderRate = perfectOrderRate.toFixed(2);

        totalNonPerfectOrders += nonPerfectOrderCount;
      }

      if (row.issue_types && Array.isArray(row.issue_types)) {
        row.issue_types.forEach(issue => {
          if (!issueTypes.find(i => i.type === issue.type)) {
            issueTypes.push({
              type: issue.type,
              count: parseInt(issue.count) || 0
            });
          }
        });
      }
    });

    // Sort issue types by count in descending order
    issueTypes.sort((a, b) => b.count - a.count);

    const totalPerfectOrders = totalOrders - totalNonPerfectOrders;
    // New overall perfect order rate calculation with 99.99% max if not perfect
    let overallPerfectOrderRate = ((totalPerfectOrders / totalOrders) * 100);
    if (totalNonPerfectOrders > 0) {
      overallPerfectOrderRate = Math.min(overallPerfectOrderRate, 99.99);
    }
    overallPerfectOrderRate = overallPerfectOrderRate.toFixed(2);

    logger.info("Perfect order rate data processed successfully");
    logger.info(`Total orders: ${totalOrders}, Perfect orders: ${totalPerfectOrders}`);
    logger.info(`Overall perfect order rate: ${overallPerfectOrderRate}%`);

    return {
      overallPerfectOrderRate,
      monthlyData: Object.values(monthlyData)
        .sort((a, b) => a.monthYear.localeCompare(b.monthYear)),
      summary: {
        totalOrders,
        perfectOrders: totalPerfectOrders,
        nonPerfectOrders: totalNonPerfectOrders
      },
      qualityIssues: {
        types: issueTypes
      }
    };
  } catch (error) {
    logger.error("Error calculating perfect order rate:", error);
    throw new Error(`Failed to calculate Perfect Order Rate: ${error.message}`);
  }
});