/* eslint-disable react/prop-types */
import React, {useState} from 'react';
import { CopyOutlined } from '@ant-design/icons';

const CopyToClipboardIcon = ({valueToCopy}) => {
  const [animate, setAnimate] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(valueToCopy);
      console.log('Value copied to clipboard');
      setAnimate(true);
      setTimeout(() => setAnimate(false), 300); // Reset animation after 300ms
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <CopyOutlined
      onClick={handleCopy}
      className={animate ? 'icon-animation' : 'copy-icon'}
    />
  );
};

export default CopyToClipboardIcon;
