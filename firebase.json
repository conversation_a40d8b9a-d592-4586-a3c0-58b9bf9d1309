{"hosting": {"public": "build", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "emulators": {"auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8081}, "hosting": {"port": 5002}, "storage": {"port": 9199}, "ui": {"enabled": true}, "singleProjectMode": true}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint"]}], "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}}