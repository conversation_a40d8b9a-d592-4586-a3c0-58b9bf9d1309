import React, { useState, useEffect } from 'react';
import { Popconfirm, Button, Upload, message, Modal, Select, Table, Input, InputNumber, DatePicker, Checkbox, Image, Typography } from 'antd';
import { CloudUploadOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { v4 as uuidv4 } from 'uuid';
import { api } from '../../pages/firebase';
import Papa from 'papaparse';
import FormItem from './FormItem';
import axios from 'axios';
import { ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { storage } from '../../pages/firebase';
import { PRODUCT_TABLE } from './template';
const { Text, Title } = Typography;


const CSVImport = ({ fields, products, setProducts, fetchProductsAsOptions, viewType = 'Product' }) => {
  const [fileUploaded, setFileUploaded] = useState(false);
  const [importTableView, setImportTableView] = useState(false);
  const [csvData, setCSVData] = useState([]);
  const [tableColumns, setTableColumns] = useState([]);
  const [tableMappingData, setTableMappingData] = useState([]);
  const [importData, setImportData] = useState([]);
  const [importTableColumns, setImportTableColumns] = useState([]);
  const [isForUpdate, setIsForUpdate] = useState(false);
  const [statusText, setStatusText] = useState(null);
  const [productsAsOptions, setProductsAsOptions] = useState([]);
  const [relatedProducts, setRelatedProducts] = useState([]);

  useEffect(() => {
    fetchProductsAsOptions().then((options) => {
      console.log('options', options);
      setProductsAsOptions(options);
    });
  }, [fields]);

  const fetchRelatedProducts = async (productIds) => {
    const productQuery = `SELECT * FROM ${PRODUCT_TABLE} WHERE id IN (${productIds.map(id => `'${id}'`).join(',')})`;
    const productResults = await api.runQueryOnCall({ options: { query: productQuery } });
    return productResults.data;
  };

  const updateDependentFields = async (rows) => {
    const productIds = new Set();
    rows.forEach(row => {
      const hasProductId = productIds.has(row.productId);
      if (!hasProductId) {
        productIds.add(row.productId);
      }
    });

    const relatedProducts = await fetchRelatedProducts(Array.from(productIds));

    console.log('relatedProducts', relatedProducts);

    const productMapping = {};
    relatedProducts.forEach(product => {
      productMapping[product.fieldId] = product;
    });

    rows = rows.map((row) => { // Dependent fields
      for (const field of fields) {
        if (field.relatedField) {
          if (field.relatedField.startsWith('product.')) {
            row[field.fieldId] = productMapping[row[field.relatedField]][field.relatedField.split('product.')[1]];
          } else {
            row[field.fieldId] = row[field.relatedField];
          }
        }
        if (field.defaultField && !row[field.defaultField]) {
          if (field.defaultField.startsWith('product.')) {
            row[field.fieldId] = productMapping[row[field.defaultField]][field.defaultField.split('product.')[1]];
          } else {
            row[field.fieldId] = row[field.defaultField];
          }
        }
      }

      return (updateDynamicFields(row));
    });

    return rows;
  };

  const updateDynamicFields = (itemData) => {
    const fieldList = fields.map(field => field.fieldId);
    itemData.hidden = false;
    itemData.fieldCompletion = 0;
    const fieldListWithValues = fieldList.filter(prop => (prop in itemData && (itemData[prop] || itemData[prop] === false || itemData[prop] === 0)));

    itemData.fieldCompletion = parseFloat(((fieldListWithValues.length / fieldList.length) * 100).toFixed(2));

    return itemData;
  };

  const handleCSVImport = (info) => {
    if (!info.file || info.file.type !== 'text/csv') {
      message.error('Upload a valid CSV file.');
      return;
    }
    Papa.parse(info.file, {
      header: true,
      skipEmptyLines: true,
      complete: (result) => {
        setFileUploaded(true);
        setCSVData(result.data);
        const propertiesSet = new Set();
        result.data.forEach(obj => {
          Object.keys(obj).forEach(prop => {
            propertiesSet.add(prop);
          });
        });
        const propertiesArray = Array.from(propertiesSet);
        const getMatchFieldByLabel = (item) => {
          const foundItem = fields.find((field) => field.label === item);
          return foundItem ? foundItem.fieldId : '';
        };

        const dataSource = propertiesArray.map((item, index) => ({
          csvField: item,
          itemField: getMatchFieldByLabel(item),
        }));
        if (dataSource.some(item => item.itemField === 'id')) {
          setIsForUpdate(true);
        }
        setTableMappingData(dataSource);

        const columns = [
          {
            title: 'CSV Columns',
            dataIndex: 'csvField',
            key: 'csvField',
          },
          {
            title: 'Item Fields',
            dataIndex: 'itemField',
            key: 'itemField',
            render: (val, record, index) => (
              <Select
                style={{ width: 400 }}
                status={(!val) ? 'error' : ''}
                placeholder="-Select-"
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
                defaultValue={val}
                options={fields.map(field => ({ label: field.label + ' [' + field.platform + ']', value: field.fieldId }))}
                onChange={(selectVal) => {
                  setTableMappingData(prevState =>
                    prevState.map(item =>
                      item.csvField === record.csvField
                        ? { ...item, itemField: selectVal }
                        : item
                    )
                  );
                }}
              />
            ),
          },
        ];
        setTableColumns(columns);
        // updateImportData(result.data, dataSource);
      },
    });
  };

  const handleSetUpdate = (value) => {
    if (value === true) {
      if (tableMappingData.some(item => item.itemField === 'id')) {
        setIsForUpdate(value);
      } else {
        message.error('The ID field should be added to the item fields.');
      }
    } else {
      setIsForUpdate(value);
    }
  };

  const updateImportData = () => {
    if (!tableMappingData.some(item => item.itemField === 'id') && isForUpdate) {
      message.error('To update the existing items, the ID field should be added to the item fields.');
      return;
    }

    const data = [];
    const columns = [];
    csvData.map((row, key) => {
      const dataItem = {};
      tableMappingData.map(mapRow => {
        if (mapRow.itemField) {
          const field = fields.find(field => field.fieldId === mapRow.itemField);
          if (field.fieldType === 'currency' || field.fieldType === 'integer' || field.fieldType === 'decimal' || field.fieldType === 'percent') {
            dataItem[mapRow.itemField] = parseFloat(row[mapRow.csvField]) || 0;
          } else if (field.fieldType === 'date') {
            dataItem[mapRow.itemField] = (row[mapRow.csvField]) ? new Date(row[mapRow.csvField]) : '';
          } else if (field.fieldType === 'select' && (field.relatedField === 'productId' || field.relatedField === 'productId')) {
            const selectedProduct = productsAsOptions.find(option =>
              option.netsuiteId === row[mapRow.csvField] || option.id === row[mapRow.csvField]
            );
            dataItem[mapRow.itemField] = selectedProduct ? selectedProduct.id : '';
          } else if (field.fieldType === 'select') {
            const fieldOptions = (field.fieldId === 'productId' || field.relatedField === 'productId' ? productsAsOptions : field.options);
            const selectedOption = fieldOptions.find(option => option.label === row[mapRow.csvField]);
            dataItem[mapRow.itemField] = (selectedOption) ? selectedOption.id : '';
          } else if (field.fieldType === 'checkbox') {
            dataItem[mapRow.itemField] = (row[mapRow.csvField] === 'Yes' || row[mapRow.csvField] === 'yes') ? true : false;
          } else if (field.fieldType === 'image') {
            dataItem[mapRow.itemField] = row[mapRow.csvField];
            // if (row[mapRow.csvField]) {
            //   axios.get(row[mapRow.csvField], { responseType: 'blob' }).then((response) => {
            //     console.log('response', response);
            //     const fileBlob = response.data;

            //     const imageName = generateRandomImageName();
            //     const storageRef = ref(storage, `item_images/${imageName}`);
            //     const uploadTask = uploadBytesResumable(storageRef, fileBlob);

            //     console.log('uploadTask', uploadTask);

            //     uploadTask.on(
            //       'state_changed',
            //       (snapshot) => {
            //         console.log(snapshot);
            //       },
            //       (error) => {
            //         console.error('Upload failed', error);
            //       },
            //       () => {
            //         getDownloadURL(uploadTask.snapshot.ref).then((downloadURL) => {
            //           setImportData((prevItems) => {
            //             const updatedItems = [...prevItems];
            //             updatedItems[key] = { ...updatedItems[key], [mapRow.itemField]: downloadURL };
            //             return updatedItems;
            //           });
            //         });
            //       }
            //     );
            //   });
            // }
          } else if (field.fieldType === 'text' || field.fieldType === 'longtext') {
            dataItem[mapRow.itemField] = (row[mapRow.csvField] !== null) ? row[mapRow.csvField] : '';
          } else {
            dataItem[mapRow.itemField] = row[mapRow.csvField];
          }

          if (!columns.some(col => col.dataIndex === mapRow.itemField)) {
            columns.push({
              title: mapRow.csvField,
              dataIndex: mapRow.itemField,
              key: mapRow.itemField,
              render: (val, record, index) => (
                <FormItem field={field} val={val} record={record} index={index} productsAsOptions={productsAsOptions} dataIndex={mapRow.itemField} setImportData={setImportData} />
              ),
            });
          }
        }
      });
      data.push(dataItem);
    });
    setImportData(data);
    setImportTableColumns(columns);
    setImportTableView(true);
  };

  const handleImportData = async () => {
    if (importData.length <= 0) {
      message.error('No item to import.');
      return;
    }
    try {
      console.log('importData', importData);
      message.info('Please wait while validating and importing items...');
      setStatusText('Processing...');

      let updatedItems = products;
      const rows = [];
      const successfulIds = new Set();

      importData.forEach((importItem, index) => {
        try {
          if (isForUpdate) {
            let itemData = products.find(data => data.id === importItem.id);
            itemData = itemData || { dateCreated: new Date() };
            rows.push({ id: importItem.id ?? uuidv4(), dateUpdated: new Date(), ...itemData, ...importItem });
          } else {
            if (!importItem.id) {
              importItem.id = uuidv4();
            }
            importItem.dateCreated = new Date();
            importItem.dateUpdated = new Date();
            rows.push(importItem);
          }
        } catch (error) {
          throw new Error(`Error processing row ${index + 1}: ${error.message}`);
        }
      });

      const updatedRows = await updateDependentFields(rows);

      setStatusText('Pushing to database...');
      const chunk = 500;
      const chunks = [];
      for (let i = 0; i < updatedRows.length; i += chunk) {
        chunks.push(updatedRows.slice(i, i + chunk));
      }

      try {
        for (let i = 0; i < chunks.length; i++) {
          setStatusText(`Processing chunk ${i + 1}/${chunks.length}`);
          await api.upsertOnCall({
            datasetId: 'items',
            table: viewType.toLowerCase() + 's',
            key: 'id',
            rows: chunks[i],
          });

          chunks[i].forEach(row => successfulIds.add(row.id));
        }

        setProducts(updatedItems);
        message.success(`Successfully imported ${rows.length} items.`);
        completeImport();
      } catch (error) {
        setStatusText('Error occurred. Attempting to rollback changes...');

        const rollbackRows = Array.from(successfulIds).map(id => ({
          id,
          deleted: true,
          dateUpdated: new Date()
        }));

        try {
          for (let i = 0; i < rollbackRows.length; i += chunk) {
            const rollbackChunk = rollbackRows.slice(i, i + chunk);
            await api.upsertOnCall({
              datasetId: 'items',
              table: viewType.toLowerCase() + 's',
              key: 'id',
              rows: rollbackChunk,
            });
          }

          throw new Error(
            `Import failed during chunk processing. All changes have been rolled back. ` +
            `Error details: ${error.message}`
          );
        } catch (rollbackError) {
          const successCount = successfulIds.size;
          throw new Error(
            `Import failed after ${successCount} items were imported. ` +
            `Rollback attempt also failed. ` +
            `Original error: ${error.message}. ` +
            `Rollback error: ${rollbackError.message}. `
          );
        }
      }
    } catch (error) {
      console.error('Import failed:', error);
      message.error(`Import failed: ${error.message}`);
      setStatusText(null);
      setFileUploaded(false);
    }
  };

  const completeImport = () => {
    setStatusText(null);
    setFileUploaded(false);
    message.success('CSV Import has been completed.');
  };

  return (
    <div>
      <Upload onChange={handleCSVImport} showUploadList={false} accept=".csv" action="/" beforeUpload={() => false}>
        <Button icon={<CloudUploadOutlined />}>Import Items</Button>
      </Upload>
      {(fileUploaded) ?
        (<Modal maskClosable={false} open={true} width={(!importTableView || statusText) ? 700 : 1200} footer={(!statusText) ? [
          (!importTableView) ? (
            <div>
              <Button onClick={() => setFileUploaded(false)} style={{ marginLeft: '5px' }}>
                Cancel
              </Button>
              <Button onClick={updateImportData} style={{ marginLeft: '5px' }} type="primary">
                Next
              </Button>
            </div>
          ) : (
            <div>
              <Button onClick={() => setImportTableView(false)} style={{ marginLeft: '5px' }}>
                Back
              </Button>
              <Popconfirm
                title="CSV Import"
                description="Do you want to import/update these data?"
                onConfirm={handleImportData}
                icon={<QuestionCircleOutlined />}
                okText="Confirm"
                cancelText="Cancel"
              >
                <Button style={{ marginLeft: '5px' }} type="primary">
                  {(isForUpdate) ? 'Update' : 'Import'}
                </Button>
              </Popconfirm>
            </div>
          )
        ] : []} onCancel={() => setFileUploaded(false)}>
          <Title level={3} style={{ marginTop: '0px' }}>CSV Import</Title>
          {(statusText) ? (<div style={{ marginTop: '20px' }}><Text italic>{statusText}</Text></div>) : (
            <div>
              <Title level={5} style={{ marginTop: '0px' }}>{(!importTableView) ? 'Fields Mapping:' : 'Data to Import:'}</Title>
              {(!importTableView) ? (
                <div style={{ overflow: 'auto' }}>
                  <Table
                    columns={tableColumns}
                    dataSource={tableMappingData}
                    pagination={false}
                    bordered
                  />
                  <Checkbox style={{ marginTop: '15px' }} onChange={(e) => {
                    handleSetUpdate(e.target.checked);
                  }} checked={isForUpdate}>Update Items</Checkbox>
                </div>
              ) : (
                <div style={{ overflow: 'auto' }}>
                  <Table
                    columns={importTableColumns}
                    dataSource={importData}
                    pagination={true}
                    bordered
                  />
                </div>
              )}
            </div>
          )}
        </Modal>
        ) : ''}
    </div>
  );
};
export default CSVImport;