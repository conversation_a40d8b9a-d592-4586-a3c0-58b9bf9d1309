{
    "version": "0.2.0",
    "configurations": [
        // {
        //     "name": "Launch Edge",
        //     "request": "launch",
        //     "type": "msedge",
        //     "url": "http://localhost:3000",
        //     "webRoot": "${workspaceFolder}",
        //     "sourceMaps": false
        // }
        {
          "type": "node",
          "request": "attach",
          "name": "Debug",
          "port": 9229,
          "restart": true,
          "skipFiles": ["<node_internals>/**"],
          "preLaunchTask": "start firebase emulator",
          "postDebugTask": "stop firebase emulator",
          "outFiles": ["${workspaceFolder}/**/*.js"]
        }
    ]
}