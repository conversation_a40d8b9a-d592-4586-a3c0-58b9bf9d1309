import React, { useState, useEffect } from 'react';
import { Card, Button, Form, Input, Table, message, Space, Modal } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SyncOutlined } from '@ant-design/icons';
import { getFunctions, httpsCallable } from 'firebase/functions';

const NetSuiteConfig = () => {
  const [form] = Form.useForm();
  const [datasets, setDatasets] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingDataset, setEditingDataset] = useState(null);
  const [loading, setLoading] = useState(false);

  const functions = getFunctions();
  const syncDataset = httpsCallable(functions, 'syncNetSuiteDataset');
  const validateConnection = httpsCallable(functions, 'validateNetSuiteConnection');

  useEffect(() => {
    // Load datasets from your storage (Firestore, etc.)
    loadDatasets();
  }, []);

  const loadDatasets = async () => {
    // TODO: Implement loading datasets from your storage
    // This is just a placeholder
    setDatasets([]);
  };

  const handleEdit = (dataset) => {
    setEditingDataset(dataset);
    form.setFieldsValue(dataset);
    setIsModalVisible(true);
  };

  const handleSync = async (dataset) => {
    try {
      setLoading(true);
      const result = await syncDataset({
        datasetId: dataset,
        bigqueryConfig: {
          // Add your BigQuery configuration here
          projectId: process.env.REACT_APP_BIGQUERY_PROJECT_ID,
          keyFilename: process.env.REACT_APP_BIGQUERY_KEY_FILENAME,
          dataset: process.env.REACT_APP_BIGQUERY_DATASET
        }
      });

      if (result.data.success) {
        message.success('Sync completed successfully');
      } else {
        throw new Error(result.data.error);
      }
    } catch (error) {
      message.error(`Failed to sync dataset: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (dataset) => {
    try {
      // TODO: Implement delete functionality
      message.success('Dataset deleted successfully');
    } catch (error) {
      message.error('Failed to delete dataset');
    }
  };

  const handleSubmit = async (values) => {
    try {
      // TODO: Implement save functionality
      message.success('Dataset saved successfully');
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('Failed to save dataset');
    }
  };

  const columns = [
    {
      title: 'Dataset Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'SuiteQL Query',
      dataIndex: 'query',
      key: 'query',
      ellipsis: true,
    },
    {
      title: 'BigQuery Table',
      dataIndex: 'bigqueryTable',
      key: 'bigqueryTable',
    },
    {
      title: 'Last Sync',
      dataIndex: 'lastSync',
      key: 'lastSync',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Button
            icon={<SyncOutlined />}
            onClick={() => handleSync(record)}
            loading={loading}
          />
          <Button
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          />
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card
        title="NetSuite Datasets"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingDataset(null);
              form.resetFields();
              setIsModalVisible(true);
            }}
          >
            Add Dataset
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={datasets}
          rowKey="id"
        />
      </Card>

      <Modal
        title={editingDataset ? 'Edit Dataset' : 'Add Dataset'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="Dataset Name"
            rules={[{ required: true, message: 'Please input dataset name!' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="query"
            label="SuiteQL Query"
            rules={[{ required: true, message: 'Please input SuiteQL query!' }]}
          >
            <Input.TextArea rows={4} />
          </Form.Item>

          <Form.Item
            name="bigqueryTable"
            label="BigQuery Table"
            rules={[{ required: true, message: 'Please input BigQuery table!' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Save
              </Button>
              <Button onClick={() => setIsModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default NetSuiteConfig; 