// src/utils/forecastDataTransform.js
// Utility helpers for transforming forecast data between
// (a) the DAILY row shape stored in BigQuery / Firebase
// (b) the MONTH-bucketed row shape consumed by the DemandPlan grid
//
// This keeps all business rules in one place.

// Map of month index → short name that matches headers generated in DemandPlan
const MONTH_NAMES = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

// Given a JS Date, return header label like "Jul 2024"
export function getMonthLabel(date) {
  return `${MONTH_NAMES[date.getMonth()]} ${date.getFullYear()}`;
}

// ---------------------------------------------------------------------------
// groupDailyToRowBuckets
// ---------------------------------------------------------------------------
// Params
//   dailyRows: array of rows coming from BigQuery (one per UPC / node / date)
//   next12Months: array<string> like ["Jul 2024", ...] (already computed in component)
// Returns array of bucketed rows ready for the grid.
export function groupDailyToRowBuckets(dailyRows, next12Months) {
  const bucketMap = new Map();

  for (const r of dailyRows) {
    const key = `${r.forecast_node}_${r.upc}`;
    let bucket = bucketMap.get(key);
    if (!bucket) {
      bucket = {
        // copy static fields (fallback to empty strings to keep col filters happy)
        forecastNode: r.forecast_node || '',
        upc: String(r.upc || ''),
        division: r.division,
        class: r.class,
        region: r.region,
        channel: r.channel,
        customer: r.customer,
        locked: r.locked ?? false,
        proxy_item: r.proxy_item || '',
        forecastMethod: r.forecast_method,
        productType: r.productType || '',
        lifestatus: r.lifestatus || '',
        productspecification: r.productspecification,
        productcategory: r.productcategory,
        productform: r.productform,
        image: r.image,
        launchdate: r.launchdate,
        enddate: r.enddate,
        color: r.color || '',
        size: r.size || '',
        baseprice: r.baseprice || 0,
        tas_30: r.tas_30 || 0,
        msrp_discount: r.msrp_discount,
        dailyQty: {}, // { '2024-07-01': 10 }
        monthTotals: {}, // { 'Jul 2024': 310 }
        originalValues: {},
        _changes: {},
        compoundKey: key
      };
      // pre-seed month totals with 0 for all 12 months so columns exist
      next12Months.forEach(m => {
        bucket.monthTotals[m] = 0;
        bucket.originalValues[m] = 0;
      });
      bucketMap.set(key, bucket);
    }

    // Accumulate into daily map
    const dateStr = r.date || r.forecast_date; // handle different naming conventions
    const qty = r.qty || 0;
    bucket.dailyQty[dateStr] = qty;

    // Add to month total
    const d = new Date(dateStr);
    const label = getMonthLabel(d);
    if (bucket.monthTotals[label] === undefined) bucket.monthTotals[label] = 0;
    bucket.monthTotals[label] += qty;
    if (bucket.originalValues[label] === undefined) bucket.originalValues[label] = 0;
    bucket.originalValues[label] += qty;
  }

  // For grid compatibility, also expose monthTotals on top-level fields
  const result = [];
  bucketMap.forEach(bucket => {
    next12Months.forEach(m => {
      bucket[m] = bucket.monthTotals[m] || 0;
    });
    result.push(bucket);
  });

  return result;
}

// ---------------------------------------------------------------------------
// flattenRowsToDaily
// ---------------------------------------------------------------------------
// Convert bucketed rows back to flat daily list for persistence.
export function flattenRowsToDaily(bucketRows) {
  const dailyRows = [];
  bucketRows.forEach(row => {
    const {
      forecastNode,
      upc,
      division,
      class: cls,
      region,
      channel,
      customer,
      locked,
      proxy_item,
      forecastMethod,
    } = row;

    Object.entries(row.dailyQty || {}).forEach(([dateStr, qty]) => {
      dailyRows.push({
        date: dateStr,
        forecast_node: forecastNode,
        upc,
        division,
        class: cls,
        region,
        channel,
        customer,
        locked,
        proxy_item,
        forecast_method: forecastMethod,
        qty
      });
    });
  });
  return dailyRows;
} 