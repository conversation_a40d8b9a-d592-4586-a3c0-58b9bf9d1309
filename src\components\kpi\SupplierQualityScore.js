import React, { useState, useEffect } from 'react';
import { Box, Typography, Grid, Paper, CircularProgress, Alert, LinearProgress } from '@mui/material';
import {
    ComposedChart, Line, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import { useFormatAxisTick } from '../../hooks/useFormatAxisTick';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import ChartExportWrapper from './ChartExportWrapper';

const SupplierQualityScore = () => {
    const [qualityData, setQualityData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const formatAxisTick = useFormatAxisTick();

    // Function to format numbers without currency
    const formatNumber = (value) => {
        return new Intl.NumberFormat('en-US').format(value);
    };

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const functions = getFunctions();
            const getSupplierQualityScore = httpsCallable(functions, 'getSupplierQualityScore');
            const getKPIGoalsForReport = httpsCallable(functions, 'getKPIGoalsForReport');

            const [qualityResult, goalsResult] = await Promise.all([
                getSupplierQualityScore(),
                getKPIGoalsForReport({ reportName: 'supplier-quality' })
            ]);

            setQualityData(qualityResult.data);

            if (goalsResult.data.success) {
                setKpiGoals(goalsResult.data.data);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            setError('Failed to fetch data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    const calculateQualityTrend = () => {
        if (!qualityData?.monthlyData || qualityData.monthlyData.length < 2) return 0;
        const lastTwo = qualityData.monthlyData.slice(-2);
        return parseFloat(lastTwo[1].qualityScore) - parseFloat(lastTwo[0].qualityScore);
    };

    const calculateDomains = () => {
        if (!qualityData?.monthlyData) return { scoreDomain: [0, 100], posDomain: [0, 1000] };

        const poValues = qualityData.monthlyData.map(item =>
            Math.max(item.totalPOs || 0, item.qualityPOs || 0)
        );

        const goalValue = kpiGoals?.['Supplier Quality Score']?.value;
        const maxScore = Math.max(...qualityData.monthlyData.map(item => parseFloat(item.qualityScore)), goalValue ? parseFloat(goalValue) : 0);

        return {
            scoreDomain: [95, Math.min(Math.ceil(maxScore * 1.1), 100)],
            posDomain: [0, Math.ceil(Math.max(...poValues) * 1.1)]
        };
    };

    const formatQualityScore = (score) => {
        const numScore = parseFloat(score);
        if (numScore < 100) {
            return Math.min(numScore, 99.99).toFixed(2);
        }
        return numScore === 100 ? "100.00" : "99.99";
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            // Debug log to see what data we're receiving
            console.log('Tooltip data:', data);

            // Calculate quality deliveries from data if not directly available
            const qualityDeliveries = data.totalOrders - data.qualityIssues;

            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                    <Typography variant="body2">{`Month: ${label}`}</Typography>
                    <Typography variant="body2" color="#2196f3">
                        {`Quality Score: ${formatQualityScore(data.qualityScore)}%`}
                    </Typography>
                    <Typography variant="body2" color="#4caf50">
                        {`Quality Deliveries: ${formatNumber(qualityDeliveries)}`}
                    </Typography>
                    <Typography variant="body2" color="#f44336">
                        {`Quality Issues: ${formatNumber(data.qualityIssues)}`}
                    </Typography>
                    <Typography variant="body2" color="#9e9e9e">
                        {`Total POs: ${formatNumber(data.totalOrders)}`}
                    </Typography>
                </Paper>
            );
        }
        return null;
    };

    const CustomYAxisTick = (props) => {
        const { x, y, payload } = props;
        return (
            <g transform={`translate(${x},${y})`}>
                <text x={0} y={0} dy={16} textAnchor="end" fill="#666">
                    {formatNumber(payload.value)}
                </text>
            </g>
        );
    };

    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: kpiGoals?.['Supplier Quality Score'],
        yAxisId: "left",
        styles: {
            stroke: "#2196f3",
            strokeDasharray: "3 3",
            label: {
                fill: "#2196f3",
                fontSize: 12,
                position: "right"
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Min",
            max: "Max"
        }
    });

    if (isLoading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Alert severity="error" sx={{ mt: 2 }}>
                {error}
            </Alert>
        );
    }

    if (!qualityData || !kpiGoals) return null;

    const { scoreDomain, posDomain } = calculateDomains();
    const qualityConfig = kpiGoals['Supplier Quality Score'];

    // Calculate max issues for progress bars
    const maxIssueCount = Math.max(...qualityData.qualityIssues.types.map(issue => issue.count));

    return (
        <ChartExportWrapper title="Supplier_Quality_Score">
            <Box sx={{ p: { xs: 2, sm: 3 }, mb: 3 }}>
                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <ResponsiveContainer width="100%" height={400}>
                        <ComposedChart
                            data={qualityData.monthlyData}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="monthYear" />
                            <YAxis
                                yAxisId="left"
                                domain={scoreDomain}
                                label={{
                                    value: 'Quality Score (%)',
                                    angle: -90,
                                    position: 'insideLeft'
                                }}
                            />
                            <YAxis
                                yAxisId="right"
                                orientation="right"
                                domain={posDomain}
                                label={{
                                    value: 'Number of POs',
                                    angle: 90,
                                    position: 'insideRight'
                                }}
                                tickFormatter={formatNumber}
                                tick={<CustomYAxisTick />}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            {goalReferenceLines}
                            <Bar yAxisId="right" dataKey="totalPOs" fill="#9e9e9e" name="Total POs" />
                            <Bar yAxisId="right" dataKey="qualityPOs" fill="#4caf50" name="Quality Deliveries" />
                            <Bar yAxisId="right" dataKey="qualityIssues" fill="#f44336" name="Quality Issues" />
                            <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="qualityScore"
                                stroke="#2196f3"
                                strokeWidth={2}
                                dot={{ r: 4 }}
                                name="Quality Score (%)"
                            />
                        </ComposedChart>
                    </ResponsiveContainer>

                    <Grid container spacing={2} sx={{ mt: 2 }}>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Overall Quality Score"
                                value={`${formatQualityScore(qualityData.overallQualityScore)}%`}
                                bgColor="#e3f2fd"
                                textColor="#2196f3"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Quality Deliveries"
                                value={formatNumber(qualityData.summary.qualityPOs)}
                                bgColor="#e8f5e9"
                                textColor="#4caf50"
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                            <KPICard
                                title="Quality Issues"
                                value={formatNumber(qualityData.summary.qualityIssues)}
                                bgColor="#ffebee"
                                textColor="#f44336"
                            />
                        </Grid>
                    </Grid>
                </Paper>

                <GoalStatusDisplay
                    currentValue={parseFloat(formatQualityScore(qualityData.overallQualityScore))}
                    goalConfig={qualityConfig}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculateQualityTrend()}
                    title="Supplier Quality Performance"
                    size="medium"
                />

                {qualityData.qualityIssues?.types?.length > 0 && (
                    <Paper elevation={3} sx={{ p: 3, mt: 3 }}>
                        <Typography variant="h6" gutterBottom>
                            Top Quality Issues
                        </Typography>
                        {qualityData.qualityIssues.types.slice(0, 5).map((issue, index) => (
                            <Box key={issue.type} sx={{ mb: 3 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                                        {`${index + 1}. ${issue.type}`}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        {`${issue.count} ${issue.count === 1 ? 'occurrence' : 'occurrences'}`}
                                    </Typography>
                                </Box>
                                <LinearProgress
                                    variant="determinate"
                                    value={(issue.count / maxIssueCount) * 100}
                                    sx={{
                                        height: 8,
                                        borderRadius: 4,
                                        backgroundColor: '#f5f5f5',
                                        '& .MuiLinearProgress-bar': {
                                            backgroundColor: index === 0 ? '#f44336' :
                                                index === 1 ? '#ff9800' :
                                                    index === 2 ? '#ffc107' :
                                                        index === 3 ? '#4caf50' : '#2196f3'
                                        }
                                    }}
                                />
                            </Box>
                        ))}
                    </Paper>
                )}
            </Box>
        </ChartExportWrapper>
    );
};

export default SupplierQualityScore;