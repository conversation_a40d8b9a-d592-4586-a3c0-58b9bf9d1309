exports.PRODUCT_TABLE = 'hj-reporting.items.products';
exports.VARIANTS_TABLE = 'hj-reporting.items.variants';

// #region PRODUCT FIELDS
const amazonProductFields = [];
const tiktokProductFields = [];
const shopifyProductFields = [];
const netsuiteProductFields = [
  { fieldId: 'netsuite_id', label: 'Internal ID', fieldType: 'text', group: 'primary', platform: "NETSUITE" },
  { fieldId: 'netsuite_abbreviation', label: 'Abbreviation', fieldType: 'text', group: 'primary', platform: "NETSUITE" },
  { fieldId: 'netsuite_created', label: 'CreatedAt', fieldType: 'date', display: 'inline', group: 'primary', platform: "NETSUITE", relatedField: 'dateCreated', note: 'Linked to dateCreated [PRIMARY]' },
  { fieldId: 'netsuite_externalid', label: 'External ID', fieldType: 'text', group: 'primary', platform: "NETSUITE" },
  { fieldId: 'netsuite_isInactive', label: 'Inactive', fieldType: 'checkbox', group: 'primary', platform: "NETSUITE" },
  { fieldId: 'netsuite_lastmodified', label: 'UpdatedAt', fieldType: 'date', display: 'inline', group: 'primary', platform: "NETSUITE", relatedField: 'dateUpdated', note: 'Linked to dateUpdated [PRIMARY]' },
  { fieldId: 'netsuite_name', label: 'Name', fieldType: 'text', group: 'primary', platform: "NETSUITE", defaultField: 'name', note: 'Default from name [PRIMARY]' },
  { fieldId: 'netsuite_owner', label: 'Owner', fieldType: 'text', group: 'primary', platform: "NETSUITE" },
  { fieldId: 'netsuite_recordId', label: 'Record ID', fieldType: 'text', group: 'primary', platform: "NETSUITE" },
  { fieldId: 'netsuite_scriptId', label: 'Script ID', fieldType: 'text', group: 'primary', platform: "NETSUITE" },
  { fieldId: 'netsuite_custrecord_sku_code', label: 'SKU Code', fieldType: 'text', group: 'primary', platform: "NETSUITE" },

  { fieldId: 'netsuite_cseg_brand', label: 'Brand', fieldType: 'text', group: 'branding', platform: "NETSUITE", defaultField: 'brand', note: 'Default from Brand [PRIMARY]' },
  { fieldId: 'netsuite_isFulfillable', label: 'Fulfillable', fieldType: 'checkbox', group: 'fulfillment', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem10', label: 'Case Quantity', fieldType: 'integer', group: 'size', platform: "NETSUITE" },
  { fieldId: 'netsuite_class', label: 'Class', fieldType: 'text', group: 'other', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem47', label: 'Cleaning Instructions', fieldType: 'text', group: 'description', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem_commodity_code', label: 'Commodity Code', fieldType: 'text', group: 'description', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem57', label: 'Container Cube', fieldType: 'decimal', group: 'size', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem58', label: 'Container Quantity', fieldType: 'integer', group: 'size', platform: "NETSUITE" },
  { fieldId: 'netsuite_createdDate', label: 'Created Date', fieldType: 'date', display: 'inline', group: 'other', platform: "NETSUITE", relatedField: 'dateCreated', note: 'Linked to dateCreated [PRIMARY]' },
  { fieldId: 'netsuite_custitem15', label: 'Full Carton Weight', fieldType: 'decimal', group: 'size', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem4', label: 'Height', fieldType: 'decimal', group: 'size', platform: "NETSUITE" },
  { fieldId: 'netsuite_weight', label: 'Item Weight', fieldType: 'decimal', group: 'size', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem5', label: 'Length', fieldType: 'decimal', group: 'size', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem36', label: 'Master Case Height', fieldType: 'decimal', group: 'size', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem13', label: 'Master Case Length', fieldType: 'decimal', group: 'size', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem35', label: 'Master Case Width', fieldType: 'decimal', group: 'size', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem42', label: 'Packaging', fieldType: 'text', group: 'description', platform: "NETSUITE" }, // Correct group?
  { fieldId: 'netsuite_custitem11', label: 'Pallet Quantity', fieldType: 'integer', group: 'size', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem6', label: 'Width', fieldType: 'decimal', group: 'size', platform: "NETSUITE" },
];

exports.defaultProductFields = [
  { fieldId: 'id', label: 'ID', fieldType: 'text', display: 'inline', group: 'primary', platform: "PRIMARY" },
  { fieldId: 'dateCreated', label: 'Date Created', fieldType: 'date', display: 'inline', group: 'primary', platform: "PRIMARY" },
  { fieldId: 'dateUpdated', label: 'Updated last', fieldType: 'date', display: 'inline', group: 'primary', platform: "PRIMARY" },
  { fieldId: 'fieldCompletion', label: 'Field Completion', fieldType: 'percent', display: 'inline', group: 'primary', platform: "PRIMARY" },
  { fieldId: 'name', label: 'Name', fieldType: 'text', mandatory: true, group: 'primary', platform: "PRIMARY" },
  { fieldId: 'brand', label: 'Brand', fieldType: 'text', mandatory: true, group: 'primary', platform: "PRIMARY" },
  { fieldId: 'hidden', label: 'Hidden', fieldType: 'checkbox', group: 'primary', platform: "PRIMARY" },
  { fieldId: 'images', label: 'Images', fieldType: 'multiimage', group: 'media', group: 'primary', platform: "PRIMARY" },
]
  .concat(amazonProductFields)
  .concat(tiktokProductFields)
  .concat(shopifyProductFields)
  .concat(netsuiteProductFields)
  ;
// #endregion

// #region VARIANT FIELDS
const amazonVariantFields = [
  // GENERAL
  { fieldId: "amazon_sellerSku", label: "Seller SKU", fieldType: "text", display: 'inline', group: "general", platform: "AMAZON", relatedField: 'sku', note: 'Linked to sku [PRIMARY]' },
  { fieldId: "amazon_brandName", label: "Brand Name", fieldType: "text", display: 'inline', group: "general", platform: "AMAZON", relatedField: "brand", note: 'Linked to brand [PRIMARY]' },
  { fieldId: "amazon_updateDelete", label: "Update Delete", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_productExemptionReason", label: "Product Exemption Reason", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_productName", label: "Product Name", fieldType: "text", group: "general", platform: "AMAZON", defaultField: "name", note: 'Default from name [PRIMARY]' },
  { fieldId: "amazon_productDescription", label: "Product Description", fieldType: "text", group: "general", platform: "AMAZON", defaultField: 'description', note: 'Default from description [PRIMARY]' },
  { fieldId: "amazon_manufacturer", label: "Manufacturer", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_amazonProductId", label: "Product ID", fieldType: "text", display: 'inline', group: "general", platform: "AMAZON", relatedField: 'productId', note: 'Linked to productId [PRIMARY]' },
  { fieldId: "amazon_manufacturerPartNumber", label: "Manufacturer Part Number", fieldType: "text", display: 'inline', group: "general", platform: "AMAZON", relatedField: 'mpn', note: 'Linked to mpn [PRIMARY]' },
  { fieldId: "amazon_productIdType", label: "Product ID Type", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_itemTypeKeyword", label: "Item Type Keyword", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_model", label: "Model", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_modelName", label: "Model Name", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_modelYear", label: "Model Year", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_careInstructions", label: "Care Instructions", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_yourPrice", label: "Your Price", fieldType: "text", group: "general", platform: "AMAZON", defaultField: 'basePrice', note: 'Default from basePrice [PRIMARY]' },
  { fieldId: "amazon_quantity", label: "Quantity", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_mainImageUrl", label: "Main Image URL", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_otherImageUrl1", label: "Other Image URL1", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_otherImageUrl2", label: "Other Image URL2", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_otherImageUrl3", label: "Other Image URL3", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_otherImageUrl4", label: "Other Image URL4", fieldType: "text", group: "general", platform: "AMAZON" },
  { fieldId: "amazon_otherImageUrl5", label: "Other Image URL5", fieldType: "text", group: "general", platform: "AMAZON" },
  // { fieldId: "amazon_otherImageUrl6", label: "Other Image URL6", fieldType: "text", group: "general", platform: "AMAZON" },
  // { fieldId: "amazon_otherImageUrl7", label: "Other Image URL7", fieldType: "text", group: "general", platform: "AMAZON" },
  // { fieldId: "amazon_otherImageUrl8", label: "Other Image URL8", fieldType: "text", group: "general", platform: "AMAZON" },
  // { fieldId: "amazon_swatchImageUrl", label: "Swatch Image URL", fieldType: "text", group: "general", platform: "AMAZON" },

  // VARIATION
  // { fieldId: "amazon_parentage", label: "Parentage", fieldType: "text", group: "variation", platform: "AMAZON" }, // ? Parentage with variations?
  // { fieldId: "amazon_parentSku", label: "Parent SKU", fieldType: "text", group: "variation", platform: "AMAZON" },
  // { fieldId: "amazon_relationshipType", label: "Relationship Type", fieldType: "text", group: "variation", platform: "AMAZON" },
  // { fieldId: "amazon_variationTheme", label: "Variation Theme", fieldType: "text", group: "variation", platform: "AMAZON" },
  // { fieldId: "amazon_packageLevel", label: "Package Level", fieldType: "text", group: "variation", platform: "AMAZON" },
  // { fieldId: "amazon_packageContainsQuantity", label: "Package Contains Quantity", fieldType: "text", group: "variation", platform: "AMAZON" },
  // { fieldId: "amazon_packageContainsIdentifier", label: "Package Contains Identifier", fieldType: "text", group: "variation", platform: "AMAZON" },

  // DISCOVERY
  { fieldId: "amazon_keyProductFeatures", label: "Key Product Features", fieldType: "text", group: "discovery", platform: "AMAZON" },
  { fieldId: "amazon_searchTerms", label: "Search Terms", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_numberOfPieces", label: "Number of Pieces", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_scent", label: "Scent", fieldType: "text", group: "discovery", platform: "AMAZON" },
  { fieldId: "amazon_includedComponents", label: "Included Components", fieldType: "text", group: "discovery", platform: "AMAZON" },
  { fieldId: "amazon_color", label: "Color", fieldType: "text", group: "discovery", platform: "AMAZON" },
  { fieldId: "amazon_colorMap", label: "Color Map", fieldType: "text", group: "discovery", platform: "AMAZON" },
  { fieldId: "amazon_size", label: "Size", fieldType: "text", group: "discovery", platform: "AMAZON" },
  { fieldId: "amazon_materialType", label: "Material Type", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_styleName", label: "Style Name", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_theme", label: "Theme", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_powerSource", label: "Power Source", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_voltage", label: "Voltage", fieldType: "text", group: "discovery", platform: "AMAZON" },
  { fieldId: "amazon_additionalFeatures", label: "Additional Features", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_runtime", label: "Runtime", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_soundLevel", label: "Sound Level", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_noiseLevelUnitOfMeasure", label: "Noise Level Unit Of Measure", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_counterdepth", label: "CounterDepth", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_recommendedUsesForProduct", label: "Recommended Uses For Product", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_itemTypeName", label: "Item Type Name", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_occasionType", label: "Occasion Type", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_runtimeUnit", label: "Runtime Unit", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_seasonAndCollectionYear", label: "Season and collection year", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_lengthRange", label: "Length Range", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_mountType", label: "Mount Type", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_isAssemblyRequired", label: "Is Assembly Required", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_numberOfBoxes", label: "Number of Boxes", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_compatibleCounterDepthUnitOfMeasure", label: "Compatible Counter Depth Unit Of Measure", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_voltageUnitOfMeasure", label: "Voltage Unit Of Measure", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_design", label: "Design", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_horsepower", label: "Horsepower", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_horsepowerUnitOfMeasure", label: "Horsepower Unit Of Measure", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_controllerType", label: "Controller Type", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_maximumPressure", label: "Maximum Pressure", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_maximumOperatingPressureUnitOfMeasure", label: "Maximum Operating Pressure Unit Of Measure", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_handleLeverPlacement", label: "Handle Lever Placement", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_lightSourceType", label: "Light Source Type", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_brightnessUnitOfMeasure", label: "Brightness Unit Of Measure", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_speedUnitOfMeasure", label: "Speed Unit Of Measure", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_torque", label: "Torque", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_itemTorqueUnitOfMeasure", label: "Item Torque Unit Of Measure", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_insulationResistanceUnitOfMeasure", label: "Insulation Resistance Unit Of Measure", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_numberOfTeeth", label: "Number of Teeth", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_oemPartNumber", label: "OEM Part Number", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_shankType", label: "Shank Type", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_numberOfSets", label: "Number of Sets", fieldType: "text", group: "discovery", platform: "AMAZON" },
  // { fieldId: "amazon_wattageUnitOfMeasure", label: "Wattage Unit of Measure", fieldType: "text", group: "discovery", platform: "AMAZON" },

  // PRODUCT ENRICHMENT
  // { fieldId: "amazon_finishTypes", label: "Finish Types", fieldType: "text", group: "enrichment", platform: "AMAZON" },
  // { fieldId: "amazon_bladeMaterialType", label: "Blade Material Type", fieldType: "text", group: "enrichment", platform: "AMAZON" },
  // { fieldId: "amazon_numberOfSpeeds", label: "Number of Speeds", fieldType: "text", group: "enrichment", platform: "AMAZON" },
  // { fieldId: "amazon_energyGuideCapacity", label: "Energy Guide Capacity", fieldType: "text", group: "enrichment", platform: "AMAZON" },
  // { fieldId: "amazon_energyGuideCostDisclosureStatement", label: "Energy Guide Cost Disclosure Statement", fieldType: "text", group: "enrichment", platform: "AMAZON" },
  // { fieldId: "amazon_certifyingAuthorityName", label: "Certifying Authority Name", fieldType: "text", group: "enrichment", platform: "AMAZON" },
  // { fieldId: "amazon_geographicJurisdiction", label: "Geographic Jurisdiction", fieldType: "text", group: "enrichment", platform: "AMAZON" },
  // { fieldId: "amazon_flashPointUnitOfMeasure", label: "flashPointUnitOfMeasure", fieldType: "text", group: "enrichment", platform: "AMAZON" },
  // { fieldId: "amazon_energyGuideAnnualOperatingCostValue", label: "Energy Guide Annual Operating Cost Value", fieldType: "text", group: "enrichment", platform: "AMAZON" },
  // { fieldId: "amazon_connectivityProtocol", label: "Connectivity Protocol", fieldType: "text", group: "enrichment", platform: "AMAZON" },
  // { fieldId: "amazon_itemPitchUnitOfMeasure", label: "Item Pitch Unit Of Measure", fieldType: "text", group: "enrichment", platform: "AMAZON" },
  // { fieldId: "amazon_isTheItemOemAuthorized", label: "Is the item OEM authorized?", fieldType: "checkbox", group: "enrichment", platform: "AMAZON" },
  // { fieldId: "amazon_bottleType", label: "Bottle Type", fieldType: "text", group: "enrichment", platform: "AMAZON" },
  // { fieldId: "amazon_capType", label: "Cap Type", fieldType: "text", group: "enrichment", platform: "AMAZON" },

  // DIMENSIONS
  // { fieldId: "amazon_itemDisplayDepthUnitOfMeasure", label: "Item Display Depth Unit Of Measure", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_shape", label: "Shape", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_displayLengthUnitOfMeasure", label: "Display Length Unit Of Measure", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_itemDisplayWidthUnitOfMeasure", label: "Item Display Width Unit Of Measure", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_itemDisplayHeightUnitOfMeasure", label: "Item Display Height Unit Of Measure", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_itemDisplayDiameterUnitOfMeasure", label: "Item Display Diameter Unit Of Measure", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_itemDisplayLength", label: "Item Display Length", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_itemDisplayWidth", label: "Item Display Width", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_itemDisplayHeight", label: "Item Display Height", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_itemDisplayWeight", label: "Item Display Weight", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_itemDisplayWeightUnitOfMeasure", label: "Item Display Weight Unit Of Measure", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  { fieldId: "amazon_unitOfMeasurePerUnitPricing", label: "Unit of Measure (Per Unit Pricing)", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  { fieldId: "amazon_unitCountPerUnitPricing", label: "Unit Count (Per Unit Pricing)", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  { fieldId: "amazon_capacityUnitOfMeasure", label: "Capacity Unit Of Measure", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  { fieldId: "amazon_capacity", label: "Capacity", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_sizeMap", label: "Size Map", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_widthRange", label: "Width Range", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_maximumWeightRecommendationUnitOfMeasure", label: "Maximum Weight Recommendation Unit Of Measure", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_liquidVolume", label: "Liquid Volume", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_liquidVolumeUnit", label: "Liquid Volume Unit", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  { fieldId: "amazon_itemWidthWidestPoint", label: "Item Width Widest Point", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  { fieldId: "amazon_itemWidthUnit", label: "Item Width Unit", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  { fieldId: "amazon_itemHeightBaseToTop", label: "Item Height Base to Top", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  { fieldId: "amazon_itemHeightUnit", label: "Item Height Unit", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_systemOfMeasurement", label: "System of Measurement", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_outsideDiameterDerived", label: "Outside Diameter Derived", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_itemDiameterUnitOfMeasure", label: "Item Diameter Unit Of Measure", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_manufacturerRecommendedMaximumWeight", label: "Manufacturer Recommended Maximum Weight", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_baseDiameterUnitOfMeasure", label: "Base Diameter Unit Of Measure", fieldType: "text", group: "dimensions", platform: "AMAZON" },
  // { fieldId: "amazon_baseDiameter", label: "Base Diameter", fieldType: "text", group: "dimensions", platform: "AMAZON" },

  // FULFILLMENT
  { fieldId: "amazon_fulfillmentCenterId", label: "Fulfillment Center ID", fieldType: "text", group: "fulfillment", platform: "AMAZON" },
  { fieldId: "amazon_packageHeight", label: "Package Height", fieldType: "text", group: "fulfillment", platform: "AMAZON" },
  { fieldId: "amazon_packageWidth", label: "Package Width", fieldType: "text", group: "fulfillment", platform: "AMAZON" },
  { fieldId: "amazon_packageLength", label: "Package Length", fieldType: "text", group: "fulfillment", platform: "AMAZON" },
  { fieldId: "amazon_packageLengthUnitOfMeasure", label: "Package Length Unit Of Measure", fieldType: "text", group: "fulfillment", platform: "AMAZON" },
  { fieldId: "amazon_packageWeight", label: "Package Weight", fieldType: "text", group: "fulfillment", platform: "AMAZON" },
  { fieldId: "amazon_packageWeightUnitOfMeasure", label: "Package Weight Unit Of Measure", fieldType: "text", group: "fulfillment", platform: "AMAZON" },
  { fieldId: "amazon_packageHeightUnitOfMeasure", label: "Package Height Unit Of Measure", fieldType: "text", group: "fulfillment", platform: "AMAZON" },
  // { fieldId: "amazon_isFragile", label: "Is Fragile?", fieldType: "checkbox", group: "fulfillment", platform: "AMAZON" },
  { fieldId: "amazon_packageWidthUnitOfMeasure", label: "Package Width Unit Of Measure", fieldType: "text", group: "fulfillment", platform: "AMAZON" },

  // COMPLIANCE
  // { fieldId: "amazon_complianceRegulationType", label: "Compliance Regulation Type", fieldType: "select", options: ["Type1", "Type2", "Type3"], group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_regulatoryIdentification", label: "Regulatory Identification", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_manufacturerWarrantyDescription", label: "Manufacturer Warranty Description", fieldType: "text", group: "compliance", platform: "AMAZON" },
  { fieldId: "amazon_cpsiaWarning", label: "Cpsia Warning", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_fabricType", label: "Fabric Type", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_executiveNumber", label: "Please provide the Executive Number (EO) required for sale into California.", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_expirationDateOfEoNumber", label: "Please provide the expiration date of the EO Number.", fieldType: "date", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_volume", label: "Volume", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_itemVolumeUnitOfMeasure", label: "itemVolumeUnitOfMeasure", fieldType: "text", group: "compliance", platform: "AMAZON" },
  { fieldId: "amazon_countryRegionOfOrigin", label: "Country/Region of Origin", fieldType: "text", group: "compliance", platform: "AMAZON" },
  { fieldId: "amazon_batteriesAreIncluded", label: "Batteries are Included", fieldType: "checkbox", group: "compliance", platform: "AMAZON" },
  { fieldId: "amazon_itemWeight", label: "Item Weight", fieldType: "text", group: "compliance", platform: "AMAZON" },
  { fieldId: "amazon_usesBatteries", label: "Is this product a battery or does it utilize batteries?", fieldType: "checkbox", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_batteryTypeSize", label: "Battery type/size", fieldType: "text", group: "compliance", platform: "AMAZON" },
  { fieldId: "amazon_itemWeightUnit", label: "Item Weight Unit", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_numberOfBatteries", label: "Number of batteries", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_wattHoursPerBattery", label: "Watt hours per battery", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_lithiumBatteryPackaging", label: "Lithium Battery Packaging", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_lithiumContentGrams", label: "Lithium content (grams)", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_numberOfLithiumIonCells", label: "Number of Lithium-ion Cells", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_numberOfLithiumMetalCells", label: "Number of Lithium Metal Cells", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_batteryComposition", label: "Battery composition", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_batteryWeightGrams", label: "Battery weight (grams)", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_batteryWeightUnit", label: "Battery Weight Unit", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_lithiumBatteryEnergyContentUnit", label: "Lithium Battery Energy Content Unit", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_lithiumBatteryWeightUnit", label: "Lithium Battery Weight Unit", fieldType: "text", group: "compliance", platform: "AMAZON" },
  { fieldId: "amazon_applicableDangerousGoodsRegulations", label: "Applicable Dangerous Goods Regulations", fieldType: "select", options: ["Regulation1", "Regulation2", "Regulation3"], group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_unNumber", label: "UN number", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_safetyDataSheetSdsUrl", label: "Safety Data Sheet (SDS) URL", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_containsLiquidContents", label: "Contains Liquid Contents?", fieldType: "checkbox", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_regulatoryOrganizationName", label: "Regulatory Organization Name", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_complianceCertificationStatus", label: "Compliance Certification Status", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_flashPointC", label: "Flash point (°C)?", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_certificationDateOfIssue", label: "Certification Date of Issue", fieldType: "date", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_externalTestingCertification", label: "External Testing Certification", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_legalComplianceCertification", label: "Legal Compliance Certification", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_categorizationGhsPictograms", label: "Categorization/GHS pictograms (select all that apply)", fieldType: "select", options: ["Option1", "Option2", "Option3"], group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_californiaProposition_65WarningType", label: "California Proposition 65 Warning Type", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_californiaProposition_65ChemicalNames", label: "California Proposition 65 Chemical Names", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_additionalChemicalName1", label: "Additional Chemical Name1", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_additionalChemicalName2", label: "Additional Chemical Name2", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_additionalChemicalName3", label: "Additional Chemical Name3", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "additionalChemicalName4", label: "Additional Chemical Name4", fieldType: "text", group: "compliance", platform: "AMAZON" },
  { fieldId: "amazon_pesticideMarking", label: "Pesticide Marking", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_pesticideRegistrationStatus", label: "Pesticide Registration Status", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_pesticideCertificationNumber", label: "Pesticide Certification Number", fieldType: "text", group: "compliance", platform: "AMAZON" },
  { fieldId: "amazon_radioFrequencyEmissionAuthorizationStatus", label: "Radio Frequency Emission & Authorization Status", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_sdocContactEmailAddress", label: "SDoC Contact Email Address", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_sdocContactUsPhoneNumber", label: "SDOC Contact US Phone Number", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_sdocContactName", label: "SDoC Contact Name", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_fccId", label: "FCC ID", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_sdocContactUsMailingAddress", label: "SDoC Contact US Mailing Address", fieldType: "text", group: "compliance", platform: "AMAZON" },
  // { fieldId: "amazon_specificUsesForProduct", label: "Specific Uses For Product", fieldType: "text", group: "compliance", platform: "AMAZON" },

  // OFFER
  // { fieldId: "amazon_shippingTemplate", label: "Shipping-Template", fieldType: "text", group: "offer", platform: "AMAZON" },
  { fieldId: "amazon_currency", label: "Currency", fieldType: "text", group: "offer", platform: "AMAZON" },
  { fieldId: "amazon_listPrice", label: "List Price", fieldType: "text", group: "offer", platform: "AMAZON", defaultField: "basePrice", note: "Default from basePrice [PRIMARY]" },
  // { fieldId: "amazon_minimumAdvertisedPrice", label: "Minimum Advertised Price", fieldType: "text", group: "offer", platform: "AMAZON" },
  // { fieldId: "amazon_launchDate", label: "Launch Date", fieldType: "date", group: "offer", platform: "AMAZON" },
  // { fieldId: "amazon_releaseDate", label: "Release Date", fieldType: "date", group: "offer", platform: "AMAZON" },
  { fieldId: "amazon_itemCondition", label: "Item Condition", fieldType: "text", group: "offer", platform: "AMAZON" },
  // { fieldId: "amazon_restockDate", label: "Restock Date", fieldType: "date", group: "offer", platform: "AMAZON" },
  // { fieldId: "amazon_handlingTime", label: "Handling Time", fieldType: "text", group: "offer", platform: "AMAZON" },
  // { fieldId: "amazon_offerConditionNote", label: "Offer Condition Note", fieldType: "text", group: "offer", platform: "AMAZON" },
  { fieldId: "amazon_productTaxCode", label: "Product Tax Code", fieldType: "text", group: "offer", platform: "AMAZON" },
  // { fieldId: "amazon_salePrice", label: "Sale Price", fieldType: "text", group: "offer", platform: "AMAZON" },
  // { fieldId: "amazon_saleStartDate", label: "Sale Start Date", fieldType: "date", group: "offer", platform: "AMAZON" },
  // { fieldId: "amazon_saleEndDate", label: "Sale End Date", fieldType: "date", group: "offer", platform: "AMAZON" },
  { fieldId: "amazon_packageQuantity", label: "Package Quantity", fieldType: "text", group: "offer", platform: "AMAZON" },
  // { fieldId: "amazon_offeringCanBeGiftMessaged", label: "Offering Can Be Gift Messaged", fieldType: "checkbox", group: "offer", platform: "AMAZON" },
  // { fieldId: "amazon_isGiftWrapAvailable", label: "Is Gift Wrap Available", fieldType: "checkbox", group: "offer", platform: "AMAZON" },
  // { fieldId: "amazon_maxOrderQuantity", label: "Max Order Quantity", fieldType: "text", group: "offer", platform: "AMAZON" },
  { fieldId: "amazon_numberOfItems", label: "Number of Items", fieldType: "text", group: "offer", platform: "AMAZON" },
  // { fieldId: "amazon_offerStartDate", label: "Offer Start Date", fieldType: "date", group: "offer", platform: "AMAZON" },
  // { fieldId: "amazon_offerEndDate", label: "Offer End Date", fieldType: "date", group: "offer", platform: "AMAZON" },
  { fieldId: "amazon_isProductExpirable", label: "Is Product Expirable", fieldType: "checkbox", group: "offer", platform: "AMAZON" },

  // B2B
  // { fieldId: "amazon_businessPrice", label: "Business Price", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_quantityPriceType", label: "Quantity Price Type", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_quantityLowerBound_1", label: "Quantity Lower Bound 1", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_quantityPrice_1", label: "Quantity Price 1", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_quantityLowerBound_2", label: "Quantity Lower Bound 2", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_quantityPrice_2", label: "Quantity Price 2", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_quantityLowerBound_3", label: "Quantity Lower Bound 3", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_quantityPrice_3", label: "Quantity Price 3", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_quantityLowerBound_4", label: "Quantity Lower Bound 4", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_quantityPrice_4", label: "Quantity Price 4", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_quantityLowerBound_5", label: "Quantity Lower Bound 5", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_quantityPrice_5", label: "Quantity Price 5", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_nationalStockNumber", label: "National Stock Number", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_progressiveDiscountType", label: "Progressive Discount Type", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_unitedNationsStandardProductsAndServicesCode", label: "United Nations Standard Products and Services Code", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_progressiveDiscountLowerBound_1", label: "Progressive Discount Lower Bound 1", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_progressiveDiscountValue_1", label: "Progressive Discount Value 1", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_pricingAction", label: "Pricing Action", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_progressiveDiscountLowerBound_2", label: "Progressive Discount Lower Bound 2", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_progressiveDiscountValue_2", label: "Progressive Discount Value 2", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_progressiveDiscountLowerBound_3", label: "Progressive Discount Lower Bound 3", fieldType: "text", group: "b2b", platform: "AMAZON" },
  // { fieldId: "amazon_progressiveDiscountValue_3", label: "Progressive Discount Value 3", fieldType: "text", group: "b2b", platform: "AMAZON" },

  // AGE RANGE
  { fieldId: "amazon_ageRangeDescription", label: "Age Range Description", fieldType: "text", group: "age", platform: "AMAZON" }
];
const tiktokVariantFields = [];
const shopifyVariantFields = [
  // GENERAL
  { fieldId: "shopify_handle", label: "Handle", fieldType: "text", group: "general", platform: "SHOPIFY" },
  { fieldId: "shopify_title", label: "Title", fieldType: "text", group: "general", platform: "SHOPIFY", defaultField: "name", note: "Default from name [PRIMARY]" },
  { fieldId: "shopify_bodyHtml", label: "Body (HTML)", fieldType: "text", group: "general", platform: "SHOPIFY" },
  { fieldId: "shopify_vendor", label: "Vendor", fieldType: "text", group: "general", platform: "SHOPIFY" },
  { fieldId: "shopify_productCategory", label: "Product Category", fieldType: "text", group: "general", platform: "SHOPIFY" },
  { fieldId: "shopify_type", label: "Type", fieldType: "text", group: "general", platform: "SHOPIFY" },
  { fieldId: "shopify_tags", label: "Tags", fieldType: "text", group: "general", platform: "SHOPIFY" },
  { fieldId: "shopify_published", label: "Published", fieldType: "checkbox", group: "general", platform: "SHOPIFY" },
  { fieldId: "shopify_option1Name", label: "Option1 Name", fieldType: "text", group: "general", platform: "SHOPIFY" },
  { fieldId: "shopify_option1Value", label: "Option1 Value", fieldType: "text", group: "general", platform: "SHOPIFY" },
  { fieldId: "shopify_option2Name", label: "Option2 Name", fieldType: "text", group: "general", platform: "SHOPIFY" },
  { fieldId: "shopify_option2Value", label: "Option2 Value", fieldType: "text", group: "general", platform: "SHOPIFY" },
  { fieldId: "shopify_option3Name", label: "Option3 Name", fieldType: "text", group: "general", platform: "SHOPIFY" },
  { fieldId: "shopify_option3Value", label: "Option3 Value", fieldType: "text", group: "general", platform: "SHOPIFY" },
  { fieldId: "shopify_giftCard", label: "Gift Card", fieldType: "checkbox", group: "general", platform: "SHOPIFY" },

  // IMAGES
  { fieldId: "shopify_imageSrc", label: "Image Src", fieldType: "text", group: "images", platform: "SHOPIFY" },
  { fieldId: "shopify_imagePosition", label: "Image Position", fieldType: "text", group: "images", platform: "SHOPIFY" },
  { fieldId: "shopify_imageAltText", label: "Image Alt Text", fieldType: "text", group: "images", platform: "SHOPIFY" },

  // SEO
  { fieldId: "shopify_seoTitle", label: "SEO Title", fieldType: "text", group: "seo", platform: "SHOPIFY" },
  { fieldId: "shopify_seoDescription", label: "SEO Description", fieldType: "longtext", group: "seo", platform: "SHOPIFY", defaultField: "description", note: "Default from description [PRIMARY]" },
  { fieldId: "shopify_googleShoppingGoogleProductCategory", label: "Google Shopping / Google Product Category", fieldType: "text", group: "seo", platform: "SHOPIFY" },
  { fieldId: "shopify_googleShoppingGender", label: "Google Shopping / Gender", fieldType: "text", group: "seo", platform: "SHOPIFY" },
  { fieldId: "shopify_googleShoppingAgeGroup", label: "Google Shopping / Age Group", fieldType: "text", group: "seo", platform: "SHOPIFY" },
  { fieldId: "shopify_googleShoppingMpn", label: "Google Shopping / MPN", fieldType: "text", group: "seo", platform: "SHOPIFY", defaultField: "mpn", note: "Default from mpn [PRIMARY]" },
  { fieldId: "shopify_googleShoppingAdwordsGrouping", label: "Google Shopping / AdWords Grouping", fieldType: "text", group: "seo", platform: "SHOPIFY" },
  { fieldId: "shopify_googleShoppingAdwordsLabels", label: "Google Shopping / AdWords Labels", fieldType: "text", group: "seo", platform: "SHOPIFY" },
  { fieldId: "shopify_googleShoppingCondition", label: "Google Shopping / Condition", fieldType: "text", group: "seo", platform: "SHOPIFY" },
  { fieldId: "shopify_googleShoppingCustomProduct", label: "Google Shopping / Custom Product", fieldType: "text", group: "seo", platform: "SHOPIFY" },
  { fieldId: "shopify_googleShoppingCustomLabel_0", label: "Google Shopping / Custom Label 0", fieldType: "text", group: "seo", platform: "SHOPIFY" },
  { fieldId: "shopify_googleShoppingCustomLabel_1", label: "Google Shopping / Custom Label 1", fieldType: "text", group: "seo", platform: "SHOPIFY" },
  { fieldId: "shopify_googleShoppingCustomLabel_2", label: "Google Shopping / Custom Label 2", fieldType: "text", group: "seo", platform: "SHOPIFY" },
  { fieldId: "shopify_googleShoppingCustomLabel_3", label: "Google Shopping / Custom Label 3", fieldType: "text", group: "seo", platform: "SHOPIFY" },
  { fieldId: "shopify_googleShoppingCustomLabel_4", label: "Google Shopping / Custom Label 4", fieldType: "text", group: "seo", platform: "SHOPIFY" },

  // VARIANTS
  { fieldId: "shopify_variantSku", label: "Variant SKU", fieldType: "text", group: "variants", platform: "SHOPIFY", defaultField: "sku", note: "Default from sku [PRIMARY]" },
  { fieldId: "shopify_variantGrams", label: "Variant Grams", fieldType: "text", group: "variants", platform: "SHOPIFY" },
  { fieldId: "shopify_variantInventoryTracker", label: "Variant Inventory Tracker", fieldType: "text", group: "variants", platform: "SHOPIFY" },
  { fieldId: "shopify_variantInventoryQty", label: "Variant Inventory Qty", fieldType: "text", group: "variants", platform: "SHOPIFY" },
  { fieldId: "shopify_variantInventoryPolicy", label: "Variant Inventory Policy", fieldType: "text", group: "variants", platform: "SHOPIFY" },
  { fieldId: "shopify_variantFulfillmentService", label: "Variant Fulfillment Service", fieldType: "text", group: "variants", platform: "SHOPIFY" },
  { fieldId: "shopify_variantPrice", label: "Variant Price", fieldType: "text", group: "variants", platform: "SHOPIFY", defaultField: "basePrice", note: "Default from basePrice [PRIMARY]" },
  { fieldId: "shopify_variantCompareAtPrice", label: "Variant Compare At Price", fieldType: "text", group: "variants", platform: "SHOPIFY" },
  { fieldId: "shopify_variantRequiresShipping", label: "Variant Requires Shipping", fieldType: "checkbox", group: "variants", platform: "SHOPIFY" },
  { fieldId: "shopify_variantTaxable", label: "Variant Taxable", fieldType: "checkbox", group: "variants", platform: "SHOPIFY" },
  { fieldId: "shopify_variantBarcode", label: "Variant Barcode", fieldType: "text", group: "variants", platform: "SHOPIFY" },
  { fieldId: "shopify_variantImage", label: "Variant Image", fieldType: "text", group: "variants", platform: "SHOPIFY" },
  { fieldId: "shopify_variantWeightUnit", label: "Variant Weight Unit", fieldType: "text", group: "variants", platform: "SHOPIFY" },
  { fieldId: "shopify_variantTaxCode", label: "Variant Tax Code", fieldType: "text", group: "variants", platform: "SHOPIFY" },

  // PRICING
  { fieldId: "shopify_costPerItem", label: "Cost per item", fieldType: "text", group: "pricing", platform: "SHOPIFY" },
  { fieldId: "shopify_priceInternational", label: "Price / International", fieldType: "text", group: "pricing", platform: "SHOPIFY" },
  { fieldId: "shopify_compareAtPriceInternational", label: "Compare At Price / International", fieldType: "text", group: "pricing", platform: "SHOPIFY" },

  // STATUS
  { fieldId: "shopify_shopifyStatus", label: "Shopify Status", fieldType: "text", group: "status", platform: "SHOPIFY" }
];
const netsuiteVariantFields = [
  { fieldId: 'netsuite_costingMethod', label: 'Costing Method', fieldType: 'text', group: 'pricing', platform: "NETSUITE" }, // Variant?
  { fieldId: 'netsuite_description', label: 'Description', fieldType: 'longtext', group: 'description', platform: "NETSUITE", defaultField: 'description', note: 'Default from Description [PRIMARY]' },
  { fieldId: 'netsuite_displayName', label: 'Display Name/Code', fieldType: 'text', group: 'primary', platform: "NETSUITE" }, // Variant?
  { fieldId: 'netsuite_externalId', label: 'External ID', fieldType: 'text', group: 'other', platform: "NETSUITE" }, // Variant? Defaulted?
  { fieldId: 'netsuite_custitem_fba_safety_stock', label: 'FBA Safety Stock', fieldType: 'integer', group: 'fulfillment', platform: "NETSUITE" }, // Variant?
  { fieldId: 'netsuite_custitem50', label: 'Frequently Asked Questions (FAQ)', fieldType: 'longtext', group: 'description', platform: "NETSUITE" }, // Variant?
  { fieldId: 'netsuite_custitem29', label: 'Front Loaded Launch Quantity', fieldType: 'integer', group: 'fulfillment', platform: "NETSUITE" }, // Variant?
  { fieldId: 'netsuite_fullName', label: 'Full Name', fieldType: 'text', group: 'primary', platform: "NETSUITE" }, // Variant?
  { fieldId: 'netsuite_custitem33', label: 'Handle Pantone', fieldType: 'text', group: 'color', platform: "NETSUITE" },
  { fieldId: 'netsuite_cseg_division', label: 'HJ Division', fieldType: 'text', group: 'branding', platform: "NETSUITE" }, // Correct group? Variant?
  { fieldId: 'netsuite_isInactive', label: 'Inactive', fieldType: 'checkbox', group: 'other', platform: "NETSUITE" }, // Variant? Defaulted?
  { fieldId: 'netsuite_id', label: 'Internal ID', fieldType: 'text', group: 'other', platform: "NETSUITE" }, // Variant?
  { fieldId: 'netsuite_storeDisplayImage', label: 'Item Display Image', fieldType: 'image', group: 'media', platform: "NETSUITE" },
  { fieldId: 'netsuite_storeDisplayThumbnail', label: 'Item Display Thumbnail', fieldType: 'image', group: 'media', platform: "NETSUITE" },
  { fieldId: 'netsuite_itemId', label: 'Item Name/Number', fieldType: 'text', group: 'primary', platform: "NETSUITE" },
  { fieldId: 'netsuite_lastPurchasePrice', label: 'Last Purchase Price', fieldType: 'currency', group: 'pricing', platform: "NETSUITE" },
  { fieldId: 'netsuite_lastModifiedDate', label: 'Last Modified Date', display: 'inline', fieldType: 'date', group: 'other', platform: "NETSUITE", relatedField: 'dateUpdated', note: 'Linked to dateUpdated [PRIMARY]' }, // Variant?
  { fieldId: 'netsuite_custitem9', label: 'Lid Pantone', fieldType: 'text', group: 'color', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem20', label: 'Life Status', fieldType: 'text', group: 'other', platform: "NETSUITE", defaultField: 'lifeStatus', note: 'Default from Life Status [PRIMARY]' },
  { fieldId: 'netsuite_location', label: 'Location', fieldType: 'text', group: 'fulfillment', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem_logo_pantone', label: 'Logo Pantone', fieldType: 'text', group: 'color', platform: "NETSUITE" },
  { fieldId: 'netsuite_manufacturer', label: 'Manufacturer', fieldType: 'text', group: 'other', platform: "NETSUITE" },
  { fieldId: 'netsuite_countryOfManufacture', label: 'Manufacturer Country', fieldType: 'text', group: 'other', platform: "NETSUITE" },
  { fieldId: 'netsuite_mpn', label: 'MPN', fieldType: 'text', group: 'other', platform: "NETSUITE", defaultField: 'mpn', note: 'Default from MPN [PRIMARY]'},
  { fieldId: 'netsuite_custitem12', label: 'Material Type', fieldType: 'text', group: 'description', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem22', label: 'MOQ', fieldType: 'integer', group: 'pricing', platform: "NETSUITE" },
  { fieldId: 'netsuite_vendor', label: 'Preferred Vendor', fieldType: 'text', group: 'other', platform: "NETSUITE" },
  { fieldId: 'netsuite_rate', label: 'Price', fieldType: 'currency', group: 'pricing', platform: "NETSUITE", defaultField: 'basePrice', note: 'Default from basePrice [PRIMARY]' },
  { fieldId: 'netsuite_custitem60', label: 'Product Category', fieldType: 'select', options: ['Straws', 'Sleeves', 'Bottles', 'Lids', 'Keychains', 'Boots', 'Supplements', 'Straps', 'Boxes', 'Packaging', 'Miscellaneous', 'Grates', 'Clothing', 'Bundles', 'Bags'], group: 'primary', platform: "NETSUITE" }, // Variant?
  { fieldId: 'netsuite_custitem39', label: 'Product Description', fieldType: 'text', group: 'description', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem59', label: 'Product Division', fieldType: 'text', group: 'branding', platform: "NETSUITE" }, // Correct group? Variant?
  { fieldId: 'netsuite_custitem61', label: 'Product Family', fieldType: 'select', options: ['Traveler', 'Supplement', 'Mini', 'Pro', 'Shaker', 'Sport', 'Squeeze', 'Quick Sip', 'Glass', 'Classic', 'Gallon', 'Miscellaneous', 'ACTA', 'Bottoms', 'Tops', 'Accessories', 'Ease2o', 'Can Cooler', 'Coffee'], group: 'branding', platform: "NETSUITE" }, // Correct group? Variant?
  { fieldId: 'netsuite_custitem40', label: 'Product Features', fieldType: 'text', group: 'description', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem51', label: 'Product Photo Requirements', fieldType: 'text', group: 'media', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem2', label: 'Product Specification', fieldType: 'text', group: 'description', platform: "NETSUITE", defaultField: 'productSpecification', note: 'Default from Product Specification [PRIMARY]' },
  { fieldId: 'netsuite_custitem_product_type', label: 'Product Type', fieldType: 'select', options: ['Accessory', 'Advent Calendar', 'Bag', 'Black Hat', 'Bottle Cleaning Kit', 'Bra', 'Bundle', 'Classic Poly Carrier', 'Classic Poly Jug', 'Classic Poly Lid', 'Classic Poly Sleeve', 'Clothing', 'Coffee Cup 14oz', 'Crewneck', 'Ease2o 24oz', 'Ease2o 32oz', 'Ease2o 40oz', 'Everyday Coffee Tumbler 20oz', 'Everyday Coffee Tumbler Lid 20oz', 'Everyday Tumbler 20oz', 'Everyday Tumbler Lid 20oz', 'Gallon Poly Hydrojug', 'Gallon Poly Sleeve', 'Gallon Poly Strap', 'Glass Hydrojug', 'Glass Sleeve', 'Glass Straw', 'Hat', 'Hoodie', 'Hydrojug Timetracker', 'HydroMug', 'HydroSHKR Grate', 'HydroSHKR Sleeve', 'Jacket', 'Laser Etch', 'Legging', 'Long Sleeve', 'Mini Poly HydroJug', 'Mini Poly HydroJug V2', 'Mini Poly Lid', 'Mini Poly Sleeve', 'Other', 'Outbound Packaging', 'PackAll Tote', 'Pro Jug Lid V2 73oz', 'Pro Poly Jug', 'Pro Poly Jug V2', 'Pro Poly Lid', 'Pro Poly Sleeve', 'Promo Box'], group: 'primary', platform: "NETSUITE" }, // Variant?
  { fieldId: 'netsuite_custitem34', label: 'Production Capacity', fieldType: 'integer', group: 'size', platform: "NETSUITE" },
  { fieldId: 'netsuite_purchaseDescription', label: 'Purchase Description', fieldType: 'text', group: 'description', platform: "NETSUITE" },
  { fieldId: 'netsuite_leadTime', label: 'Purchase Lead Time', fieldType: 'integer', group: 'fulfillment', platform: "NETSUITE" }, // Correct fieldType?
  { fieldId: 'netsuite_cost', label: 'Purchase Price', fieldType: 'currency', group: 'pricing', platform: "NETSUITE", defaultField: 'basePrice', note: 'Default from basePrice [PRIMARY]' },
  { fieldId: 'netsuite_custitem_reserve_inv_qty', label: 'Reserved Inventory Quantity', fieldType: 'integer', group: 'fulfillment', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem31', label: 'Reserve Inventory Quantity Reason', fieldType: 'text', group: 'fulfillment', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem27', label: 'Safety Stock', fieldType: 'integer', group: 'fulfillment', platform: "NETSUITE" },
  { fieldId: 'netsuite_safetyStockLevel', label: 'Safety Stock Level', fieldType: 'integer', group: 'fulfillment', platform: "NETSUITE" },
  { fieldId: 'netsuite_safetyStockLevelDays', label: 'Safety Stock Level Days', fieldType: 'integer', group: 'fulfillment', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem_is_scan_item', label: 'Scan at Warehouse', fieldType: 'checkbox', group: 'fulfillment', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem_wmsse_shelflife', label: 'Shelf Life in Days', fieldType: 'integer', group: 'fulfillment', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem_shopify_color_name', label: 'Shopify Variant Color Name', fieldType: 'text', group: 'color', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem32', label: 'Straw Pantone', fieldType: 'text', group: 'color', platform: "NETSUITE" },
  { fieldId: 'netsuite_taxschedule', label: 'Tax Schedule', fieldType: 'select', options: ['Taxable', 'Not Taxable'], group: 'pricing', platform: "NETSUITE" }, // Should this be a bool?
  { fieldId: 'netsuite_custitem46', label: 'Trademark Product Features', fieldType: 'text', group: 'description', platform: "NETSUITE" },
  { fieldId: 'netsuite_itemType', label: 'Type', fieldType: 'text', group: 'primary', platform: "NETSUITE" }, // Variant?
  { fieldId: 'netsuite_custitem_type_of_goods', label: 'Type of Goods', fieldType: 'text', group: 'primary', platform: "NETSUITE" }, // Variant?
  { fieldId: 'netsuite_custitem_uk_hst_codes', label: 'UK HS Tariff Code', fieldType: 'text', group: 'description', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem_un_number', label: 'UN Number', fieldType: 'text', group: 'description', platform: "NETSUITE" },
  { fieldId: 'netsuite_upcCode', label: 'UPC Code', fieldType: 'text', display: 'inline', group: 'other', platform: "NETSUITE", relatedField: 'upc', note: 'Linked to upc [PRIMARY]' },
  { fieldId: 'netsuite_custitem_hst_codes', label: 'US HS Tariff Code', fieldType: 'text', group: 'description', platform: "NETSUITE" },
  { fieldId: 'netsuite_vendorName', label: 'Vendor Name/Code', fieldType: 'text', group: 'other', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem48', label: 'Warranty Details', fieldType: 'text', group: 'description', platform: "NETSUITE" },
  { fieldId: 'netsuite_weightUnit', label: 'Weight Unit', fieldType: 'text', group: 'size', platform: "NETSUITE" },
  { fieldId: 'netsuite_weightUnits', label: 'Weight Units', fieldType: 'text', group: 'size', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem_ws_safety_stock', label: 'WS Safety Stock', fieldType: 'integer', group: 'fulfillment', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem_atlas_item_image', label: 'Item Image', fieldType: 'image', group: 'media', platform: "NETSUITE", relatedField: 'itemImage', note: 'Linked to itemImage [PRIMARY]' },
  { fieldId: 'netsuite_custitem24', label: 'Launch Date', fieldType: 'date', group: 'other', platform: "NETSUITE" },
  { fieldId: 'netsuite_custitem25', label: 'End Date', fieldType: 'date', group: 'other', platform: "NETSUITE" },

];

exports.defaultVariantFields = [
  { fieldId: 'id', label: 'ID', fieldType: 'text', display: 'inline', group: 'primary', platform: "PRIMARY" },
  { fieldId: 'dateCreated', label: 'Date Created', fieldType: 'date', display: 'inline', group: 'primary', platform: "PRIMARY" },
  { fieldId: 'hidden', label: 'Hidden', fieldType: 'checkbox', group: 'primary', platform: "PRIMARY" },
  { fieldId: 'dateUpdated', label: 'Updated last', fieldType: 'date', display: 'inline', group: 'primary', platform: "PRIMARY" },
  { fieldId: 'fieldCompletion', label: 'Field Completion', fieldType: 'percent', display: 'inline', group: 'primary', platform: "PRIMARY" },
  { fieldId: 'productId', label: 'Base Product', fieldType: 'select', display: 'inline', group: 'primary', platform: "PRIMARY" },
  { fieldId: 'sku', label: 'SKU', fieldType: 'text', mandatory: true, group: 'primary', platform: "PRIMARY" },
  { fieldId: 'upc', label: 'UPC', fieldType: 'text', mandatory: true, group: 'primary', platform: "PRIMARY" },
  { fieldId: 'mpn', label: 'MPN', fieldType: 'text', mandatory: true, group: 'primary', platform: "PRIMARY" },
  { fieldId: 'name', label: 'Name', fieldType: 'text', mandatory: true, group: 'primary', platform: "PRIMARY" },
  { fieldId: 'brand', label: 'Brand', fieldType: 'text', display: 'inline', group: 'primary', platform: "PRIMARY", relatedField: 'product.brand', note: 'Linked to product.brand [PRIMARY]' },
  { fieldId: 'description', label: 'Description', fieldType: 'longtext', mandatory: true, group: 'primary', platform: "PRIMARY" },
  { fieldId: 'productSpecification', label: 'Product Specification', fieldType: 'longtext', group: 'primary', platform: "PRIMARY" },
  { fieldId: 'basePrice', label: 'Base Price', fieldType: 'currency', mandatory: true, group: 'primary', platform: "PRIMARY" },
  {
    fieldId: 'lifeStatus', label: 'Life Status', fieldType: 'select', mandatory: true, group: 'primary', platform: "PRIMARY", options: [
      { id: 'Active', label: 'Active' },
      { id: 'Launching', label: 'Launching' },
      { id: 'Phasing Out', label: 'Phasing Out' }
    ]
  },
  { fieldId: 'itemImage', label: 'Item Image', fieldType: 'image', group: 'media', platform: "PRIMARY" },
]
  .concat(amazonVariantFields)
  .concat(tiktokVariantFields)
  .concat(shopifyVariantFields)
  .concat(netsuiteVariantFields)
  ;
// #endregion

exports.defaultVariantViews = [{
  name: 'Default View',
  filters: ['hidden', 'lifeStatus', 'productId'],
  columns: ['itemImage', 'name', 'sku', 'upc', 'productId', 'description', 'lifeStatus'],
  public: true,
  createdBy: 'default'
}];
exports.defaultProductViews = [{
  name: 'Default View',
  filters: ['hidden', 'name'],
  columns: ['name', 'netsuite_custitem4', 'netsuite_custitem5', 'netsuite_custitem6'],
  public: true,
  createdBy: 'default'
}];

exports.defaultMandatoryStages = [
  { id: 'Active', label: 'Active' },
  { id: 'Launching', label: 'Launching' },
  { id: 'Phasing Out', label: 'Phasing Out' },
];

exports.defaultPlatforms = [
  { id: 'PRIMARY', label: 'Primary' },
  { id: 'NETSUITE', label: 'NetSuite' },
  { id: 'SHOPIFY', label: 'Shopify' },
  { id: 'AMAZON', label: 'Amazon' },
  { id: 'TIKTOK', label: 'TikTok' }
];

exports.defaultFieldTypes = [
  { id: 'text', label: 'Text', bigQueryType: 'STRING' },
  { id: 'longtext', label: 'Long Text', bigQueryType: 'STRING' },
  { id: 'currency', label: 'Currency', bigQueryType: 'FLOAT64' },
  { id: 'integer', label: 'Integer', bigQueryType: 'INTEGER' },
  { id: 'decimal', label: 'Decimal', bigQueryType: 'FLOAT64' },
  { id: 'date', label: 'Date', bigQueryType: 'TIMESTAMP' },
  { id: 'url', label: 'URL', bigQueryType: 'STRING' },
  { id: 'select', label: 'Dropdown', bigQueryType: 'STRING' },
  { id: 'image', label: 'Single Image', bigQueryType: 'STRING' },
  { id: 'multiimage', label: 'Multiple Images', bigQueryType: 'ARRAY<STRING>' },
  { id: 'checkbox', label: 'Checkbox', bigQueryType: 'BOOLEAN' },
  { id: 'percent', label: 'Percentage', bigQueryType: 'FLOAT64' }
];

exports.defaultDisplayTypes = [
  { id: 'normal', label: 'Normal' },
  { id: 'disabled', label: 'Disabled' },
  { id: 'inline', label: 'Read-Only' }
];

exports.defaultStageList = [
  { id: 'Active', label: 'Active' },
  { id: 'Launching', label: 'Launching' },
  { id: 'Phasing Out', label: 'Phasing Out' },
];

exports.defaultPlatformGroups = {
  'PRIMARY': [
    { id: 'primary', label: 'Primary Information' },
    { id: 'media', label: 'Media' }, // TODO: add more primary platform groups?
  ],
  'NETSUITE': [
    { id: 'primaryInformation', label: 'Primary Information' },
    { id: 'secondaryInformation', label: 'Secondary Information' },
    { id: 'purchasingInventory', label: 'Purchasing/Inventory' },
    { id: 'salesPricing', label: 'Sales/Pricing' },
  ],
  'SHOPIFY': [
    { id: 'primaryInformation', label: 'Primary Information' },
    { id: 'secondaryInformation', label: 'Secondary Information' },
  ],
  'AMAZON': [
    { id: 'basic', label: 'Basic' },
    { id: 'variation', label: 'Variation' },
    { id: 'discovery', label: 'Discovery' },
    { id: 'productEnrichment', label: 'Product Enrichment' },
    { id: 'dimensions', label: 'Dimensions' },
    { id: 'fulfillment', label: 'Fulfillment' },
    { id: 'compliance', label: 'Compliance' },
    { id: 'offer', label: 'Offer' },
    { id: 'b2b', label: 'B2B' },
    { id: 'required', label: 'Required' },
  ],
  'TIKTOK': [
    { id: 'primaryInformation', label: 'Primary Information' },
    { id: 'secondaryInformation', label: 'Secondary Information' },
  ]
};

exports.dummyProducts = [
  { id: "1", name: "Traveler 40oz", brand: "HydroJug", hidden: false, dateCreated: new Date(), dateUpdated: new Date(), fieldCompletion: 100, images: ["https://cdn.shopify.com/s/files/1/0019/2582/9229/products/40ozBlack_1_800x.jpg?v=1618320000"] },
  { id: "2", name: "Traveler 64oz", brand: "HydroJug", hidden: false, dateCreated: new Date(), dateUpdated: new Date(), fieldCompletion: 100, images: ["https://cdn.shopify.com/s/files/1/0019/2582/9229/products/64ozBlack_1_800x.jpg?v=1618320000"] },
  { id: "3", name: "Traveler 32oz", brand: "HydroJug", hidden: false, dateCreated: new Date(), dateUpdated: new Date(), fieldCompletion: 100, images: ["https://cdn.shopify.com/s/files/1/0019/2582/9229/products/32ozBlack_1_800x.jpg?v=1618320000"] },
];

exports.dummyVariants = [
  { id: "1", name: "Black", brand: "HydroJug", sku: "40-BLK", upc: "123456789012", productId: "1", lifeStatus: "active", hidden: false, dateCreated: new Date(), dateUpdated: new Date(), fieldCompletion: 100, images: ["https://cdn.shopify.com/s/files/1/0019/2582/9229/products/40ozBlack_1_800x.jpg?v=1618320000"] },
  { id: "2", name: "Black", brand: "HydroJug", sku: "64-BLK", upc: "123456789013", productId: "2", lifeStatus: "active", hidden: false, dateCreated: new Date(), dateUpdated: new Date(), fieldCompletion: 100, images: ["https://cdn.shopify.com/s/files/1/0019/2582/9229/products/64ozBlack_1_800x.jpg?v=1618320000"] },
  { id: "3", name: "Black", brand: "HydroJug", sku: "32-BLK", upc: "123456789014", productId: "3", lifeStatus: "active", hidden: false, dateCreated: new Date(), dateUpdated: new Date(), fieldCompletion: 100, images: ["https://cdn.shopify.com/s/files/1/0019/2582/9229/products/32ozBlack_1_800x.jpg?v=1618320000"] },
  { id: "4", name: "Red", brand: "HydroJug", sku: "40-RED", upc: "123456789015", productId: "1", lifeStatus: "active", hidden: false, dateCreated: new Date(), dateUpdated: new Date(), fieldCompletion: 100, images: ["https://cdn.shopify.com/s/files/1/0019/2582/9229/products/40ozRed_1_800x.jpg?v=1618320000"] },
];