import React, { useState, useEffect } from 'react';
import { Box, Typography, Grid, Paper, CircularProgress, Alert } from '@mui/material';
import {
    Line<PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import { getFunctions, httpsCallable } from 'firebase/functions';
import KPICard from './KPICard';
import GoalStatusDisplay from './GoalStatusDisplay';
import useKPIReferenceLines from '../../hooks/useKPIReferenceLines';
import ChartExportWrapper from './ChartExportWrapper';

const OrderFulfillmentCycleTime = () => {
    const [cycleTimeData, setCycleTimeData] = useState(null);
    const [kpiGoals, setKpiGoals] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState(null);
    const [lastCompleteMonth, setLastCompleteMonth] = useState('');

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        setIsLoading(true);
        setError(null);
        try {
            const functions = getFunctions();
            const getOrderFulfillmentCycleTime = httpsCallable(functions, 'getOrderFulfillmentCycleTime');
            const getKPIGoalsForReport = httpsCallable(functions, 'getKPIGoalsForReport');

            const [cycleTimeResult, goalsResult] = await Promise.all([
                getOrderFulfillmentCycleTime(),
                getKPIGoalsForReport({ reportName: 'order-fulfillment' })
            ]);

            // Reverse the order of monthlyData to display dates in ascending order
            const reversedMonthlyData = [...cycleTimeResult.data.monthlyData].reverse();
            setCycleTimeData({ ...cycleTimeResult.data, monthlyData: reversedMonthlyData });
            setLastCompleteMonth(cycleTimeResult.data.lastCompleteMonth);

            if (goalsResult.data.success) {
                setKpiGoals(goalsResult.data.data);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            setError('Failed to fetch data. Please try again later.');
        } finally {
            setIsLoading(false);
        }
    };

    const calculateCycleTimeTrend = () => {
        if (!cycleTimeData?.monthlyData || cycleTimeData.monthlyData.length < 2) return 0;
        const lastTwo = cycleTimeData.monthlyData.slice(-2);
        return lastTwo[1].cycleTime - lastTwo[0].cycleTime;
    };

    // Calculate domains including the goal values
    const calculateDomain = () => {
        if (!cycleTimeData?.monthlyData) return [0, 10];

        const cycleTimeValues = cycleTimeData.monthlyData.map(item => item.cycleTime);

        // Include goal values in domain calculation
        const goalMin = kpiGoals?.['Order Fulfillment Cycle Time']?.min;
        const goalMax = kpiGoals?.['Order Fulfillment Cycle Time']?.max;

        const allValues = [
            ...cycleTimeValues,
            goalMin && parseFloat(goalMin),
            goalMax && parseFloat(goalMax)
        ].filter(Boolean);

        const maxValue = Math.max(...allValues);
        const minValue = Math.min(...allValues);

        return [
            Math.max(0, Math.floor(minValue * 0.9)),
            Math.ceil(maxValue * 1.1)
        ];
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            return (
                <Paper elevation={3} sx={{ p: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
                    <Typography variant="body2">{`Month: ${label}`}</Typography>
                    <Typography variant="body2" color="primary">
                        {`Average Cycle Time: ${payload[0].value.toFixed(2)} days`}
                    </Typography>
                </Paper>
            );
        }
        return null;
    };

    // Use the KPI reference lines hook
    const goalReferenceLines = useKPIReferenceLines({
        goalConfig: kpiGoals?.['Order Fulfillment Cycle Time'],
        yAxisId: "left",
        styles: {
            stroke: "#ff9800",
            strokeDasharray: "3 3",
            label: {
                fill: "#ff9800",
                fontSize: 12,
                position: "right"
            }
        },
        labelPrefix: {
            single: "Target",
            min: "Min Target",
            max: "Max Target"
        }
    });

    if (isLoading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Alert severity="error" sx={{ mt: 2 }}>
                {error}
            </Alert>
        );
    }

    if (!cycleTimeData || !kpiGoals) return null;

    const domain = calculateDomain();
    const cycleTimeConfig = kpiGoals['Order Fulfillment Cycle Time'];

    return (
        <ChartExportWrapper title="Order_Fulfillment_Cycle_Time">
            <Box sx={{ p: { xs: 2, sm: 3 }, mb: 3 }}>
                <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                    <ResponsiveContainer width="100%" height={500}>
                        <LineChart data={cycleTimeData.monthlyData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis
                                yAxisId="left"
                                domain={domain}
                                label={{ value: 'Average Cycle Time (days)', angle: -90, position: 'insideLeft' }}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            {goalReferenceLines}
                            <Line
                                yAxisId="left"
                                type="monotone"
                                dataKey="cycleTime"
                                stroke="#8884d8"
                                strokeWidth={3}
                                dot={{ r: 4, fill: "#8884d8" }}
                                activeDot={{ r: 8 }}
                                name="Average Cycle Time"
                            />
                        </LineChart>
                    </ResponsiveContainer>

                    <Grid container spacing={2} justifyContent="center" sx={{ mt: 2 }}>
                        <Grid item xs={12}>
                            <KPICard
                                title="Overall Average Cycle Time"
                                value={`${cycleTimeData.averageCycleTime.toFixed(2)} days`}
                                bgColor="#f0f4ff"
                                textColor="primary"
                            />
                        </Grid>
                    </Grid>
                </Paper>

                <GoalStatusDisplay
                    currentValue={cycleTimeData.averageCycleTime}
                    goalConfig={cycleTimeConfig}
                    showScore={true}
                    showTrend={true}
                    trendValue={calculateCycleTimeTrend()}
                    size="medium"
                    title="Order Fulfillment Cycle Time Performance"
                />
            </Box>
        </ChartExportWrapper>
    );
};

export default OrderFulfillmentCycleTime;