import React, { useState } from 'react';
import { Button, Typography, Space, Card, Row, Col } from 'antd';
import {
  ReloadOutlined,
  SyncOutlined,
  ToolOutlined,
  SettingOutlined,
  DatabaseOutlined,
  ApiOutlined
} from '@ant-design/icons';
import FixDsgLabelsModal from '../components/utilities/FixDsgLabelsModal';
const { Title } = Typography;

const Utilities = ({ userObj }) => {
  const [showFixDsgLabelPopup, setShowFixDsgLabelPopup] = useState(false);

  return (
    <div style={{ padding: '24px' }}>
      <Title level={1}>System Utilities</Title>
      <FixDsgLabelsModal show={showFixDsgLabelPopup} onClose={() => setShowFixDsgLabelPopup(false)} />
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Card title="Wholesale" bordered={false}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                type="primary"
                block
                onClick={() => setShowFixDsgLabelPopup(true)}
              >
              Fix Dsg Label
            </Button>
          </Space>
        </Card>
      </Col>
    </Row>
    </div >
  );
};

export default Utilities;
