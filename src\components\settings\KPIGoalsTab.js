import React, { useState, useEffect } from 'react';
import { Collapse, Typography, Form, Select, InputNumber, Button, Space, Alert, Spin } from 'antd';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { UndoOutlined, SaveOutlined } from '@ant-design/icons';

const { Panel } = Collapse;
const { Title, Text } = Typography;

const goalConditions = [
  { value: 'higher', label: 'Higher is better' },
  { value: 'lower', label: 'Lower is better' },
  { value: 'inRange', label: 'Keep in range' },
  { value: 'higherThanMin', label: 'Should exceed minimum' },
  { value: 'lowerThanMax', label: 'Should be below maximum' }
];

const KPIGoalsTab = () => {
  const [form] = Form.useForm();
  const [kpiGoals, setKpiGoals] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  const functions = getFunctions();
  const getKPIGoalsFn = httpsCallable(functions, 'getKPIGoals');
  const updateKPIGoalsFn = httpsCallable(functions, 'updateKPIGoals');
  const resetKPIGoalsFn = httpsCallable(functions, 'resetKPIGoals');

  useEffect(() => {
    fetchKPIGoals();
  }, []);

  const fetchKPIGoals = async () => {
    try {
      const result = await getKPIGoalsFn();
      setKpiGoals(result.data);
      form.setFieldsValue(result.data);
      setError(null);
    } catch (err) {
      setError('Failed to load KPI goals. Please try again.');
      console.error('Error fetching KPI goals:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      const values = await form.validateFields();
      await updateKPIGoalsFn({ kpiGoals: values });
      setSuccess('KPI goals saved successfully');
      setError(null);
    } catch (err) {
      setError('Failed to save KPI goals. Please try again.');
      console.error('Error saving KPI goals:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleReset = async () => {
    try {
      setSaving(true);
      const result = await resetKPIGoalsFn();
      setKpiGoals(result.data.data);
      form.setFieldsValue(result.data.data);
      setSuccess('KPI goals reset to defaults successfully');
      setError(null);
    } catch (err) {
      setError('Failed to reset KPI goals. Please try again.');
      console.error('Error resetting KPI goals:', err);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <Spin size="large" />;
  }

  return (
    <div style={{ padding: '24px' }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
          <Title level={3}>KPI Goals</Title>
          <Space>
            <Button
              icon={<UndoOutlined />}
              onClick={handleReset}
              loading={saving}
            >
              Reset to Default
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={saving}
            >
              Save Changes
            </Button>
          </Space>
        </div>

        {error && <Alert message={error} type="error" showIcon style={{ marginBottom: 16 }} />}
        {success && <Alert message={success} type="success" showIcon style={{ marginBottom: 16 }} closable onClose={() => setSuccess(null)} />}

        <Form form={form} layout="vertical">
          <Collapse>
            {Object.entries(kpiGoals || {}).map(([category, kpis]) => (
              <Panel header={category} key={category}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  {Object.entries(kpis).map(([kpiName, kpiData]) => {
                    const hasRange = kpiData.hasOwnProperty('min') && kpiData.hasOwnProperty('max');
                    
                    return (
                      <div key={kpiName} style={{ display: 'flex', gap: 16, alignItems: 'flex-start' }}>
                        <Text style={{ width: 300 }}>{kpiName}</Text>
                        <Form.Item
                          name={[category, kpiName, 'goalCondition']}
                          style={{ width: 200, margin: 0 }}
                        >
                          <Select options={goalConditions} />
                        </Form.Item>
                        {hasRange ? (
                          <Space>
                            <Form.Item
                              name={[category, kpiName, 'min']}
                              style={{ margin: 0 }}
                            >
                              <InputNumber placeholder="Min" style={{ width: 100 }} />
                            </Form.Item>
                            <Text>-</Text>
                            <Form.Item
                              name={[category, kpiName, 'max']}
                              style={{ margin: 0 }}
                            >
                              <InputNumber placeholder="Max" style={{ width: 100 }} />
                            </Form.Item>
                          </Space>
                        ) : (
                          <Form.Item
                            name={[category, kpiName, 'value']}
                            style={{ margin: 0 }}
                          >
                            <InputNumber style={{ width: 100 }} />
                          </Form.Item>
                        )}
                        <Text>{kpiData.unit}</Text>
                      </div>
                    );
                  })}
                </Space>
              </Panel>
            ))}
          </Collapse>
        </Form>
      </Space>
    </div>
  );
};

export default KPIGoalsTab;