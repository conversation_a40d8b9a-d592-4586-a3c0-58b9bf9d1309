/* eslint-disable guard-for-in */
// const Shopify = require("shopify-api-node");
const axios = require("axios");
const {flattenListOfObjects} = require("./constants");
const makeGraphQLRequest = async ({query, storeName, apiKey, variables = {}, operationName = undefined, cursor = ""}) => {
  if (cursor) {
    variables.cursor = cursor;
  }
  const data = {
    operationName,
    query,
    variables,
  };

  const config = {
    method: "post",
    url: `https://${storeName.replace(".myshopify.com", "")}.myshopify.com/admin/api/unstable/graphql.json`,
    headers: {
      "X-Shopify-Access-Token": apiKey,
      "Content-Type": "application/json",
    },
    data,
  };
  let req;
  try {
    req = await axios(config);
    req = req.data;
  } catch (el) {
    if (el.message.includes("A bulk query operation for this app and shop is already in progress")) {
      console.log(`Bulk query operation in progress for ${storeName}`);
    } else {
      console.error(`Error with axios req`, el);
      throw new Error(`Error with axios req: ${el}`);
    }
  }
  return req;
};
const makeBulkQueryRequest = async ({storeName, apiKey, bulkOperationQuery}) => {
  const reqObj = {
    query: bulkOperationQuery,
    storeName,
    apiKey,
  };

  const resp = await makeGraphQLRequest(reqObj);

  if (resp.errors || resp.data.bulkOperationRunQuery.userErrors.length > 0) {
    throw new Error(
        `Error with bulk operation: ${JSON.stringify(
            resp.errors || resp.data.bulkOperationRunQuery.userErrors,
        )}`,
    );
  }

  const bulkOperationId = resp.data.bulkOperationRunQuery.bulkOperation.id;
  console.log(`Bulk operation initiated with ID: ${bulkOperationId}`);

  // Poll for operation status
  const statusCheckQuery = `
    {
      currentBulkOperation {
        id
        status
        url
        errorCode
        partialDataUrl
        type
        objectCount
        fileSize
      }
    }
  `;
  let operationComplete = false;
  let bulkOperationStatus;

  do {
    await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait for 2 seconds before checking status
    bulkOperationStatus = await makeGraphQLRequest({
      query: statusCheckQuery,
      storeName,
      apiKey,
    });

    const {currentBulkOperation} = bulkOperationStatus.data;
    operationComplete =
      currentBulkOperation.status === "COMPLETED" ||
      currentBulkOperation.status === "FAILED";

    if (currentBulkOperation.status === "FAILED") {
      throw new Error(
          `Bulk operation failed with error: ${currentBulkOperation.errorCode}`,
      );
    }
  } while (!operationComplete);

  // Download the results
  const resultUrl = bulkOperationStatus.data.currentBulkOperation.url;
  if (!resultUrl) {
    console.error("No result URL found", JSON.stringify(bulkOperationStatus));
    throw new Error("No result URL found " + JSON.stringify(bulkOperationStatus));
  }
  const ordersResp = await axios.get(resultUrl);
  const orderText = ordersResp.data;
  // const orderText = await ordersData.text();
  // console.log("ordersData", orderText);
  let orderLines;
  let data = [];
  if (orderText instanceof Object) { 
    data = [orderText];
  } else {
    orderLines = orderText.split("\n");
    orderLines.pop(); // Remove the last empty line
    data = orderLines.map((line) => JSON.parse(line));
  }
  return data;
};
const getShopifyOrders = async ({storeName, apiKey, startDate, maxDate, node}) => {
  if (!startDate) {
    startDate = new Date();
    startDate.setHours(startDate.getHours() - 12);
    startDate = startDate.toISOString();
  }
  if (!maxDate) {
    maxDate = new Date(startDate);
    maxDate.setHours(maxDate.getHours() + 12);
    maxDate = new Date(Math.min(maxDate.getTime(), new Date().getTime()));
    maxDate = maxDate.toISOString();
  }
  console.log("dates", {startDate, maxDate});

  if(!node) {
    node = `
      id
      name
      note
      legacyResourceId
      sourceName
      createdAt
      customer{
        id
        email
        firstName
        lastName
      }
      originalTotalPriceSet{
        shopMoney{
          amount
        }
      }
      poNumber
      processedAt
      risk{
        recommendation
      }
      sourceIdentifier
      sourceName
      displayFulfillmentStatus
      displayFinancialStatus
      discountCodes
      tags
      shippingLine{
        title
        price
      }
      shippingAddress {
        province
      }
      app{
        name
      }
      closedAt
      currencyCode
      totalDiscountsSet{
        shopMoney{
          amount
        }
      }
      totalOutstandingSet{
        shopMoney{
          amount
        }
      }
      totalPriceSet{
        shopMoney{
          amount
        }
      }
      totalRefundedSet{
        shopMoney{
          amount
        }
      }
      totalShippingPriceSet{
        shopMoney{
          amount
        }
      }
      totalTaxSet{
        shopMoney{
          amount
        }
      }
    `;
  }

  const bulkOperationQuery = `
    mutation {
      bulkOperationRunQuery(
        query: """
        {
          orders(query: "created_at:>'${startDate}' AND created_at:<='${maxDate}' ") {
            edges {
              node {
                ${node}
              }
            }
          }
        }
        """
      ) {
        bulkOperation {
          id
          status
        }
        userErrors {
          field
          message
        }
      }
    }
  `;
  let orders = await makeBulkQueryRequest({storeName, apiKey, bulkOperationQuery});
  orders = flattenListOfObjects(orders);
  orders = orders.map((order) => {
    for (const key in order) {
      if (order[key] instanceof Array) {
        order[key] = order[key].join(",");
      }
      const floatKeys = ["price", "amount"];
      if (floatKeys.some((floatKey) => key.includes(floatKey))) {
        order[key] = parseFloat(order[key]);
      }
      order.store = storeName;
    }

    return order;
  });
  return {orders, maxDate};
};

const getShopifyDiscounts = async ({storeName, apiKey, startDate, maxDate, node}) => {
  if (!startDate) {
    startDate = new Date();
    startDate.setHours(startDate.getHours() - 12);
    startDate = startDate.toISOString();
  }
  if (!maxDate) {
    maxDate = new Date(startDate);
    maxDate.setHours(maxDate.getHours() + 12);
    maxDate = new Date(Math.min(maxDate.getTime(), new Date().getTime()));
    maxDate = maxDate.toISOString();
  }
  console.log("dates", {startDate, maxDate});

  if(!node) {
    node = `
      id
      discount {
        __typename
        ... on DiscountCodeBasic {
          title
          summary
          status
          createdAt
          updatedAt
          startsAt
          endsAt
          codesCount {
            count
          }
          totalSales {
            amount
          }
          combinesWith {
            productDiscounts
          }
          codes {
            edges {
              node {
                id
                code
              }
            }
          }
        }
        ... on DiscountCodeFreeShipping {
          title
          summary
          status
          createdAt
          updatedAt
          startsAt
          endsAt
          codesCount {
            count
          }
          totalSales {
            amount
          }
          combinesWith {
            productDiscounts
          }
          codes {
            edges {
              node {
                id
                code
              }
            }
          }
        }
      }
    `;
  }

  const bulkOperationQuery = `
    mutation {
      bulkOperationRunQuery(
        query: """
        {
          discountNodes(query: "created_at:>'${startDate}' AND created_at:<='${maxDate}' ") {
            edges {
              node {
                ${node}
              }
            }
          }
        }
        """
      ) {
        bulkOperation {
          id
          status
        }
        userErrors {
          field
          message
        }
      }
    }
  `;
  let discounts = await makeBulkQueryRequest({storeName, apiKey, bulkOperationQuery});
  discounts = flattenListOfObjects(discounts);
  discounts = discounts.map((discount) => {
    return discount;
  });
  return {discounts, maxDate};
};

module.exports = {
  makeGraphQLRequest,
  makeBulkQueryRequest,
  getShopifyOrders,
  getShopifyDiscounts,
};
